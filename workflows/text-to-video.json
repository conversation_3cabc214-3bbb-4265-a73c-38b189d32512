{"name": "Text to Video Workflow", "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"httpMethod": "POST", "path": "text-to-video", "options": {}}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"functionCode": "// Extract text content from the incoming webhook\nconst textContent = $input.item.json.text || '';\nconst title = $input.item.json.title || 'Untitled';\nconst userId = $input.item.json.userId;\n\nif (!textContent) {\n  throw new Error('No text content provided');\n}\n\nif (!userId) {\n  throw new Error('No user ID provided');\n}\n\nreturn {\n  json: {\n    userId,\n    title,\n    textContent,\n    timestamp: new Date().toISOString(),\n    processId: `proc_${Date.now()}`\n  }\n};"}, "name": "Extract Text", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [650, 300]}, {"parameters": {"functionCode": "// Generate a script from the text content\nconst { textContent, title, userId, processId } = $input.item.json;\n\n// In a real scenario, this might call an AI service\n// For this template, we'll create a simple script\nconst script = `# ${title}\\n\\n${textContent.split('.').join('.\\n\\n')}`;\n\nreturn {\n  json: {\n    ...$input.item.json,\n    script,\n    scriptGenerated: true\n  }\n};"}, "name": "Generate Script", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [850, 300]}, {"parameters": {"functionCode": "// Simulate video creation\n// In a real scenario, this would call a video generation service\nconst { script, title, userId, processId } = $input.item.json;\n\nreturn {\n  json: {\n    ...$input.item.json,\n    videoUrl: `https://example.com/videos/${processId}.mp4`,\n    thumbnailUrl: `https://example.com/thumbnails/${processId}.jpg`,\n    duration: Math.floor(Math.random() * 180) + 60, // Random duration between 60-240 seconds\n    videoCreated: true,\n    completedAt: new Date().toISOString()\n  }\n};"}, "name": "Create Video", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"url": "=https://example.com/api/content/{{$json[\"processId\"]}}", "options": {"jsonParameters": true}, "bodyParametersJson": "={{ JSON.stringify($json) }}"}, "name": "Update Content Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1250, 300]}], "connections": {"Start": {"main": [[{"node": "Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Extract Text", "type": "main", "index": 0}]]}, "Extract Text": {"main": [[{"node": "Generate Script", "type": "main", "index": 0}]]}, "Generate Script": {"main": [[{"node": "Create Video", "type": "main", "index": 0}]]}, "Create Video": {"main": [[{"node": "Update Content Status", "type": "main", "index": 0}]]}}}