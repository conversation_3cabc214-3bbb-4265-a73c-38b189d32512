{"name": "Blog to Social Media Workflow", "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"httpMethod": "POST", "path": "blog-to-social", "options": {}}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"functionCode": "// Extract blog content from the incoming webhook\nconst blogContent = $input.item.json.text || '';\nconst title = $input.item.json.title || 'Untitled';\nconst userId = $input.item.json.userId;\nconst platforms = $input.item.json.platforms || ['twitter', 'linkedin', 'facebook'];\n\nif (!blogContent) {\n  throw new Error('No blog content provided');\n}\n\nif (!userId) {\n  throw new Error('No user ID provided');\n}\n\nreturn {\n  json: {\n    userId,\n    title,\n    blogContent,\n    platforms,\n    timestamp: new Date().toISOString(),\n    processId: `proc_${Date.now()}`\n  }\n};"}, "name": "Extract Blog Content", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [650, 300]}, {"parameters": {"functionCode": "// Generate summaries for different platforms\nconst { blogContent, title, userId, platforms, processId } = $input.item.json;\n\n// In a real scenario, this might call an AI service\n// For this template, we'll create simple summaries\nconst summaries = {};\n\nif (platforms.includes('twitter')) {\n  // Twitter/X has a 280 character limit\n  summaries.twitter = `${title}: ${blogContent.substring(0, 240)}... Read more: https://example.com/blog/${processId}`;\n}\n\nif (platforms.includes('linkedin')) {\n  // LinkedIn allows longer posts\n  summaries.linkedin = `# ${title}\\n\\n${blogContent.substring(0, 500)}...\\n\\nRead the full article: https://example.com/blog/${processId}`;\n}\n\nif (platforms.includes('facebook')) {\n  // Facebook also allows longer posts\n  summaries.facebook = `${title}\\n\\n${blogContent.substring(0, 400)}...\\n\\nRead more: https://example.com/blog/${processId}`;\n}\n\nreturn {\n  json: {\n    ...$input.item.json,\n    summaries,\n    summariesGenerated: true\n  }\n};"}, "name": "Generate Summaries", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [850, 300]}, {"parameters": {"functionCode": "// Simulate image creation for social media\n// In a real scenario, this would call an image generation service\nconst { title, userId, processId, platforms } = $input.item.json;\n\nconst images = {};\n\nplatforms.forEach(platform => {\n  // Different sizes for different platforms\n  switch(platform) {\n    case 'twitter':\n      images[platform] = `https://example.com/images/${processId}_twitter.jpg`;\n      break;\n    case 'linkedin':\n      images[platform] = `https://example.com/images/${processId}_linkedin.jpg`;\n      break;\n    case 'facebook':\n      images[platform] = `https://example.com/images/${processId}_facebook.jpg`;\n      break;\n    default:\n      images[platform] = `https://example.com/images/${processId}_default.jpg`;\n  }\n});\n\nreturn {\n  json: {\n    ...$input.item.json,\n    images,\n    imagesCreated: true,\n    completedAt: new Date().toISOString()\n  }\n};"}, "name": "Create Images", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"url": "=https://example.com/api/content/{{$json[\"processId\"]}}", "options": {"jsonParameters": true}, "bodyParametersJson": "={{ JSON.stringify($json) }}"}, "name": "Update Content Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1250, 300]}], "connections": {"Start": {"main": [[{"node": "Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Extract Blog Content", "type": "main", "index": 0}]]}, "Extract Blog Content": {"main": [[{"node": "Generate Summaries", "type": "main", "index": 0}]]}, "Generate Summaries": {"main": [[{"node": "Create Images", "type": "main", "index": 0}]]}, "Create Images": {"main": [[{"node": "Update Content Status", "type": "main", "index": 0}]]}}}