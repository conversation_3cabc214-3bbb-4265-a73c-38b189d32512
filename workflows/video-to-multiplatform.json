{"name": "Video to Multi-Platform Workflow", "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"httpMethod": "POST", "path": "video-to-multiplatform", "options": {}}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"functionCode": "// Extract video data from the incoming webhook\nconst videoUrl = $input.item.json.videoUrl || '';\nconst title = $input.item.json.title || 'Untitled Video';\nconst description = $input.item.json.description || '';\nconst userId = $input.item.json.userId;\nconst contentId = $input.item.json.contentId;\nconst platforms = $input.item.json.platforms || ['youtube', 'instagram', 'tiktok', 'linkedin'];\n\nif (!videoUrl) {\n  throw new Error('No video URL provided');\n}\n\nif (!userId) {\n  throw new Error('No user ID provided');\n}\n\nreturn {\n  json: {\n    userId,\n    contentId,\n    videoUrl,\n    title,\n    description,\n    platforms,\n    timestamp: new Date().toISOString(),\n    processId: `video_multi_${Date.now()}`\n  }\n};"}, "name": "Extract Video Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [650, 300]}, {"parameters": {"functionCode": "// Analyze video content and metadata\nconst { videoUrl, title, description, userId, contentId, platforms, processId } = $input.item.json;\n\n// Simulate video analysis (in real scenario, would use video processing APIs)\nconst videoAnalysis = {\n  duration: 120, // 2 minutes\n  resolution: { width: 1920, height: 1080 },\n  format: 'mp4',\n  fileSize: '50MB',\n  aspectRatio: '16:9',\n  frameRate: 30,\n  audioTrack: true,\n  subtitles: false,\n  thumbnail: `${videoUrl.replace('.mp4', '_thumb.jpg')}`,\n  scenes: [\n    { start: 0, end: 30, description: 'Introduction' },\n    { start: 30, end: 90, description: 'Main content' },\n    { start: 90, end: 120, description: 'Conclusion' }\n  ]\n};\n\n// Generate tags based on title and description\nconst tags = [\n  'video',\n  'content',\n  'marketing',\n  'digital',\n  'social'\n];\n\nreturn {\n  json: {\n    ...($input.item.json),\n    videoAnalysis,\n    tags,\n    analyzed: true\n  }\n};"}, "name": "Analyze Video", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [850, 300]}, {"parameters": {"functionCode": "// Create platform-specific video versions\nconst { videoUrl, videoAnalysis, title, description, platforms, userId, contentId, processId } = $input.item.json;\n\nconst platformVideos = {};\n\nplatforms.forEach(platform => {\n  switch(platform) {\n    case 'youtube':\n      platformVideos[platform] = {\n        url: videoUrl, // Original video for YouTube\n        format: 'mp4',\n        resolution: videoAnalysis.resolution,\n        maxDuration: 43200, // 12 hours\n        maxFileSize: '128GB',\n        aspectRatio: '16:9',\n        requirements: ['thumbnail', 'title', 'description', 'tags']\n      };\n      break;\n    case 'instagram':\n      // Instagram has different requirements for feed vs reels\n      platformVideos[platform] = {\n        feed: {\n          url: videoUrl, // Would be cropped to 1:1 in real scenario\n          format: 'mp4',\n          resolution: { width: 1080, height: 1080 },\n          maxDuration: 60,\n          aspectRatio: '1:1'\n        },\n        reels: {\n          url: videoUrl, // Would be cropped to 9:16 in real scenario\n          format: 'mp4',\n          resolution: { width: 1080, height: 1920 },\n          maxDuration: 90,\n          aspectRatio: '9:16'\n        }\n      };\n      break;\n    case 'tiktok':\n      platformVideos[platform] = {\n        url: videoUrl, // Would be cropped to 9:16 in real scenario\n        format: 'mp4',\n        resolution: { width: 1080, height: 1920 },\n        maxDuration: 180, // 3 minutes\n        aspectRatio: '9:16',\n        requirements: ['vertical_video', 'engaging_start']\n      };\n      break;\n    case 'linkedin':\n      platformVideos[platform] = {\n        url: videoUrl,\n        format: 'mp4',\n        resolution: videoAnalysis.resolution,\n        maxDuration: 600, // 10 minutes\n        aspectRatio: '16:9',\n        requirements: ['professional_content', 'captions']\n      };\n      break;\n    case 'twitter':\n      platformVideos[platform] = {\n        url: videoUrl,\n        format: 'mp4',\n        resolution: { width: 1280, height: 720 },\n        maxDuration: 140, // 2 minutes 20 seconds\n        maxFileSize: '512MB',\n        aspectRatio: '16:9'\n      };\n      break;\n  }\n});\n\nreturn {\n  json: {\n    ...($input.item.json),\n    platformVideos,\n    optimized: true\n  }\n};"}, "name": "Optimize for Platforms", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"functionCode": "// Generate platform-specific metadata and descriptions\nconst { title, description, tags, platformVideos, userId, contentId, processId } = $input.item.json;\n\nconst platformContent = {};\n\nObject.keys(platformVideos).forEach(platform => {\n  switch(platform) {\n    case 'youtube':\n      platformContent[platform] = {\n        title: title.length > 100 ? title.substring(0, 97) + '...' : title,\n        description: `${description}\\n\\n🔔 Subscribe for more content!\\n\\n#${tags.join(' #')}`,\n        tags: tags,\n        thumbnail: platformVideos[platform].thumbnail || '',\n        category: 'Education',\n        privacy: 'public',\n        video: platformVideos[platform]\n      };\n      break;\n    case 'instagram':\n      platformContent[platform] = {\n        feed: {\n          caption: `${title}\\n\\n${description.substring(0, 200)}...\\n\\n#${tags.join(' #')}`,\n          video: platformVideos[platform].feed\n        },\n        reels: {\n          caption: `${title}\\n\\n#${tags.join(' #')} #reels #viral`,\n          video: platformVideos[platform].reels\n        }\n      };\n      break;\n    case 'tiktok':\n      platformContent[platform] = {\n        caption: `${title} #${tags.join(' #')} #fyp #viral`,\n        video: platformVideos[platform],\n        effects: ['trending_sound', 'hashtag_challenge']\n      };\n      break;\n    case 'linkedin':\n      platformContent[platform] = {\n        title: title,\n        description: `${description}\\n\\nWhat are your thoughts on this topic? Share in the comments below!\\n\\n#${tags.join(' #')}`,\n        video: platformVideos[platform],\n        audience: 'professional'\n      };\n      break;\n    case 'twitter':\n      const shortDesc = description.length > 200 ? description.substring(0, 197) + '...' : description;\n      platformContent[platform] = {\n        text: `🎥 ${title}\\n\\n${shortDesc}\\n\\n#${tags.slice(0, 3).join(' #')}`,\n        video: platformVideos[platform]\n      };\n      break;\n  }\n});\n\nreturn {\n  json: {\n    ...($input.item.json),\n    platformContent,\n    contentGenerated: true\n  }\n};"}, "name": "Generate Platform Content", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1250, 300]}, {"parameters": {"functionCode": "// Create publishing schedule for optimal engagement\nconst { platformContent, userId, contentId, processId } = $input.item.json;\n\n// Optimal posting times for different platforms (simplified)\nconst optimalTimes = {\n  youtube: { day: 1, hour: 14, minute: 0 }, // Tuesday 2 PM\n  instagram: { day: 1, hour: 11, minute: 0 }, // Tuesday 11 AM\n  tiktok: { day: 2, hour: 18, minute: 0 }, // Wednesday 6 PM\n  linkedin: { day: 3, hour: 8, minute: 0 }, // Thursday 8 AM\n  twitter: { day: 1, hour: 9, minute: 0 } // Tuesday 9 AM\n};\n\nconst publishingSchedule = {};\nconst now = new Date();\n\nObject.keys(platformContent).forEach(platform => {\n  const optimalTime = optimalTimes[platform];\n  const scheduleDate = new Date(now);\n  \n  // Calculate next occurrence of the optimal day\n  const daysUntilOptimal = (optimalTime.day - scheduleDate.getDay() + 7) % 7;\n  scheduleDate.setDate(scheduleDate.getDate() + (daysUntilOptimal || 7));\n  scheduleDate.setHours(optimalTime.hour, optimalTime.minute, 0, 0);\n  \n  publishingSchedule[platform] = {\n    content: platformContent[platform],\n    scheduledFor: scheduleDate.toISOString(),\n    status: 'scheduled',\n    platform: platform,\n    estimatedReach: Math.floor(Math.random() * 10000) + 1000 // Simulated reach estimate\n  };\n});\n\nreturn {\n  json: {\n    ...($input.item.json),\n    publishingSchedule,\n    scheduled: true,\n    completedAt: new Date().toISOString()\n  }\n};"}, "name": "Schedule Publishing", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1450, 300]}, {"parameters": {"url": "=http://backend:4000/api/content/{{$json[\"contentId\"]}}/multiplatform-result", "options": {"jsonParameters": true}, "bodyParametersJson": "={{ JSON.stringify($json) }}"}, "name": "Save Publishing Plan", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1650, 300]}], "connections": {"Start": {"main": [[{"node": "Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Extract Video Data", "type": "main", "index": 0}]]}, "Extract Video Data": {"main": [[{"node": "Analyze Video", "type": "main", "index": 0}]]}, "Analyze Video": {"main": [[{"node": "Optimize for Platforms", "type": "main", "index": 0}]]}, "Optimize for Platforms": {"main": [[{"node": "Generate Platform Content", "type": "main", "index": 0}]]}, "Generate Platform Content": {"main": [[{"node": "Schedule Publishing", "type": "main", "index": 0}]]}, "Schedule Publishing": {"main": [[{"node": "Save Publishing Plan", "type": "main", "index": 0}]]}}}