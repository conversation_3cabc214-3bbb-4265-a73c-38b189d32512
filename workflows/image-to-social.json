{"name": "Image to Social Media Workflow", "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"httpMethod": "POST", "path": "image-to-social", "options": {}}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"functionCode": "// Extract image data from the incoming webhook\nconst imageUrl = $input.item.json.imageUrl || '';\nconst caption = $input.item.json.caption || '';\nconst userId = $input.item.json.userId;\nconst contentId = $input.item.json.contentId;\nconst platforms = $input.item.json.platforms || ['instagram', 'twitter', 'facebook'];\n\nif (!imageUrl) {\n  throw new Error('No image URL provided');\n}\n\nif (!userId) {\n  throw new Error('No user ID provided');\n}\n\nreturn {\n  json: {\n    userId,\n    contentId,\n    imageUrl,\n    caption,\n    platforms,\n    timestamp: new Date().toISOString(),\n    processId: `img_social_${Date.now()}`\n  }\n};"}, "name": "Extract Image Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [650, 300]}, {"parameters": {"functionCode": "// Analyze image and generate metadata\nconst { imageUrl, caption, userId, contentId, platforms, processId } = $input.item.json;\n\n// Simulate image analysis (in real scenario, this would call an AI vision service)\nconst imageAnalysis = {\n  dimensions: { width: 1080, height: 1080 },\n  format: 'jpeg',\n  colors: ['#FF6B6B', '#4ECDC4', '#45B7D1'],\n  objects: ['person', 'background', 'text'],\n  mood: 'professional',\n  quality: 'high'\n};\n\n// Generate hashtags based on image analysis and caption\nconst hashtags = [\n  '#contentcreation',\n  '#socialmedia',\n  '#digitalmarketing',\n  '#branding',\n  '#creative'\n];\n\nreturn {\n  json: {\n    ...($input.item.json),\n    imageAnalysis,\n    hashtags,\n    analyzed: true\n  }\n};"}, "name": "Analyze Image", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [850, 300]}, {"parameters": {"functionCode": "// Optimize image for different platforms\nconst { imageUrl, imageAnalysis, caption, hashtags, platforms, userId, contentId, processId } = $input.item.json;\n\n// Platform-specific image optimizations\nconst optimizedImages = {};\n\nplatforms.forEach(platform => {\n  switch(platform) {\n    case 'instagram':\n      optimizedImages[platform] = {\n        url: imageUrl, // In real scenario, would resize to 1080x1080\n        dimensions: { width: 1080, height: 1080 },\n        format: 'jpeg',\n        quality: 85\n      };\n      break;\n    case 'twitter':\n      optimizedImages[platform] = {\n        url: imageUrl, // In real scenario, would resize to 1200x675\n        dimensions: { width: 1200, height: 675 },\n        format: 'jpeg',\n        quality: 80\n      };\n      break;\n    case 'facebook':\n      optimizedImages[platform] = {\n        url: imageUrl, // In real scenario, would resize to 1200x630\n        dimensions: { width: 1200, height: 630 },\n        format: 'jpeg',\n        quality: 85\n      };\n      break;\n    case 'linkedin':\n      optimizedImages[platform] = {\n        url: imageUrl, // In real scenario, would resize to 1200x627\n        dimensions: { width: 1200, height: 627 },\n        format: 'jpeg',\n        quality: 85\n      };\n      break;\n  }\n});\n\nreturn {\n  json: {\n    ...($input.item.json),\n    optimizedImages,\n    optimized: true\n  }\n};"}, "name": "Optimize Images", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"functionCode": "// Generate platform-specific captions\nconst { caption, hashtags, platforms, optimizedImages, userId, contentId, processId } = $input.item.json;\n\nconst platformPosts = {};\n\nplatforms.forEach(platform => {\n  switch(platform) {\n    case 'instagram':\n      platformPosts[platform] = {\n        caption: `${caption}\\n\\n${hashtags.join(' ')}`,\n        image: optimizedImages[platform],\n        maxLength: 2200,\n        features: ['stories', 'reels', 'feed']\n      };\n      break;\n    case 'twitter':\n      // Twitter has character limit, so shorter caption\n      const shortCaption = caption.length > 200 ? caption.substring(0, 197) + '...' : caption;\n      platformPosts[platform] = {\n        caption: `${shortCaption}\\n\\n${hashtags.slice(0, 3).join(' ')}`,\n        image: optimizedImages[platform],\n        maxLength: 280,\n        features: ['tweet', 'thread']\n      };\n      break;\n    case 'facebook':\n      platformPosts[platform] = {\n        caption: `${caption}\\n\\n${hashtags.join(' ')}`,\n        image: optimizedImages[platform],\n        maxLength: 63206,\n        features: ['post', 'story']\n      };\n      break;\n    case 'linkedin':\n      platformPosts[platform] = {\n        caption: `${caption}\\n\\n${hashtags.join(' ')}`,\n        image: optimizedImages[platform],\n        maxLength: 3000,\n        features: ['post', 'article']\n      };\n      break;\n  }\n});\n\nreturn {\n  json: {\n    ...($input.item.json),\n    platformPosts,\n    postsGenerated: true\n  }\n};"}, "name": "Generate Platform Posts", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1250, 300]}, {"parameters": {"functionCode": "// Schedule posts for optimal times\nconst { platformPosts, userId, contentId, processId } = $input.item.json;\n\n// Optimal posting times (simplified - in real scenario, would be user-specific)\nconst optimalTimes = {\n  instagram: { hour: 11, minute: 0 }, // 11:00 AM\n  twitter: { hour: 9, minute: 0 },    // 9:00 AM\n  facebook: { hour: 15, minute: 0 },  // 3:00 PM\n  linkedin: { hour: 8, minute: 0 }    // 8:00 AM\n};\n\nconst scheduledPosts = {};\nconst now = new Date();\n\nObject.keys(platformPosts).forEach(platform => {\n  const optimalTime = optimalTimes[platform];\n  const scheduleDate = new Date(now);\n  \n  // Schedule for next day at optimal time\n  scheduleDate.setDate(scheduleDate.getDate() + 1);\n  scheduleDate.setHours(optimalTime.hour, optimalTime.minute, 0, 0);\n  \n  scheduledPosts[platform] = {\n    ...platformPosts[platform],\n    scheduledFor: scheduleDate.toISOString(),\n    status: 'scheduled'\n  };\n});\n\nreturn {\n  json: {\n    ...($input.item.json),\n    scheduledPosts,\n    scheduled: true,\n    completedAt: new Date().toISOString()\n  }\n};"}, "name": "Schedule Posts", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1450, 300]}, {"parameters": {"url": "=http://backend:4000/api/content/{{$json[\"contentId\"]}}/social-result", "options": {"jsonParameters": true}, "bodyParametersJson": "={{ JSON.stringify($json) }}"}, "name": "Save Social Posts", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1650, 300]}], "connections": {"Start": {"main": [[{"node": "Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Extract Image Data", "type": "main", "index": 0}]]}, "Extract Image Data": {"main": [[{"node": "Analyze Image", "type": "main", "index": 0}]]}, "Analyze Image": {"main": [[{"node": "Optimize Images", "type": "main", "index": 0}]]}, "Optimize Images": {"main": [[{"node": "Generate Platform Posts", "type": "main", "index": 0}]]}, "Generate Platform Posts": {"main": [[{"node": "Schedule Posts", "type": "main", "index": 0}]]}, "Schedule Posts": {"main": [[{"node": "Save Social Posts", "type": "main", "index": 0}]]}}}