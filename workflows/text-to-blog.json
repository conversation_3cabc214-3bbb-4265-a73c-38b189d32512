{"name": "Text to Blog Post Workflow", "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"httpMethod": "POST", "path": "text-to-blog", "options": {}}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"functionCode": "// Extract text content from the incoming webhook\nconst textContent = $input.item.json.text || '';\nconst title = $input.item.json.title || 'Untitled Blog Post';\nconst userId = $input.item.json.userId;\nconst contentId = $input.item.json.contentId;\n\nif (!textContent) {\n  throw new Error('No text content provided');\n}\n\nif (!userId) {\n  throw new Error('No user ID provided');\n}\n\nreturn {\n  json: {\n    userId,\n    contentId,\n    title,\n    textContent,\n    timestamp: new Date().toISOString(),\n    processId: `blog_${Date.now()}`\n  }\n};"}, "name": "Extract Content", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [650, 300]}, {"parameters": {"functionCode": "// Analyze content and generate blog structure\nconst { textContent, title, userId, contentId, processId } = $input.item.json;\n\n// Split content into paragraphs\nconst paragraphs = textContent.split('\\n').filter(p => p.trim().length > 0);\n\n// Generate blog metadata\nconst wordCount = textContent.split(' ').length;\nconst readingTime = Math.ceil(wordCount / 200); // Average reading speed\n\n// Generate tags based on content (simplified)\nconst commonWords = textContent.toLowerCase().match(/\\b\\w{4,}\\b/g) || [];\nconst wordFreq = {};\ncommonWords.forEach(word => {\n  wordFreq[word] = (wordFreq[word] || 0) + 1;\n});\nconst tags = Object.entries(wordFreq)\n  .sort(([,a], [,b]) => b - a)\n  .slice(0, 5)\n  .map(([word]) => word);\n\nreturn {\n  json: {\n    ...($input.item.json),\n    blogStructure: {\n      title,\n      introduction: paragraphs[0] || '',\n      body: paragraphs.slice(1).join('\\n\\n'),\n      conclusion: paragraphs[paragraphs.length - 1] || '',\n      wordCount,\n      readingTime,\n      tags\n    },\n    analyzed: true\n  }\n};"}, "name": "Analyze Content", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [850, 300]}, {"parameters": {"functionCode": "// Generate SEO-optimized blog post\nconst { blogStructure, title, userId, contentId, processId } = $input.item.json;\n\n// Generate SEO title (max 60 characters)\nconst seoTitle = title.length > 60 ? title.substring(0, 57) + '...' : title;\n\n// Generate meta description (max 160 characters)\nconst metaDescription = blogStructure.introduction.length > 160 \n  ? blogStructure.introduction.substring(0, 157) + '...'\n  : blogStructure.introduction;\n\n// Generate slug\nconst slug = title.toLowerCase()\n  .replace(/[^a-z0-9\\s-]/g, '')\n  .replace(/\\s+/g, '-')\n  .replace(/-+/g, '-')\n  .trim('-');\n\n// Create formatted blog post\nconst blogPost = {\n  title: seoTitle,\n  slug,\n  metaDescription,\n  content: `# ${title}\\n\\n${blogStructure.introduction}\\n\\n${blogStructure.body}\\n\\n## Conclusion\\n\\n${blogStructure.conclusion}`,\n  tags: blogStructure.tags,\n  wordCount: blogStructure.wordCount,\n  readingTime: blogStructure.readingTime,\n  publishedAt: new Date().toISOString(),\n  status: 'draft'\n};\n\nreturn {\n  json: {\n    ...($input.item.json),\n    blogPost,\n    blogGenerated: true\n  }\n};"}, "name": "Generate Blog Post", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"functionCode": "// Generate social media snippets for promotion\nconst { blogPost, userId, contentId, processId } = $input.item.json;\n\n// Generate Twitter snippet (max 280 characters)\nconst twitterSnippet = `📝 New blog post: ${blogPost.title}\\n\\n${blogPost.metaDescription}\\n\\n#blog #content`;\n\n// Generate LinkedIn snippet\nconst linkedinSnippet = `I just published a new blog post: \"${blogPost.title}\"\\n\\n${blogPost.metaDescription}\\n\\nRead more: [Link to blog post]\\n\\n${blogPost.tags.map(tag => `#${tag}`).join(' ')}`;\n\n// Generate Facebook snippet\nconst facebookSnippet = `📖 New blog post is live!\\n\\n\"${blogPost.title}\"\\n\\n${blogPost.metaDescription}\\n\\nWhat do you think about this topic? Share your thoughts in the comments!`;\n\nreturn {\n  json: {\n    ...($input.item.json),\n    socialSnippets: {\n      twitter: twitterSnippet,\n      linkedin: linkedinSnippet,\n      facebook: facebookSnippet\n    },\n    socialGenerated: true\n  }\n};"}, "name": "Generate Social Snippets", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1250, 300]}, {"parameters": {"url": "=http://backend:4000/api/content/{{$json[\"contentId\"]}}/blog-result", "options": {"jsonParameters": true}, "bodyParametersJson": "={{ JSON.stringify($json) }}"}, "name": "Save Blog Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1450, 300]}], "connections": {"Start": {"main": [[{"node": "Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Extract Content", "type": "main", "index": 0}]]}, "Extract Content": {"main": [[{"node": "Analyze Content", "type": "main", "index": 0}]]}, "Analyze Content": {"main": [[{"node": "Generate Blog Post", "type": "main", "index": 0}]]}, "Generate Blog Post": {"main": [[{"node": "Generate Social Snippets", "type": "main", "index": 0}]]}, "Generate Social Snippets": {"main": [[{"node": "Save Blog Post", "type": "main", "index": 0}]]}}}