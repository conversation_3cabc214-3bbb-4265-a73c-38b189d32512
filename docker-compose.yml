version: '3.8'
services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: contentforge-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - NEXT_PUBLIC_API_URL=http://backend:4000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm run dev
    depends_on:
      - backend
    networks:
      - contentforge-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: contentforge-backend
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=development
      - PORT=4000
      - SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - SUP<PERSON>ASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - N8N_API_URL=http://n8n:5678/api/v1
      - N8N_WEBHOOK_BASE_URL=http://backend:4000/api/webhooks
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD:-admin}
    restart: unless-stopped
    volumes:
      - ./backend:/app
      - /app/node_modules
    command: sh -c "npm install && npm run dev"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4000/"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 5s
    depends_on:
      - n8n
    networks:
      - contentforge-network

  n8n:
    image: n8nio/n8n
    container_name: contentforge-n8n
    ports:
      - "5678:5678"
    environment:
      - GENERIC_TIMEZONE=Europe/London
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - N8N_HOST=0.0.0.0
      - N8N_EDITOR_BASE_URL=http://localhost:5678
      - NODE_ENV=development
      - N8N_DIAGNOSTICS_ENABLED=false
      - N8N_USER_MANAGEMENT_DISABLED=false
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD:-admin}
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5678/"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - contentforge-network

  # Local development services
  mailhog:
    image: mailhog/mailhog
    container_name: contentforge-mailhog
    ports:
      - "1025:1025" # SMTP server
      - "8025:8025" # Web UI
    networks:
      - contentforge-network

networks:
  contentforge-network:
    driver: bridge

volumes:
  n8n_data:
