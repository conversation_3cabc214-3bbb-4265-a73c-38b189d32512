# ContentForge
*Digital Content Automation Platform*

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://frontend-4gyueiv34-gem-devs-projects.vercel.app)
[![Progress](https://img.shields.io/badge/progress-65%25-blue)](docs/project-status-update.md)
[![Next.js](https://img.shields.io/badge/Next.js-15.3.2-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-100%25-blue)](https://www.typescriptlang.org/)

ContentForge is a digital content automation platform that streamlines creation, transformation, and multi-platform publishing (YouTube, Instagram, LinkedIn). Built with Next.js, Supabase, and n8n, it offers AI-driven workflows, analytics, and a scalable, secure MVP. Developed on a zero-budget with an 8GB RAM MacBook Pro in 12 weeks.

**🚀 Live Demo**: [https://frontend-4gyueiv34-gem-devs-projects.vercel.app](https://frontend-4gyueiv34-gem-devs-projects.vercel.app)

## 📊 Project Status

- **Overall Progress**: 65% Complete
- **MVP Target**: On track for delivery
- **Last Updated**: January 25, 2025
- **Deployment**: ✅ Operational on Vercel

## 🎯 Module Status

| Module | Progress | Status |
|--------|----------|--------|
| Authentication & User Management | 95% | ✅ Complete |
| Frontend Infrastructure | 90% | ✅ Complete |
| Backend API Framework | 85% | ✅ Complete |
| Content Management | 70% | 🔄 In Progress |
| Analytics Dashboard | 60% | 🔄 In Progress |
| Workflow Orchestration | 40% | 🔄 Planned |
| Publishing System | 30% | 📋 Planned |
| Content Transformation | 30% | 📋 Planned |
| Notifications | 20% | 📋 Planned |

## Features

- **Universal Input Portal**: Accept diverse content inputs (text, images, video)
- **Intelligent Workflow Orchestration**: Automate content transformation and publishing with n8n
- **Multi-Platform Publishing**: Publish to YouTube, Instagram, LinkedIn, and more
- **Pre-built Workflow Templates**: Ready-to-use templates for common content automation tasks
- **Real-time Workflow Monitoring**: Track workflow execution status and results
- **Centralized Content Management**: Organize and track all your content
- **Analytics Dashboard**: Monitor performance across platforms

### Workflow Automation Features

- **Text to Blog Post**: Convert text content into SEO-optimized blog posts with social media snippets
- **Image to Social Media**: Optimize images for multiple platforms with automated captions
- **Video to Multi-Platform**: Distribute video content across YouTube, Instagram, TikTok, and LinkedIn
- **Custom Workflow Creation**: Build your own workflows using the n8n visual editor
- **Template Management**: Import, export, and share workflow templates
- **Execution History**: Monitor all workflow executions with detailed logs and results

## Tech Stack

- **Frontend**: Next.js with TypeScript and Tailwind CSS
- **Backend**: Node.js/Express.js
- **Database**: Supabase (PostgreSQL)
- **Workflow Automation**: n8n
- **Deployment**: Vercel (frontend), Docker (backend)

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- npm or yarn
- Docker and Docker Compose (for local development)
- Git

### Quick Start with n8n Workflow Automation

```bash
# Clone the repository
git clone https://github.com/your-username/contentforge.git
cd contentforge

# Quick setup with n8n workflows
./scripts/quick-start-n8n.sh

# Or manual setup
npm install
docker-compose up -d
cd backend && npm run setup-n8n
```

### Manual Installation

```bash
# Install dependencies
npm install

# Start development servers
docker-compose up

# Set up n8n workflows (in a new terminal)
cd backend
npm run setup-n8n
```

For detailed setup instructions, see the [Setup Guide](./docs/setup-guide.md) or [n8n Workflow Setup](./docs/n8n-workflow-setup.md).

## Documentation

- [Setup Guide](./docs/setup-guide.md)
- [Contributing Guide](./docs/contributing.md)
- [Troubleshooting](./docs/troubleshooting.md)
- [Developer Onboarding](./docs/onboarding.md)

## Development

```bash
# Run frontend development server
npm run dev

# Run linting
npm run lint

# Run tests
npm test
```

## Deployment

The application can be deployed using Docker and Vercel. See the [deployment documentation](./docs/readme.md#deployment) for details.

## Contributing

Contributions are welcome! Please see our [Contributing Guide](./docs/contributing.md) for details.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
