-- Migration: Analytics and Platform Credentials Tables
-- Description: Create tables for analytics data collection and platform API credentials
-- Date: 2024-01-XX

-- Create published_content table if it doesn't exist
CREATE TABLE IF NOT EXISTS published_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID REFERENCES content(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    platform VARCHAR(50) NOT NULL,
    platform_post_id VARCHAR(255), -- Platform-specific post ID
    publication_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(50) DEFAULT 'published',
    url TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create analytics table
CREATE TABLE IF NOT EXISTS analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    published_content_id UUID REFERENCES published_content(id) ON DELETE CASCADE,
    platform VARCHAR(50) NOT NULL,
    metrics JSONB NOT NULL, -- Store all metrics as JSON
    date DATE NOT NULL, -- Date of the analytics data
    collected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create platform_credentials table for storing API tokens
CREATE TABLE IF NOT EXISTS platform_credentials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    platform VARCHAR(50) NOT NULL,
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active', -- active, expired, revoked
    account_info JSONB, -- Store platform-specific account details
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create collection_summaries table for tracking analytics collection
CREATE TABLE IF NOT EXISTS collection_summaries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    date DATE NOT NULL,
    users_processed INTEGER DEFAULT 0,
    total_analytics_collected INTEGER DEFAULT 0,
    total_failed INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0,
    execution_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_published_content_user_id ON published_content(user_id);
CREATE INDEX IF NOT EXISTS idx_published_content_platform ON published_content(platform);
CREATE INDEX IF NOT EXISTS idx_published_content_publication_date ON published_content(publication_date);
CREATE INDEX IF NOT EXISTS idx_published_content_status ON published_content(status);

CREATE INDEX IF NOT EXISTS idx_analytics_published_content_id ON analytics(published_content_id);
CREATE INDEX IF NOT EXISTS idx_analytics_platform ON analytics(platform);
CREATE INDEX IF NOT EXISTS idx_analytics_date ON analytics(date);
CREATE INDEX IF NOT EXISTS idx_analytics_collected_at ON analytics(collected_at);

CREATE INDEX IF NOT EXISTS idx_platform_credentials_user_id ON platform_credentials(user_id);
CREATE INDEX IF NOT EXISTS idx_platform_credentials_platform ON platform_credentials(platform);
CREATE INDEX IF NOT EXISTS idx_platform_credentials_status ON platform_credentials(status);

CREATE INDEX IF NOT EXISTS idx_collection_summaries_date ON collection_summaries(date);

-- Create unique constraint to prevent duplicate analytics for same content and date
CREATE UNIQUE INDEX IF NOT EXISTS idx_analytics_unique_content_date 
ON analytics(published_content_id, date);

-- Create unique constraint for platform credentials per user per platform
CREATE UNIQUE INDEX IF NOT EXISTS idx_platform_credentials_unique_user_platform 
ON platform_credentials(user_id, platform) WHERE status = 'active';

-- Add RLS (Row Level Security) policies
ALTER TABLE published_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_credentials ENABLE ROW LEVEL SECURITY;
ALTER TABLE collection_summaries ENABLE ROW LEVEL SECURITY;

-- RLS Policies for published_content
CREATE POLICY IF NOT EXISTS "Users can view their own published content" 
ON published_content FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own published content" 
ON published_content FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own published content" 
ON published_content FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own published content" 
ON published_content FOR DELETE 
USING (auth.uid() = user_id);

-- RLS Policies for analytics
CREATE POLICY IF NOT EXISTS "Users can view analytics for their content" 
ON analytics FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM published_content 
        WHERE published_content.id = analytics.published_content_id 
        AND published_content.user_id = auth.uid()
    )
);

CREATE POLICY IF NOT EXISTS "System can insert analytics" 
ON analytics FOR INSERT 
WITH CHECK (true); -- Allow system to insert analytics

CREATE POLICY IF NOT EXISTS "System can update analytics" 
ON analytics FOR UPDATE 
USING (true); -- Allow system to update analytics

-- RLS Policies for platform_credentials
CREATE POLICY IF NOT EXISTS "Users can view their own credentials" 
ON platform_credentials FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own credentials" 
ON platform_credentials FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own credentials" 
ON platform_credentials FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own credentials" 
ON platform_credentials FOR DELETE 
USING (auth.uid() = user_id);

-- RLS Policies for collection_summaries (admin only)
CREATE POLICY IF NOT EXISTS "Admins can view collection summaries" 
ON collection_summaries FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.id = auth.uid() 
        AND users.role = 'admin'
    )
);

CREATE POLICY IF NOT EXISTS "System can insert collection summaries" 
ON collection_summaries FOR INSERT 
WITH CHECK (true);

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for platform_credentials updated_at
CREATE TRIGGER IF NOT EXISTS update_platform_credentials_updated_at 
    BEFORE UPDATE ON platform_credentials 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create view for analytics with content details
CREATE OR REPLACE VIEW analytics_with_content AS
SELECT 
    a.id,
    a.published_content_id,
    a.platform,
    a.metrics,
    a.date,
    a.collected_at,
    a.created_at,
    pc.content_id,
    pc.user_id,
    pc.url as content_url,
    pc.publication_date,
    c.title as content_title
FROM analytics a
JOIN published_content pc ON a.published_content_id = pc.id
LEFT JOIN content c ON pc.content_id = c.id;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON published_content TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON analytics TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON platform_credentials TO authenticated;
GRANT SELECT ON collection_summaries TO authenticated;
GRANT SELECT ON analytics_with_content TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE published_content IS 'Tracks content published to external platforms';
COMMENT ON TABLE analytics IS 'Stores performance metrics collected from social media platforms';
COMMENT ON TABLE platform_credentials IS 'Stores API credentials for social media platforms';
COMMENT ON TABLE collection_summaries IS 'Tracks analytics collection job summaries';

COMMENT ON COLUMN analytics.metrics IS 'JSON object containing platform-specific metrics (views, likes, shares, etc.)';
COMMENT ON COLUMN platform_credentials.account_info IS 'JSON object containing platform-specific account details';
COMMENT ON COLUMN published_content.metadata IS 'JSON object containing platform-specific publication metadata';
