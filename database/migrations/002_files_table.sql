-- Create files table for file storage management
CREATE TABLE IF NOT EXISTS "public"."files" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "user_id" UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  "filename" TEXT NOT NULL,
  "file_path" TEXT NOT NULL,
  "file_url" TEXT NOT NULL,
  "mime_type" TEXT NOT NULL,
  "file_size" BIGINT NOT NULL,
  "file_hash" TEXT NOT NULL,
  "metadata" JSONB DEFAULT '{}'::jsonb,
  "storage_provider" TEXT NOT NULL DEFAULT 'supabase',
  "is_processed" BOOLEAN DEFAULT false,
  "processing_status" TEXT DEFAULT 'pending',
  "processing_error" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_files_user_id" ON "public"."files" ("user_id");
CREATE INDEX IF NOT EXISTS "idx_files_mime_type" ON "public"."files" ("mime_type");
CREATE INDEX IF NOT EXISTS "idx_files_hash" ON "public"."files" ("file_hash");
CREATE INDEX IF NOT EXISTS "idx_files_created_at" ON "public"."files" ("created_at");
CREATE INDEX IF NOT EXISTS "idx_files_processing_status" ON "public"."files" ("processing_status");

-- Enable RLS on files table
ALTER TABLE "public"."files" ENABLE ROW LEVEL SECURITY;

-- Files policies
CREATE POLICY "Users can view their own files" 
  ON "public"."files" FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can upload their own files" 
  ON "public"."files" FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own files" 
  ON "public"."files" FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own files" 
  ON "public"."files" FOR DELETE 
  USING (auth.uid() = user_id);

-- Add trigger for updated_at
CREATE TRIGGER update_files_modtime
  BEFORE UPDATE ON files
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

-- Create file processing queue table
CREATE TABLE IF NOT EXISTS "public"."file_processing_queue" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "file_id" UUID NOT NULL REFERENCES public.files(id) ON DELETE CASCADE,
  "processing_type" TEXT NOT NULL,
  "priority" INTEGER DEFAULT 5,
  "status" TEXT NOT NULL DEFAULT 'pending',
  "started_at" TIMESTAMP WITH TIME ZONE,
  "completed_at" TIMESTAMP WITH TIME ZONE,
  "error_message" TEXT,
  "retry_count" INTEGER DEFAULT 0,
  "max_retries" INTEGER DEFAULT 3,
  "processing_options" JSONB DEFAULT '{}'::jsonb,
  "result" JSONB DEFAULT '{}'::jsonb,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create indexes for processing queue
CREATE INDEX IF NOT EXISTS "idx_queue_status" ON "public"."file_processing_queue" ("status");
CREATE INDEX IF NOT EXISTS "idx_queue_priority" ON "public"."file_processing_queue" ("priority");
CREATE INDEX IF NOT EXISTS "idx_queue_created_at" ON "public"."file_processing_queue" ("created_at");
CREATE INDEX IF NOT EXISTS "idx_queue_file_id" ON "public"."file_processing_queue" ("file_id");

-- Enable RLS on processing queue table
ALTER TABLE "public"."file_processing_queue" ENABLE ROW LEVEL SECURITY;

-- Processing queue policies
CREATE POLICY "Users can view processing queue for their files" 
  ON "public"."file_processing_queue" FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.files 
    WHERE files.id = file_processing_queue.file_id 
    AND files.user_id = auth.uid()
  ));

CREATE POLICY "Users can create processing jobs for their files" 
  ON "public"."file_processing_queue" FOR INSERT 
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.files 
    WHERE files.id = file_processing_queue.file_id 
    AND files.user_id = auth.uid()
  ));

-- Add trigger for processing queue updated_at
CREATE TRIGGER update_file_processing_queue_modtime
  BEFORE UPDATE ON file_processing_queue
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

-- Update content table to reference files
ALTER TABLE "public"."content" ADD COLUMN IF NOT EXISTS "input_file_id" UUID REFERENCES public.files(id) ON DELETE SET NULL;
ALTER TABLE "public"."content" ADD COLUMN IF NOT EXISTS "input_data" JSONB DEFAULT '{}'::jsonb;

-- Create index for content file reference
CREATE INDEX IF NOT EXISTS "idx_content_input_file_id" ON "public"."content" ("input_file_id");
