-- Add OAuth token storage for platform integrations
-- This migration adds tables to store OAuth tokens and refresh tokens securely

-- Create oauth_tokens table for storing platform authentication tokens
CREATE TABLE IF NOT EXISTS "public"."oauth_tokens" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "user_id" UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  "platform" TEXT NOT NULL,
  "access_token" TEXT NOT NULL,
  "refresh_token" TEXT,
  "token_type" TEXT DEFAULT 'Bearer',
  "expires_at" TIMESTAMP WITH TIME ZONE,
  "scope" TEXT,
  "platform_user_id" TEXT,
  "platform_username" TEXT,
  "is_active" BOOLEAN DEFAULT true,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(user_id, platform)
);

-- Create scheduled_publications table for content scheduling
CREATE TABLE IF NOT EXISTS "public"."scheduled_publications" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "user_id" UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  "content_id" UUID NOT NULL REFERENCES public.content(id) ON DELETE CASCADE,
  "platform" TEXT NOT NULL,
  "scheduled_time" TIMESTAMP WITH TIME ZONE NOT NULL,
  "status" TEXT NOT NULL DEFAULT 'scheduled', -- scheduled, publishing, published, failed, cancelled
  "published_url" TEXT,
  "platform_post_id" TEXT,
  "error_message" TEXT,
  "retry_count" INTEGER DEFAULT 0,
  "max_retries" INTEGER DEFAULT 3,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create published_content table for tracking published content
CREATE TABLE IF NOT EXISTS "public"."published_content" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "user_id" UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  "content_id" UUID NOT NULL REFERENCES public.content(id) ON DELETE CASCADE,
  "platform" TEXT NOT NULL,
  "platform_post_id" TEXT NOT NULL,
  "published_url" TEXT,
  "status" TEXT NOT NULL DEFAULT 'published', -- published, failed, deleted
  "platform_data" JSONB DEFAULT '{}'::jsonb,
  "published_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(content_id, platform)
);

-- Enable RLS on new tables
ALTER TABLE "public"."oauth_tokens" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."scheduled_publications" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."published_content" ENABLE ROW LEVEL SECURITY;

-- OAuth tokens policies
CREATE POLICY "Users can view their own OAuth tokens" 
  ON "public"."oauth_tokens" FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own OAuth tokens" 
  ON "public"."oauth_tokens" FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own OAuth tokens" 
  ON "public"."oauth_tokens" FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own OAuth tokens" 
  ON "public"."oauth_tokens" FOR DELETE 
  USING (auth.uid() = user_id);

-- Scheduled publications policies
CREATE POLICY "Users can view their own scheduled publications" 
  ON "public"."scheduled_publications" FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own scheduled publications" 
  ON "public"."scheduled_publications" FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own scheduled publications" 
  ON "public"."scheduled_publications" FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own scheduled publications" 
  ON "public"."scheduled_publications" FOR DELETE 
  USING (auth.uid() = user_id);

-- Published content policies
CREATE POLICY "Users can view their own published content" 
  ON "public"."published_content" FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own published content records" 
  ON "public"."published_content" FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own published content records" 
  ON "public"."published_content" FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own published content records" 
  ON "public"."published_content" FOR DELETE 
  USING (auth.uid() = user_id);

-- Add triggers for updated_at
CREATE TRIGGER update_oauth_tokens_modtime
  BEFORE UPDATE ON oauth_tokens
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_scheduled_publications_modtime
  BEFORE UPDATE ON scheduled_publications
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_published_content_modtime
  BEFORE UPDATE ON published_content
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_user_platform ON oauth_tokens(user_id, platform);
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_expires_at ON oauth_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_scheduled_publications_scheduled_time ON scheduled_publications(scheduled_time);
CREATE INDEX IF NOT EXISTS idx_scheduled_publications_status ON scheduled_publications(status);
CREATE INDEX IF NOT EXISTS idx_published_content_user_platform ON published_content(user_id, platform);
CREATE INDEX IF NOT EXISTS idx_published_content_published_at ON published_content(published_at);
