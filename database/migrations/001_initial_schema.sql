-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create schema for application
CREATE SCHEMA IF NOT EXISTS "public";

-- Set up auth tables and triggers
-- Note: <PERSON><PERSON><PERSON> already sets up auth schema and tables

-- Create users table (extends auth.users)
CREATE TABLE IF NOT EXISTS "public"."profiles" (
  "id" UUID NOT NULL PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  "username" TEXT UNIQUE,
  "full_name" TEXT,
  "avatar_url" TEXT,
  "website" TEXT,
  "company" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create content table
CREATE TABLE IF NOT EXISTS "public"."content" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "user_id" UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  "title" TEXT NOT NULL,
  "description" TEXT,
  "content_type" TEXT NOT NULL,
  "source_url" TEXT,
  "status" TEXT NOT NULL DEFAULT 'draft',
  "metadata" JSONB DEFAULT '{}'::jsonb,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create content_versions table
CREATE TABLE IF NOT EXISTS "public"."content_versions" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "content_id" UUID NOT NULL REFERENCES public.content(id) ON DELETE CASCADE,
  "version" INTEGER NOT NULL,
  "data" JSONB NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "created_by" UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE
);

-- Create platforms table
CREATE TABLE IF NOT EXISTS "public"."platforms" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "name" TEXT NOT NULL UNIQUE,
  "description" TEXT,
  "icon" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create content_platforms table (for publishing status)
CREATE TABLE IF NOT EXISTS "public"."content_platforms" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "content_id" UUID NOT NULL REFERENCES public.content(id) ON DELETE CASCADE,
  "platform_id" UUID NOT NULL REFERENCES public.platforms(id) ON DELETE CASCADE,
  "status" TEXT NOT NULL DEFAULT 'pending',
  "published_url" TEXT,
  "published_at" TIMESTAMP WITH TIME ZONE,
  "platform_data" JSONB DEFAULT '{}'::jsonb,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(content_id, platform_id)
);

-- Create workflows table
CREATE TABLE IF NOT EXISTS "public"."workflows" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "name" TEXT NOT NULL,
  "description" TEXT,
  "n8n_workflow_id" TEXT,
  "is_active" BOOLEAN DEFAULT true,
  "config" JSONB DEFAULT '{}'::jsonb,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create content_workflows table
CREATE TABLE IF NOT EXISTS "public"."content_workflows" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "content_id" UUID NOT NULL REFERENCES public.content(id) ON DELETE CASCADE,
  "workflow_id" UUID NOT NULL REFERENCES public.workflows(id) ON DELETE CASCADE,
  "status" TEXT NOT NULL DEFAULT 'pending',
  "result" JSONB DEFAULT '{}'::jsonb,
  "started_at" TIMESTAMP WITH TIME ZONE,
  "completed_at" TIMESTAMP WITH TIME ZONE,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create analytics table
CREATE TABLE IF NOT EXISTS "public"."analytics" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "content_id" UUID NOT NULL REFERENCES public.content(id) ON DELETE CASCADE,
  "platform_id" UUID NOT NULL REFERENCES public.platforms(id) ON DELETE CASCADE,
  "views" INTEGER DEFAULT 0,
  "likes" INTEGER DEFAULT 0,
  "shares" INTEGER DEFAULT 0,
  "comments" INTEGER DEFAULT 0,
  "data" JSONB DEFAULT '{}'::jsonb,
  "last_updated" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(content_id, platform_id)
);

-- Create user_platform_credentials table
CREATE TABLE IF NOT EXISTS "public"."user_platform_credentials" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "user_id" UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  "platform_id" UUID NOT NULL REFERENCES public.platforms(id) ON DELETE CASCADE,
  "credentials" JSONB NOT NULL,
  "is_active" BOOLEAN DEFAULT true,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(user_id, platform_id)
);

-- Set up Row Level Security (RLS) policies

-- Enable RLS on all tables
ALTER TABLE "public"."profiles" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."content" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."content_versions" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."platforms" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."content_platforms" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."workflows" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."content_workflows" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."analytics" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."user_platform_credentials" ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view their own profile" 
  ON "public"."profiles" FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" 
  ON "public"."profiles" FOR UPDATE 
  USING (auth.uid() = id);

-- Content policies
CREATE POLICY "Users can view their own content" 
  ON "public"."content" FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own content" 
  ON "public"."content" FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own content" 
  ON "public"."content" FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own content" 
  ON "public"."content" FOR DELETE 
  USING (auth.uid() = user_id);

-- Content versions policies
CREATE POLICY "Users can view their own content versions" 
  ON "public"."content_versions" FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.content 
    WHERE content.id = content_versions.content_id 
    AND content.user_id = auth.uid()
  ));

CREATE POLICY "Users can create versions for their own content" 
  ON "public"."content_versions" FOR INSERT 
  WITH CHECK (
    auth.uid() = created_by AND
    EXISTS (
      SELECT 1 FROM public.content 
      WHERE content.id = content_versions.content_id 
      AND content.user_id = auth.uid()
    )
  );

-- Platforms policies (public read, admin write)
CREATE POLICY "Platforms are viewable by all users" 
  ON "public"."platforms" FOR SELECT 
  USING (true);

-- Content platforms policies
CREATE POLICY "Users can view their own content platform connections" 
  ON "public"."content_platforms" FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.content 
    WHERE content.id = content_platforms.content_id 
    AND content.user_id = auth.uid()
  ));

CREATE POLICY "Users can create platform connections for their own content" 
  ON "public"."content_platforms" FOR INSERT 
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.content 
    WHERE content.id = content_platforms.content_id 
    AND content.user_id = auth.uid()
  ));

CREATE POLICY "Users can update platform connections for their own content" 
  ON "public"."content_platforms" FOR UPDATE 
  USING (EXISTS (
    SELECT 1 FROM public.content 
    WHERE content.id = content_platforms.content_id 
    AND content.user_id = auth.uid()
  ));

CREATE POLICY "Users can delete platform connections for their own content" 
  ON "public"."content_platforms" FOR DELETE 
  USING (EXISTS (
    SELECT 1 FROM public.content 
    WHERE content.id = content_platforms.content_id 
    AND content.user_id = auth.uid()
  ));

-- Workflows policies (public read, admin write)
CREATE POLICY "Workflows are viewable by all users" 
  ON "public"."workflows" FOR SELECT 
  USING (true);

-- Content workflows policies
CREATE POLICY "Users can view workflows for their own content" 
  ON "public"."content_workflows" FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.content 
    WHERE content.id = content_workflows.content_id 
    AND content.user_id = auth.uid()
  ));

CREATE POLICY "Users can create workflow connections for their own content" 
  ON "public"."content_workflows" FOR INSERT 
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.content 
    WHERE content.id = content_workflows.content_id 
    AND content.user_id = auth.uid()
  ));

-- Analytics policies
CREATE POLICY "Users can view analytics for their own content" 
  ON "public"."analytics" FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.content 
    WHERE content.id = analytics.content_id 
    AND content.user_id = auth.uid()
  ));

-- User platform credentials policies
CREATE POLICY "Users can view their own platform credentials" 
  ON "public"."user_platform_credentials" FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own platform credentials" 
  ON "public"."user_platform_credentials" FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own platform credentials" 
  ON "public"."user_platform_credentials" FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own platform credentials" 
  ON "public"."user_platform_credentials" FOR DELETE 
  USING (auth.uid() = user_id);

-- Create functions and triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_modified_column() 
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for updated_at
CREATE TRIGGER update_profiles_modtime
  BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_content_modtime
  BEFORE UPDATE ON content
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_platforms_modtime
  BEFORE UPDATE ON platforms
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_content_platforms_modtime
  BEFORE UPDATE ON content_platforms
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_workflows_modtime
  BEFORE UPDATE ON workflows
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_content_workflows_modtime
  BEFORE UPDATE ON content_workflows
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_user_platform_credentials_modtime
  BEFORE UPDATE ON user_platform_credentials
  FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

-- Create trigger for creating a profile after a user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, username, avatar_url)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'avatar_url');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Insert initial platform data
INSERT INTO public.platforms (name, description, icon)
VALUES 
  ('YouTube', 'Video sharing platform', 'youtube'),
  ('Instagram', 'Photo and video sharing social network', 'instagram'),
  ('LinkedIn', 'Professional networking platform', 'linkedin'),
  ('Twitter', 'Microblogging and social networking service', 'twitter'),
  ('Facebook', 'Social media and social networking service', 'facebook'),
  ('Medium', 'Online publishing platform', 'medium'),
  ('TikTok', 'Short-form video hosting service', 'tiktok')
ON CONFLICT (name) DO NOTHING;

-- Insert initial workflow templates
INSERT INTO public.workflows (name, description, config)
VALUES 
  ('Text to Video', 'Convert text content to video format', '{"steps": ["text_extraction", "script_generation", "video_creation"]}'),
  ('Blog to Social', 'Convert blog posts to social media content', '{"steps": ["text_extraction", "summary_generation", "image_creation"]}'),
  ('Video to Social Clips', 'Extract clips from videos for social media', '{"steps": ["video_analysis", "clip_extraction", "format_conversion"]}'),
  ('Image Optimization', 'Optimize images for different platforms', '{"steps": ["image_analysis", "resize", "format_conversion"]}')
ON CONFLICT (name) DO NOTHING;
