const express = require('express');
const router = express.Router();
const fileController = require('../controllers/fileController');
const authMiddleware = require('../middleware/authMiddleware');

// Apply authentication middleware to all routes
router.use(authMiddleware);

/**
 * @route POST /api/files/upload
 * @desc Upload a single file
 * @access Private
 */
router.post('/upload', fileController.uploadFile);

/**
 * @route POST /api/files/upload-multiple
 * @desc Upload multiple files
 * @access Private
 */
router.post('/upload-multiple', fileController.uploadMultipleFiles);

/**
 * @route GET /api/files
 * @desc List user files with pagination and filtering
 * @access Private
 */
router.get('/', fileController.listFiles);

/**
 * @route GET /api/files/:fileId
 * @desc Get file by ID
 * @access Private
 */
router.get('/:fileId', fileController.getFile);

/**
 * @route DELETE /api/files/:fileId
 * @desc Delete file by ID
 * @access Private
 */
router.delete('/:fileId', fileController.deleteFile);

/**
 * @route GET /api/files/usage/stats
 * @desc Get storage usage statistics
 * @access Private
 */
router.get('/usage/stats', fileController.getStorageUsage);

/**
 * @route POST /api/files/:fileId/process
 * @desc Add file to processing queue
 * @access Private
 */
router.post('/:fileId/process', fileController.processFile);

/**
 * @route GET /api/files/queue/stats
 * @desc Get processing queue statistics
 * @access Private
 */
router.get('/queue/stats', fileController.getQueueStats);

module.exports = router;
