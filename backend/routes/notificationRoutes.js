const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const authMiddleware = require('../middleware/authMiddleware');

// All notification routes require authentication
router.use(authMiddleware.authenticate);

// Get notifications for the current user
router.get('/',
  notificationController.getNotifications
);

// Create a new notification (requires permission)
router.post('/',
  authMiddleware.checkPermission('user', 'create'),
  notificationController.createNotification
);

// Mark notification as read
router.put('/:id/read',
  notificationController.markAsRead
);

// Mark all notifications as read
router.put('/read-all',
  notificationController.markAllAsRead
);

// Delete notification
router.delete('/:id',
  notificationController.deleteNotification
);

// Delete all notifications
router.delete('/',
  notificationController.deleteAllNotifications
);

// Get notification preferences
router.get('/preferences',
  notificationController.getNotificationPreferences
);

// Update notification preferences
router.put('/preferences',
  notificationController.updateNotificationPreferences
);

module.exports = router;
