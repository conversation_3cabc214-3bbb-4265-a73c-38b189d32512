const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const authMiddleware = require('../middleware/authMiddleware');

// Public routes
router.post('/register', userController.register);
router.post('/login', userController.login);
router.post('/forgot-password', userController.forgotPassword);
router.post('/reset-password', userController.resetPassword);

// Protected routes (require authentication)
router.get('/profile', authMiddleware.authenticate, userController.getProfile);
router.put('/profile', authMiddleware.authenticate, userController.updateProfile);
router.get('/preferences', authMiddleware.authenticate, userController.getPreferences);
router.put('/preferences', authMiddleware.authenticate, userController.updatePreferences);

// Role and permission information (available to all authenticated users)
router.get('/roles-permissions', authMiddleware.authenticate, userController.getRolesAndPermissions);

// Admin routes (require admin role)
router.get('/',
  authMiddleware.authenticate,
  authMiddleware.checkPermission('user', 'read'),
  userController.getAllUsers
);

router.get('/:id',
  authMiddleware.authenticate,
  authMiddleware.checkPermission('user', 'read'),
  userController.getUserById
);

router.put('/:id/role',
  authMiddleware.authenticate,
  authMiddleware.checkPermission('user', 'update'),
  userController.updateUserRole
);

router.put('/:id/permissions',
  authMiddleware.authenticate,
  authMiddleware.checkPermission('user', 'update'),
  userController.updateUserPermissions
);

router.put('/:id/deactivate',
  authMiddleware.authenticate,
  authMiddleware.checkPermission('user', 'update'),
  userController.deactivateUser
);

router.put('/:id/reactivate',
  authMiddleware.authenticate,
  authMiddleware.checkPermission('user', 'update'),
  userController.reactivateUser
);

module.exports = router;
