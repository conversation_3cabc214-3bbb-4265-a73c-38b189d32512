const express = require('express');
const router = express.Router();
const oauthController = require('../controllers/oauthController');
const authMiddleware = require('../middleware/authMiddleware');

// OAuth routes for platform authentication

// Get authorization URL for a platform
router.get('/auth/:platform',
  authMiddleware.authenticate,
  oauthController.getAuthUrl
);

// Handle OAuth callback
router.get('/callback/:platform',
  oauthController.handleCallback
);

// Get connected platforms for authenticated user
router.get('/connected',
  authMiddleware.authenticate,
  oauthController.getConnectedPlatforms
);

// Disconnect a platform
router.delete('/disconnect/:platform',
  authMiddleware.authenticate,
  oauthController.disconnectPlatform
);

// Refresh OAuth tokens
router.post('/refresh/:platform',
  authMiddleware.authenticate,
  oauthController.refreshTokens
);

module.exports = router;
