const express = require('express');
const router = express.Router();
const contentController = require('../controllers/contentController');
const authMiddleware = require('../middleware/authMiddleware');

// All content routes require authentication
router.use(authMiddleware.authenticate);

// Content CRUD operations with permission checks
router.post('/',
  authMiddleware.checkPermission('content', 'create'),
  contentController.createContent
);

router.get('/',
  authMiddleware.checkPermission('content', 'read'),
  contentController.getAllContent
);

// Content search and filtering (must come before /:id routes to avoid conflicts)
router.get('/search',
  authMiddleware.checkPermission('content', 'read'),
  contentController.searchContent
);

router.get('/filter',
  authMiddleware.checkPermission('content', 'read'),
  contentController.filterContent
);

// Content specific routes
router.get('/:id',
  authMiddleware.checkPermission('content', 'read'),
  contentController.getContentById
);

router.put('/:id',
  authMiddleware.checkPermission('content', 'update'),
  contentController.updateContent
);

router.delete('/:id',
  authMiddleware.checkPermission('content', 'delete'),
  contentController.deleteContent
);

// Content versioning
router.get('/:id/versions',
  authMiddleware.checkPermission('content', 'read'),
  contentController.getContentVersions
);

router.get('/:id/versions/:versionId',
  authMiddleware.checkPermission('content', 'read'),
  contentController.getContentVersion
);

module.exports = router;
