const express = require('express');
const router = express.Router();
const workflowController = require('../controllers/workflowController');
const authMiddleware = require('../middleware/authMiddleware');

// All workflow routes require authentication
router.use(authMiddleware.authenticate);

// Workflow templates (must come before /:id routes to avoid conflicts)
router.get('/templates',
  authMiddleware.checkPermission('workflow', 'read'),
  workflowController.getWorkflowTemplates
);

router.post('/templates/:templateId/clone',
  authMiddleware.checkPermission('workflow', 'create'),
  workflowController.cloneWorkflowTemplate
);

router.post('/templates/:templateId/import',
  authMiddleware.checkPermission('workflow', 'create'),
  workflowController.importTemplateToN8n
);

router.post('/n8n/:n8nWorkflowId/export',
  authMiddleware.checkPermission('workflow', 'create'),
  workflowController.exportN8nWorkflowAsTemplate
);

router.get('/templates/stats',
  authMiddleware.checkPermission('workflow', 'read'),
  workflowController.getTemplateStats
);

router.post('/templates/validate',
  authMiddleware.checkPermission('workflow', 'read'),
  workflowController.validateTemplate
);

// Workflow CRUD operations
router.post('/',
  authMiddleware.checkPermission('workflow', 'create'),
  workflowController.createWorkflow
);

router.get('/',
  authMiddleware.checkPermission('workflow', 'read'),
  workflowController.getAllWorkflows
);

router.get('/:id',
  authMiddleware.checkPermission('workflow', 'read'),
  workflowController.getWorkflowById
);

router.put('/:id',
  authMiddleware.checkPermission('workflow', 'update'),
  workflowController.updateWorkflow
);

router.delete('/:id',
  authMiddleware.checkPermission('workflow', 'delete'),
  workflowController.deleteWorkflow
);

// Workflow execution
router.post('/:id/execute',
  authMiddleware.checkPermission('workflow', 'update'),
  workflowController.executeWorkflow
);

router.get('/:id/status',
  authMiddleware.checkPermission('workflow', 'read'),
  workflowController.getWorkflowStatus
);

router.post('/:id/cancel',
  authMiddleware.checkPermission('workflow', 'update'),
  workflowController.cancelWorkflow
);

module.exports = router;
