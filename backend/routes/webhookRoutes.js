const express = require('express');
const router = express.Router();
const supabase = require('../config/supabase');

/**
 * Handle n8n workflow execution completion webhook
 */
router.post('/n8n/execution/:webhookId', async (req, res) => {
  try {
    const { webhookId } = req.params;
    const { workflowId, event } = req.query;
    const executionData = req.body;

    console.log(`Received n8n webhook: ${event} for workflow ${workflowId}, webhook ID: ${webhookId}`);

    if (event === 'execution_complete') {
      // Find the execution in our database
      const { data: execution, error: executionError } = await supabase
        .from('workflow_executions')
        .select('*')
        .eq('workflow_id', workflowId)
        .eq('n8n_execution_id', executionData.executionId)
        .single();

      if (executionError) {
        console.error('Error finding execution:', executionError);
        return res.status(404).json({ error: 'Execution not found' });
      }

      // Update execution status based on n8n result
      let status = 'completed';
      let error = null;

      if (executionData.status === 'error') {
        status = 'failed';
        error = executionData.error?.message || 'Unknown error';
      } else if (executionData.status === 'canceled') {
        status = 'cancelled';
      }

      // Update execution in database
      const { error: updateError } = await supabase
        .from('workflow_executions')
        .update({
          status,
          error,
          results: executionData.data,
          n8n_data: executionData,
          completed_at: new Date()
        })
        .eq('id', execution.id);

      if (updateError) {
        console.error('Error updating execution:', updateError);
        return res.status(500).json({ error: 'Failed to update execution' });
      }

      return res.status(200).json({ message: 'Execution updated successfully' });
    } else {
      // Handle other webhook events
      console.log(`Unhandled webhook event: ${event}`);
      return res.status(200).json({ message: 'Webhook received' });
    }
  } catch (error) {
    console.error('Webhook error:', error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
});

/**
 * Handle blog post workflow results
 */
router.post('/content/:contentId/blog-result', async (req, res) => {
  try {
    const { contentId } = req.params;
    const workflowResult = req.body;

    console.log(`Received blog workflow result for content ${contentId}`);

    // Update content with blog post data
    const { error } = await supabase
      .from('content')
      .update({
        processed_data: {
          ...workflowResult,
          type: 'blog_post',
          processed_at: new Date().toISOString()
        },
        status: 'processed',
        updated_at: new Date()
      })
      .eq('id', contentId);

    if (error) {
      console.error('Error updating content with blog result:', error);
      return res.status(500).json({ error: 'Failed to update content' });
    }

    return res.status(200).json({ message: 'Blog result saved successfully' });
  } catch (error) {
    console.error('Blog result webhook error:', error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
});

/**
 * Handle social media workflow results
 */
router.post('/content/:contentId/social-result', async (req, res) => {
  try {
    const { contentId } = req.params;
    const workflowResult = req.body;

    console.log(`Received social workflow result for content ${contentId}`);

    // Update content with social media data
    const { error } = await supabase
      .from('content')
      .update({
        processed_data: {
          ...workflowResult,
          type: 'social_media',
          processed_at: new Date().toISOString()
        },
        status: 'processed',
        updated_at: new Date()
      })
      .eq('id', contentId);

    if (error) {
      console.error('Error updating content with social result:', error);
      return res.status(500).json({ error: 'Failed to update content' });
    }

    return res.status(200).json({ message: 'Social result saved successfully' });
  } catch (error) {
    console.error('Social result webhook error:', error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
});

/**
 * Handle multi-platform workflow results
 */
router.post('/content/:contentId/multiplatform-result', async (req, res) => {
  try {
    const { contentId } = req.params;
    const workflowResult = req.body;

    console.log(`Received multiplatform workflow result for content ${contentId}`);

    // Update content with multi-platform data
    const { error } = await supabase
      .from('content')
      .update({
        processed_data: {
          ...workflowResult,
          type: 'multiplatform',
          processed_at: new Date().toISOString()
        },
        status: 'processed',
        updated_at: new Date()
      })
      .eq('id', contentId);

    if (error) {
      console.error('Error updating content with multiplatform result:', error);
      return res.status(500).json({ error: 'Failed to update content' });
    }

    return res.status(200).json({ message: 'Multiplatform result saved successfully' });
  } catch (error) {
    console.error('Multiplatform result webhook error:', error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
});

module.exports = router;
