const express = require('express');
const router = express.Router();
const analyticsController = require('../controllers/analyticsController');
const authMiddleware = require('../middleware/authMiddleware');

// All analytics routes require authentication
router.use(authMiddleware.authenticate);

// Analytics operations with permission checks
router.get('/content/:contentId',
  authMiddleware.checkPermission('analytics', 'read'),
  analyticsController.getContentAnalytics
);

router.get('/platform/:platform',
  authMiddleware.checkPermission('analytics', 'read'),
  analyticsController.getPlatformAnalytics
);

router.get('/overview',
  authMiddleware.checkPermission('analytics', 'read'),
  analyticsController.getAnalyticsOverview
);

router.get('/performance',
  authMiddleware.checkPermission('analytics', 'read'),
  analyticsController.getPerformanceMetrics
);

// Date range filtering
router.get('/range',
  authMiddleware.checkPermission('analytics', 'read'),
  analyticsController.getAnalyticsByDateRange
);

// Export analytics
router.get('/export',
  authMiddleware.checkPermission('analytics', 'read'),
  analyticsController.exportAnalytics
);

// Trigger analytics collection
router.post('/collect',
  authMiddleware.checkPermission('analytics', 'write'),
  analyticsController.triggerAnalyticsCollection
);

// Get collection status
router.get('/status',
  authMiddleware.checkPermission('analytics', 'read'),
  analyticsController.getCollectionStatus
);

// Get top performing content
router.get('/content/top',
  authMiddleware.checkPermission('analytics', 'read'),
  analyticsController.getTopContent
);

// Get real-time summary
router.get('/realtime',
  authMiddleware.checkPermission('analytics', 'read'),
  analyticsController.getRealTimeSummary
);

module.exports = router;
