const express = require('express');
const router = express.Router();
const transformController = require('../controllers/transformController');
const authMiddleware = require('../middleware/authMiddleware');

// All transform routes require authentication
router.use(authMiddleware.authenticate);

// Get information about available transformers
router.get('/info',
  authMiddleware.checkPermission('content', 'read'),
  transformController.getTransformerInfo
);

// Get supported transformation types
router.get('/types',
  authMiddleware.checkPermission('content', 'read'),
  transformController.getSupportedTypes
);

// Transform content (file upload)
router.post('/',
  authMiddleware.checkPermission('content', 'create'),
  transformController.transformContent
);

// Transform text content (no file upload)
router.post('/text',
  authMiddleware.checkPermission('content', 'create'),
  transformController.transformText
);

// Optimize content for specific platform
router.post('/optimize',
  authMiddleware.checkPermission('content', 'create'),
  transformController.optimizeForPlatform
);

// Generate content summary
router.post('/summary',
  authMiddleware.checkPermission('content', 'create'),
  transformController.generateSummary
);

// Extract and suggest hashtags
router.post('/hashtags',
  authMiddleware.checkPermission('content', 'create'),
  transformController.extractHashtags
);

// Analyze content quality
router.post('/quality',
  authMiddleware.checkPermission('content', 'create'),
  transformController.analyzeQuality
);

// Get platform specifications
router.get('/platforms/:platform/specs',
  authMiddleware.checkPermission('content', 'read'),
  transformController.getPlatformSpecs
);

module.exports = router;
