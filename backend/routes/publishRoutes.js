const express = require('express');
const router = express.Router();
const publishController = require('../controllers/publishController');
const authMiddleware = require('../middleware/authMiddleware');

// All publishing routes require authentication
router.use(authMiddleware.authenticate);

// Get available platforms for the user
router.get('/platforms',
  authMiddleware.checkPermission('publish', 'read'),
  publishController.getAvailablePlatforms
);

// Platform-specific operations (must come before /:id routes to avoid conflicts)
router.post('/youtube',
  authMiddleware.checkPermission('publish', 'create'),
  publishController.publishToYouTube
);

router.post('/instagram',
  authMiddleware.checkPermission('publish', 'create'),
  publishController.publishToInstagram
);

router.post('/linkedin',
  authMiddleware.checkPermission('publish', 'create'),
  publishController.publishToLinkedIn
);

router.post('/blog',
  authMiddleware.checkPermission('publish', 'create'),
  publishController.publishToBlog
);

// Scheduling (must come before /:id routes to avoid conflicts)
router.post('/schedule',
  authMiddleware.checkPermission('publish', 'create'),
  publishController.schedulePublication
);

router.get('/schedule',
  authMiddleware.checkPermission('publish', 'read'),
  publishController.getScheduledPublications
);

router.put('/schedule/:id',
  authMiddleware.checkPermission('publish', 'update'),
  publishController.updateScheduledPublication
);

router.delete('/schedule/:id',
  authMiddleware.checkPermission('publish', 'delete'),
  publishController.cancelScheduledPublication
);

// Publishing operations
router.post('/',
  authMiddleware.checkPermission('publish', 'create'),
  publishController.publishContent
);

router.get('/',
  authMiddleware.checkPermission('publish', 'read'),
  publishController.getAllPublishedContent
);

router.get('/:id',
  authMiddleware.checkPermission('publish', 'read'),
  publishController.getPublishedContentById
);

router.delete('/:id',
  authMiddleware.checkPermission('publish', 'delete'),
  publishController.unpublishContent
);

module.exports = router;
