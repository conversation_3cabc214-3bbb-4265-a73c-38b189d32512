const supabase = require('../config/supabase');

// Define role hierarchy and permissions
const ROLES = {
  admin: { level: 3, description: 'Full access to all features' },
  editor: { level: 2, description: 'Can create and manage content' },
  viewer: { level: 1, description: 'Read-only access' }
};

// Define permissions for different resources
const PERMISSIONS = {
  content: {
    create: ['admin', 'editor'],
    read: ['admin', 'editor', 'viewer'],
    update: ['admin', 'editor'],
    delete: ['admin']
  },
  workflow: {
    create: ['admin', 'editor'],
    read: ['admin', 'editor', 'viewer'],
    update: ['admin', 'editor'],
    delete: ['admin']
  },
  user: {
    create: ['admin'],
    read: ['admin'],
    update: ['admin'],
    delete: ['admin']
  },
  analytics: {
    read: ['admin', 'editor', 'viewer']
  },
  publish: {
    create: ['admin', 'editor'],
    read: ['admin', 'editor', 'viewer'],
    update: ['admin', 'editor'],
    delete: ['admin', 'editor']
  },
  notification: {
    create: ['admin', 'editor'],
    read: ['admin', 'editor', 'viewer'],
    update: ['admin', 'editor', 'viewer'],
    delete: ['admin', 'editor', 'viewer']
  }
};

/**
 * Middleware to authenticate requests using Supabase JWT
 */
exports.authenticate = async (req, res, next) => {
  try {
    // Get the authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized', message: 'Authentication token is required' });
    }

    // Extract the token
    const token = authHeader.split(' ')[1];

    // Verify the token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({ error: 'Unauthorized', message: 'Invalid or expired token' });
    }

    // Get user's profile including role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role, permissions')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      // Continue without role information
      req.user = user;
      req.userRole = 'viewer'; // Default to lowest role
      req.userPermissions = {};
    } else {
      // Attach user, role and custom permissions to the request object
      req.user = user;
      req.userRole = profile.role || 'viewer';
      req.userPermissions = profile.permissions || {};
    }

    // Continue to the next middleware or route handler
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Authentication failed' });
  }
};

/**
 * Middleware to check if user has required role
 */
exports.authorize = (requiredRole) => {
  return async (req, res, next) => {
    try {
      // Get user and role from previous middleware
      const { user, userRole } = req;

      if (!user) {
        return res.status(401).json({ error: 'Unauthorized', message: 'User not authenticated' });
      }

      // If role wasn't attached in authenticate middleware, fetch it now
      if (!userRole) {
        // Get user's role from Supabase
        const { data, error } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (error || !data) {
          return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch user role' });
        }

        req.userRole = data.role;
      }

      // Check if user has the required role or higher
      const userRoleLevel = ROLES[req.userRole]?.level || 0;
      const requiredRoleLevel = ROLES[requiredRole]?.level || 999; // Default to highest level if role not found

      if (userRoleLevel < requiredRoleLevel) {
        return res.status(403).json({
          error: 'Forbidden',
          message: `Insufficient permissions. Required role: ${requiredRole}`
        });
      }

      // User has the required role, continue
      next();
    } catch (error) {
      console.error('Authorization error:', error);
      return res.status(500).json({ error: 'Internal Server Error', message: 'Authorization failed' });
    }
  };
};

/**
 * Middleware to check if user has required permission for a resource
 */
exports.checkPermission = (resource, action) => {
  return async (req, res, next) => {
    try {
      // Get user, role and permissions from previous middleware
      const { user, userRole, userPermissions } = req;

      if (!user) {
        return res.status(401).json({ error: 'Unauthorized', message: 'User not authenticated' });
      }

      // Check for custom user-specific permissions override
      if (userPermissions &&
          userPermissions[resource] &&
          userPermissions[resource][action] === true) {
        // User has explicit permission
        return next();
      }

      // Check if user role has permission for this resource and action
      const allowedRoles = PERMISSIONS[resource]?.[action] || [];

      if (!allowedRoles.includes(userRole)) {
        return res.status(403).json({
          error: 'Forbidden',
          message: `You don't have permission to ${action} ${resource}`
        });
      }

      // User has permission, continue
      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({ error: 'Internal Server Error', message: 'Permission check failed' });
    }
  };
};

/**
 * Get all roles defined in the system
 */
exports.getRoles = () => ROLES;

/**
 * Get all permissions defined in the system
 */
exports.getPermissions = () => PERMISSIONS;
