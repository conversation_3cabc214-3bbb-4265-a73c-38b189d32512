const request = require('supertest');
const app = require('../app');
const platformManager = require('../services/platformManager');
const schedulingService = require('../services/schedulingService');

// Mock the platform services
jest.mock('../services/platformManager');
jest.mock('../services/schedulingService');

describe('Platform Integration API', () => {
  // Mock user authentication
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>'
  };

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock authentication middleware
    jest.doMock('../middleware/authMiddleware', () => ({
      authenticate: (req, res, next) => {
        req.user = mockUser;
        next();
      },
      checkPermission: () => (req, res, next) => next()
    }));
  });

  describe('OAuth Endpoints', () => {
    test('GET /api/oauth/auth/:platform should return auth URL', async () => {
      const response = await request(app)
        .get('/api/oauth/auth/youtube')
        .expect(200);

      expect(response.body).toHaveProperty('authUrl');
      expect(response.body).toHaveProperty('state');
      expect(response.body.platform).toBe('youtube');
    });

    test('GET /api/oauth/auth/:platform should reject unsupported platform', async () => {
      const response = await request(app)
        .get('/api/oauth/auth/unsupported')
        .expect(400);

      expect(response.body.error).toBe('Unsupported Platform');
    });

    test('GET /api/oauth/connected should return connected platforms', async () => {
      const response = await request(app)
        .get('/api/oauth/connected')
        .expect(200);

      expect(response.body).toHaveProperty('connected_platforms');
    });
  });

  describe('Publishing Endpoints', () => {
    test('GET /api/publish/platforms should return available platforms', async () => {
      platformManager.getAvailablePlatforms.mockResolvedValue({
        success: true,
        platforms: [
          {
            platform: 'youtube',
            username: 'Test Channel',
            is_expired: false,
            capabilities: {
              supportedContentTypes: ['video/mp4'],
              supportedActions: ['publish', 'analytics']
            }
          }
        ],
        count: 1
      });

      const response = await request(app)
        .get('/api/publish/platforms')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.platforms).toHaveLength(1);
      expect(response.body.platforms[0].platform).toBe('youtube');
    });

    test('POST /api/publish should publish content to platforms', async () => {
      platformManager.publishToMultiplePlatforms.mockResolvedValue({
        success: true,
        results: {
          youtube: {
            success: true,
            videoId: 'test-video-id',
            videoUrl: 'https://youtube.com/watch?v=test-video-id'
          }
        },
        errors: {},
        published_platforms: ['youtube'],
        failed_platforms: []
      });

      const publishData = {
        content_id: 'test-content-id',
        platforms: ['youtube']
      };

      const response = await request(app)
        .post('/api/publish')
        .send(publishData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.published_platforms).toContain('youtube');
    });

    test('POST /api/publish should handle missing content_id', async () => {
      const publishData = {
        platforms: ['youtube']
      };

      const response = await request(app)
        .post('/api/publish')
        .send(publishData)
        .expect(400);

      expect(response.body.error).toBe('Bad Request');
    });
  });

  describe('Scheduling Endpoints', () => {
    test('POST /api/publish/schedule should schedule publications', async () => {
      schedulingService.schedulePublication.mockResolvedValue({
        success: true,
        scheduled_publication: {
          id: 'test-schedule-id',
          content_id: 'test-content-id',
          platform: 'youtube',
          scheduled_time: '2024-12-25T10:00:00Z',
          status: 'scheduled'
        },
        message: 'Successfully scheduled publication to youtube'
      });

      const scheduleData = {
        content_id: 'test-content-id',
        platforms: ['youtube'],
        schedule_time: '2024-12-25T10:00:00Z'
      };

      const response = await request(app)
        .post('/api/publish/schedule')
        .send(scheduleData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.scheduled_publications).toHaveLength(1);
    });

    test('GET /api/publish/schedule should return scheduled publications', async () => {
      schedulingService.getScheduledPublications.mockResolvedValue({
        success: true,
        scheduled_publications: [
          {
            id: 'test-schedule-id',
            content_id: 'test-content-id',
            platform: 'youtube',
            scheduled_time: '2024-12-25T10:00:00Z',
            status: 'scheduled'
          }
        ],
        count: 1
      });

      const response = await request(app)
        .get('/api/publish/schedule')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.scheduled_publications).toHaveLength(1);
    });

    test('DELETE /api/publish/schedule/:id should cancel scheduled publication', async () => {
      schedulingService.cancelScheduledPublication.mockResolvedValue({
        success: true,
        message: 'Successfully cancelled scheduled publication'
      });

      const response = await request(app)
        .delete('/api/publish/schedule/test-schedule-id')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('cancelled');
    });
  });

  describe('Platform Manager', () => {
    test('should initialize for user', async () => {
      const result = await platformManager.initializeForUser('test-user-id');
      expect(platformManager.initializeForUser).toHaveBeenCalledWith('test-user-id');
    });

    test('should publish to multiple platforms', async () => {
      const content = {
        id: 'test-content-id',
        title: 'Test Content',
        description: 'Test Description',
        content_type: 'video/mp4'
      };

      const result = await platformManager.publishToMultiplePlatforms(
        'test-user-id',
        content,
        ['youtube', 'instagram'],
        {}
      );

      expect(platformManager.publishToMultiplePlatforms).toHaveBeenCalledWith(
        'test-user-id',
        content,
        ['youtube', 'instagram'],
        {}
      );
    });
  });

  describe('Scheduling Service', () => {
    test('should schedule publication', async () => {
      const scheduledTime = new Date('2024-12-25T10:00:00Z');
      
      const result = await schedulingService.schedulePublication(
        'test-user-id',
        'test-content-id',
        'youtube',
        scheduledTime,
        {}
      );

      expect(schedulingService.schedulePublication).toHaveBeenCalledWith(
        'test-user-id',
        'test-content-id',
        'youtube',
        scheduledTime,
        {}
      );
    });

    test('should get scheduled publications', async () => {
      const options = { platform: 'youtube', limit: 10 };
      
      const result = await schedulingService.getScheduledPublications(
        'test-user-id',
        options
      );

      expect(schedulingService.getScheduledPublications).toHaveBeenCalledWith(
        'test-user-id',
        options
      );
    });

    test('should cancel scheduled publication', async () => {
      const result = await schedulingService.cancelScheduledPublication(
        'test-user-id',
        'test-schedule-id'
      );

      expect(schedulingService.cancelScheduledPublication).toHaveBeenCalledWith(
        'test-user-id',
        'test-schedule-id'
      );
    });
  });

  describe('Error Handling', () => {
    test('should handle platform manager errors', async () => {
      platformManager.getAvailablePlatforms.mockResolvedValue({
        success: false,
        error: 'Database connection failed',
        message: 'Failed to fetch available platforms'
      });

      const response = await request(app)
        .get('/api/publish/platforms')
        .expect(500);

      expect(response.body.error).toBe('Platform Fetch Failed');
    });

    test('should handle scheduling service errors', async () => {
      schedulingService.schedulePublication.mockResolvedValue({
        success: false,
        error: 'Platform not connected',
        message: 'You need to connect your youtube account'
      });

      const scheduleData = {
        content_id: 'test-content-id',
        platforms: ['youtube'],
        schedule_time: '2024-12-25T10:00:00Z'
      };

      const response = await request(app)
        .post('/api/publish/schedule')
        .send(scheduleData)
        .expect(400);

      expect(response.body.error).toBe('Scheduling Failed');
    });
  });

  describe('Input Validation', () => {
    test('should validate required fields for publishing', async () => {
      const response = await request(app)
        .post('/api/publish')
        .send({})
        .expect(400);

      expect(response.body.error).toBe('Bad Request');
      expect(response.body.message).toContain('Content ID');
    });

    test('should validate required fields for scheduling', async () => {
      const response = await request(app)
        .post('/api/publish/schedule')
        .send({
          content_id: 'test-content-id'
        })
        .expect(400);

      expect(response.body.error).toBe('Bad Request');
      expect(response.body.message).toContain('platforms');
    });

    test('should validate schedule time format', async () => {
      const response = await request(app)
        .post('/api/publish/schedule')
        .send({
          content_id: 'test-content-id',
          platforms: ['youtube'],
          schedule_time: 'invalid-date'
        })
        .expect(400);

      expect(response.body.error).toBe('Bad Request');
      expect(response.body.message).toContain('Invalid schedule time');
    });
  });
});
