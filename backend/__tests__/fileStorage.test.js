const FileStorageService = require('../services/storage/fileStorageService');
const path = require('path');
const fs = require('fs').promises;

describe('File Storage Service', () => {
  let fileStorage;
  const testUserId = 'test-user-123';

  beforeEach(() => {
    fileStorage = new FileStorageService({
      bucketName: 'test-bucket',
      tempDir: path.join(__dirname, 'temp')
    });
  });

  afterEach(async () => {
    // Clean up test files
    try {
      await fs.rmdir(path.join(__dirname, 'temp'), { recursive: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('File Validation', () => {
    test('should validate file size', () => {
      const file = {
        name: 'test.jpg',
        size: 200 * 1024 * 1024, // 200MB
        mimetype: 'image/jpeg'
      };

      const validation = fileStorage.validateFile(file);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(expect.stringContaining('exceeds maximum allowed size'));
    });

    test('should validate MIME type', () => {
      const file = {
        name: 'test.exe',
        size: 1024,
        mimetype: 'application/x-executable'
      };

      const validation = fileStorage.validateFile(file);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(expect.stringContaining('not allowed'));
    });

    test('should reject dangerous file extensions', () => {
      const file = {
        name: 'malicious.exe',
        size: 1024,
        mimetype: 'image/jpeg' // Spoofed MIME type
      };

      const validation = fileStorage.validateFile(file);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(expect.stringContaining('not allowed for security reasons'));
    });

    test('should accept valid files', () => {
      const file = {
        name: 'test.jpg',
        size: 1024 * 1024, // 1MB
        mimetype: 'image/jpeg'
      };

      const validation = fileStorage.validateFile(file);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });
  });

  describe('Filename Sanitization', () => {
    test('should sanitize dangerous characters', () => {
      const filename = 'test/../../../etc/passwd';
      const sanitized = fileStorage.sanitizeFilename(filename);
      expect(sanitized).toBe('test_____etc_passwd');
    });

    test('should handle empty filenames', () => {
      const filename = '';
      const sanitized = fileStorage.sanitizeFilename(filename);
      expect(sanitized).toBe('unnamed_file');
    });

    test('should limit filename length', () => {
      const longFilename = 'a'.repeat(300) + '.txt';
      const sanitized = fileStorage.sanitizeFilename(longFilename);
      expect(sanitized.length).toBeLessThanOrEqual(255);
      expect(sanitized).toEndWith('.txt');
    });
  });

  describe('File Path Generation', () => {
    test('should generate hierarchical file paths', () => {
      const filename = 'test.jpg';
      const filePath = fileStorage.generateFilePath(filename, testUserId);
      
      expect(filePath).toMatch(new RegExp(`^${testUserId}/\\d{4}/\\d{2}/[a-f0-9-]+\\d+-test\\.jpg$`));
    });

    test('should handle special characters in filename', () => {
      const filename = 'test file (1).jpg';
      const filePath = fileStorage.generateFilePath(filename, testUserId);
      
      expect(filePath).toContain('test_file__1_');
      expect(filePath).toEndWith('.jpg');
    });
  });

  describe('File Hash Calculation', () => {
    test('should calculate consistent hash for same content', () => {
      const content1 = Buffer.from('test content');
      const content2 = Buffer.from('test content');
      
      const hash1 = fileStorage.calculateFileHash(content1);
      const hash2 = fileStorage.calculateFileHash(content2);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toHaveLength(64); // SHA-256 hex length
    });

    test('should calculate different hash for different content', () => {
      const content1 = Buffer.from('test content 1');
      const content2 = Buffer.from('test content 2');
      
      const hash1 = fileStorage.calculateFileHash(content1);
      const hash2 = fileStorage.calculateFileHash(content2);
      
      expect(hash1).not.toBe(hash2);
    });
  });

  describe('File Size Formatting', () => {
    test('should format bytes correctly', () => {
      expect(fileStorage.formatFileSize(0)).toBe('0 B');
      expect(fileStorage.formatFileSize(1024)).toBe('1 KB');
      expect(fileStorage.formatFileSize(1024 * 1024)).toBe('1 MB');
      expect(fileStorage.formatFileSize(1024 * 1024 * 1024)).toBe('1 GB');
    });

    test('should handle decimal places', () => {
      expect(fileStorage.formatFileSize(1536)).toBe('1.5 KB'); // 1.5 KB
      expect(fileStorage.formatFileSize(1572864)).toBe('1.5 MB'); // 1.5 MB
    });
  });
});
