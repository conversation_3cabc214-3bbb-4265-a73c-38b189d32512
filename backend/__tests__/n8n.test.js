const n8nService = require('../services/n8n/n8nService');
const axios = require('axios');

// Mock axios
jest.mock('axios');

describe('n8n Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getWorkflows', () => {
    it('should fetch workflows from n8n', async () => {
      // Mock axios response
      axios.create.mockReturnValue({
        get: jest.fn().mockResolvedValue({
          data: [
            { id: 'workflow1', name: 'Test Workflow 1' },
            { id: 'workflow2', name: 'Test Workflow 2' }
          ]
        })
      });

      const result = await n8nService.getWorkflows();

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data[0].name).toBe('Test Workflow 1');
    });

    it('should handle errors when fetching workflows', async () => {
      // Mock axios error response
      axios.create.mockReturnValue({
        get: jest.fn().mockRejectedValue({
          response: {
            data: {
              message: 'API Error'
            }
          }
        })
      });

      const result = await n8nService.getWorkflows();

      expect(result.success).toBe(false);
      expect(result.error).toBe('API Error');
    });
  });

  describe('createWorkflow', () => {
    it('should create a workflow in n8n', async () => {
      // Mock axios response
      axios.create.mockReturnValue({
        post: jest.fn().mockResolvedValue({
          data: { id: 'new-workflow', name: 'New Workflow' }
        })
      });

      const workflowData = {
        name: 'New Workflow',
        nodes: [],
        connections: {}
      };

      const result = await n8nService.createWorkflow(workflowData);

      expect(result.success).toBe(true);
      expect(result.data.id).toBe('new-workflow');
      expect(result.data.name).toBe('New Workflow');
    });

    it('should handle errors when creating a workflow', async () => {
      // Mock axios error response
      axios.create.mockReturnValue({
        post: jest.fn().mockRejectedValue({
          response: {
            data: {
              message: 'Invalid workflow data'
            }
          }
        })
      });

      const workflowData = {
        name: 'Invalid Workflow',
        nodes: 'not-an-array' // Invalid data
      };

      const result = await n8nService.createWorkflow(workflowData);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid workflow data');
    });
  });

  describe('executeWorkflow', () => {
    it('should execute a workflow in n8n', async () => {
      // Mock axios response
      axios.create.mockReturnValue({
        post: jest.fn().mockResolvedValue({
          data: { 
            executionId: 'exec123',
            status: 'running'
          }
        })
      });

      const result = await n8nService.executeWorkflow('workflow1', { data: 'test' });

      expect(result.success).toBe(true);
      expect(result.data.executionId).toBe('exec123');
      expect(result.data.status).toBe('running');
    });

    it('should handle errors when executing a workflow', async () => {
      // Mock axios error response
      axios.create.mockReturnValue({
        post: jest.fn().mockRejectedValue({
          response: {
            data: {
              message: 'Workflow not found'
            }
          }
        })
      });

      const result = await n8nService.executeWorkflow('non-existent', { data: 'test' });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Workflow not found');
    });
  });

  describe('getTemplates', () => {
    it('should return workflow templates', async () => {
      const templates = await n8nService.getTemplates();
      
      expect(templates.success).toBe(true);
      expect(Array.isArray(templates.data)).toBe(true);
      expect(templates.data.length).toBeGreaterThan(0);
      
      // Check template structure
      const template = templates.data[0];
      expect(template).toHaveProperty('id');
      expect(template).toHaveProperty('name');
      expect(template).toHaveProperty('description');
      expect(template).toHaveProperty('nodes');
      expect(template).toHaveProperty('connections');
    });
  });

  describe('createWorkflowFromTemplate', () => {
    it('should create a workflow from a template', async () => {
      // Mock getTemplate response
      jest.spyOn(n8nService, 'getTemplate').mockResolvedValue({
        success: true,
        data: {
          id: 'template1',
          name: 'Test Template',
          nodes: [],
          connections: {},
          tags: ['test']
        }
      });
      
      // Mock createWorkflow response
      jest.spyOn(n8nService, 'createWorkflow').mockResolvedValue({
        success: true,
        data: {
          id: 'new-workflow',
          name: 'Custom Template Name'
        }
      });
      
      const result = await n8nService.createWorkflowFromTemplate('template1', {
        name: 'Custom Template Name'
      });
      
      expect(result.success).toBe(true);
      expect(result.data.id).toBe('new-workflow');
      expect(result.data.name).toBe('Custom Template Name');
    });
    
    it('should handle template not found', async () => {
      // Mock getTemplate response for not found
      jest.spyOn(n8nService, 'getTemplate').mockResolvedValue({
        success: false,
        error: 'Template not found'
      });
      
      const result = await n8nService.createWorkflowFromTemplate('non-existent');
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Template not found');
    });
  });

  describe('createWebhookUrl', () => {
    it('should create a valid webhook URL', () => {
      const url = n8nService.createWebhookUrl('workflow123', 'execution_complete');
      
      expect(url).toContain('/webhook/');
      expect(url).toContain('workflowId=workflow123');
      expect(url).toContain('event=execution_complete');
    });
  });
});
