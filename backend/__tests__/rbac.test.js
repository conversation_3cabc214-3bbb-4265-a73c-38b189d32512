const request = require('supertest');
const app = require('../app');
const supabase = require('../config/supabase');
const authMiddleware = require('../middleware/authMiddleware');

// Mock Supabase
jest.mock('../config/supabase', () => ({
  auth: {
    signUp: jest.fn(),
    signInWithPassword: jest.fn(),
    getUser: jest.fn(),
    signOut: jest.fn(),
    resetPasswordForEmail: jest.fn(),
    updateUser: jest.fn()
  },
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  single: jest.fn(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  range: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  or: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis(),
  gte: jest.fn().mockReturnThis(),
  lte: jest.fn().mockReturnThis(),
  contains: jest.fn().mockReturnThis()
}));

describe('Role-Based Access Control', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Authentication Middleware', () => {
    it('should authenticate a valid token', async () => {
      // Mock Supabase response
      supabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123', email: '<EMAIL>' } },
        error: null
      });

      supabase.from().select().eq().single.mockResolvedValue({
        data: { role: 'admin', permissions: {} },
        error: null
      });

      const req = {
        headers: { authorization: 'Bearer valid-token' },
        user: null
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await authMiddleware.authenticate(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(req.user).toEqual({ id: 'user123', email: '<EMAIL>' });
      expect(req.userRole).toBe('admin');
    });

    it('should reject an invalid token', async () => {
      // Mock Supabase response for invalid token
      supabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid token' }
      });

      const req = {
        headers: { authorization: 'Bearer invalid-token' },
        user: null
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      await authMiddleware.authenticate(req, res, next);

      expect(next).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Unauthorized'
      }));
    });
  });

  describe('Authorization Middleware', () => {
    it('should authorize a user with sufficient role', async () => {
      const req = {
        user: { id: 'user123' },
        userRole: 'admin'
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      const authorizeMiddleware = authMiddleware.authorize('editor');
      await authorizeMiddleware(req, res, next);

      expect(next).toHaveBeenCalled();
    });

    it('should reject a user with insufficient role', async () => {
      const req = {
        user: { id: 'user123' },
        userRole: 'viewer'
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      const authorizeMiddleware = authMiddleware.authorize('editor');
      await authorizeMiddleware(req, res, next);

      expect(next).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Forbidden'
      }));
    });
  });

  describe('Permission Middleware', () => {
    it('should allow access when user has required permission', async () => {
      const req = {
        user: { id: 'user123' },
        userRole: 'editor',
        userPermissions: {}
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      const permissionMiddleware = authMiddleware.checkPermission('content', 'create');
      await permissionMiddleware(req, res, next);

      expect(next).toHaveBeenCalled();
    });

    it('should deny access when user lacks required permission', async () => {
      const req = {
        user: { id: 'user123' },
        userRole: 'viewer',
        userPermissions: {}
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      const permissionMiddleware = authMiddleware.checkPermission('content', 'create');
      await permissionMiddleware(req, res, next);

      expect(next).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Forbidden'
      }));
    });

    it('should allow access when user has explicit permission override', async () => {
      const req = {
        user: { id: 'user123' },
        userRole: 'viewer',
        userPermissions: {
          content: {
            create: true
          }
        }
      };
      const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const next = jest.fn();

      const permissionMiddleware = authMiddleware.checkPermission('content', 'create');
      await permissionMiddleware(req, res, next);

      expect(next).toHaveBeenCalled();
    });
  });

  describe('Role and Permission Utilities', () => {
    it('should return all defined roles', () => {
      const roles = authMiddleware.getRoles();
      
      expect(roles).toHaveProperty('admin');
      expect(roles).toHaveProperty('editor');
      expect(roles).toHaveProperty('viewer');
    });

    it('should return all defined permissions', () => {
      const permissions = authMiddleware.getPermissions();
      
      expect(permissions).toHaveProperty('content');
      expect(permissions).toHaveProperty('workflow');
      expect(permissions).toHaveProperty('user');
      expect(permissions).toHaveProperty('analytics');
      expect(permissions).toHaveProperty('publish');
    });
  });
});
