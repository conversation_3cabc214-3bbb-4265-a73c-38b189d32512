const { TextTransformer, ImageTransformer } = require('../services/transform');
const ContentOptimizer = require('../services/transform/text/contentOptimizer');

describe('Content Transformation', () => {
  describe('Text Transformer', () => {
    let textTransformer;

    beforeEach(() => {
      textTransformer = new TextTransformer();
    });

    test('should transform plain text to markdown', async () => {
      const inputText = 'This is a test paragraph.\n\nThis is another paragraph.';
      const inputType = 'text/plain';
      const outputType = 'text/markdown';
      const options = {
        firstParagraphAsHeading: true,
        headingLevel: 2
      };

      const result = await textTransformer.transform(inputText, inputType, outputType, options);

      expect(result.success).toBe(true);
      expect(result.data).toContain('## This is a test paragraph.');
      expect(result.data).toContain('This is another paragraph.');
    });

    test('should transform plain text to HTML', async () => {
      const inputText = 'This is a test paragraph.\n\nThis is another paragraph.';
      const inputType = 'text/plain';
      const outputType = 'text/html';
      const options = {
        addHtmlStructure: true,
        cssClass: 'content'
      };

      const result = await textTransformer.transform(inputText, inputType, outputType, options);

      expect(result.success).toBe(true);
      expect(result.data).toContain('<div class="content">');
      expect(result.data).toContain('<p>This is a test paragraph.</p>');
      expect(result.data).toContain('<p>This is another paragraph.</p>');
    });

    test('should apply text operations', async () => {
      const inputText = '  this is a test string with extra spaces  ';
      const inputType = 'text/plain';
      const outputType = 'text/plain';
      const options = {
        trim: true,
        capitalize: true,
        removeExtraSpaces: true
      };

      const result = await textTransformer.transform(inputText, inputType, outputType, options);

      expect(result.success).toBe(true);
      expect(result.data).toBe('This Is A Test String With Extra Spaces');
    });

    test('should handle invalid input type', async () => {
      const inputText = 'This is a test paragraph.';
      const inputType = 'text/invalid';
      const outputType = 'text/plain';

      const result = await textTransformer.transform(inputText, inputType, outputType);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Unsupported input type');
    });

    test('should handle invalid output type', async () => {
      const inputText = 'This is a test paragraph.';
      const inputType = 'text/plain';
      const outputType = 'text/invalid';

      const result = await textTransformer.transform(inputText, inputType, outputType);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Unsupported output type');
    });

    test('should convert markdown to plain text', async () => {
      const inputText = '# Heading\n\nThis is **bold** and *italic* text.\n\n- List item 1\n- List item 2';
      const inputType = 'text/markdown';
      const outputType = 'text/plain';

      const result = await textTransformer.transform(inputText, inputType, outputType);

      expect(result.success).toBe(true);
      expect(result.data).not.toContain('#');
      expect(result.data).not.toContain('**');
      expect(result.data).not.toContain('*');
      expect(result.data).not.toContain('-');
      expect(result.data).toContain('Heading');
      expect(result.data).toContain('bold');
      expect(result.data).toContain('italic');
      expect(result.data).toContain('List item 1');
      expect(result.data).toContain('List item 2');
    });

    test('should convert HTML to plain text', async () => {
      const inputText = '<h1>Heading</h1><p>This is <strong>bold</strong> and <em>italic</em> text.</p><ul><li>List item 1</li><li>List item 2</li></ul>';
      const inputType = 'text/html';
      const outputType = 'text/plain';

      const result = await textTransformer.transform(inputText, inputType, outputType);

      expect(result.success).toBe(true);
      expect(result.data).not.toContain('<');
      expect(result.data).not.toContain('>');
      expect(result.data).toContain('Heading');
      expect(result.data).toContain('bold');
      expect(result.data).toContain('italic');
      expect(result.data).toContain('List item 1');
      expect(result.data).toContain('List item 2');
    });

    test('should convert plain text to JSON', async () => {
      const inputText = 'This is a test paragraph.\n\nThis is another paragraph.';
      const inputType = 'text/plain';
      const outputType = 'application/json';
      const options = {
        structure: 'array',
        pretty: true
      };

      const result = await textTransformer.transform(inputText, inputType, outputType, options);

      expect(result.success).toBe(true);

      // Parse the JSON result
      const jsonResult = JSON.parse(result.data);
      expect(Array.isArray(jsonResult)).toBe(true);
      expect(jsonResult).toHaveLength(2);
      expect(jsonResult[0]).toBe('This is a test paragraph.');
      expect(jsonResult[1]).toBe('This is another paragraph.');
    });

    test('should handle invalid input gracefully', async () => {
      const result = await textTransformer.transform(null, 'text/plain', 'text/markdown');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Input data is required');
    });
  });

  describe('Platform Optimization', () => {
    let textTransformer;

    beforeEach(() => {
      textTransformer = new TextTransformer();
    });

    test('should optimize content for Twitter', async () => {
      const longContent = 'This is a very long tweet that definitely exceeds the 280 character limit for Twitter and needs to be optimized to fit within the platform constraints while maintaining readability and engagement for the audience.';

      const result = await textTransformer.optimizeForPlatform(longContent, 'twitter');

      expect(result.success).toBe(true);
      expect(result.data.length).toBeLessThanOrEqual(280);
      expect(result.optimization.withinLimit).toBe(true);
      expect(result.optimization.platform).toBe('twitter');
    });

    test('should optimize content for Instagram', async () => {
      const content = 'Check out this amazing sunset! The colors are absolutely breathtaking. #sunset #photography #nature #beautiful #amazing';

      const result = await textTransformer.optimizeForPlatform(content, 'instagram', { generateHashtags: true });

      expect(result.success).toBe(true);
      expect(result.optimization.platform).toBe('instagram');
      expect(result.optimization.hashtagSuggestions).toBeDefined();
      expect(result.optimization.qualityScore).toBeDefined();
    });

    test('should provide quality score and recommendations', async () => {
      const poorContent = 'bad content';

      const result = await textTransformer.optimizeForPlatform(poorContent, 'linkedin');

      expect(result.success).toBe(true);
      expect(result.optimization.qualityScore.score).toBeLessThan(70);
      expect(result.optimization.qualityScore.recommendations).toBeDefined();
    });
  });

  describe('Content Summarization', () => {
    let textTransformer;

    beforeEach(() => {
      textTransformer = new TextTransformer();
    });

    test('should generate summary of long content', async () => {
      const longContent = `
        Artificial intelligence is transforming the way we work and live. Machine learning algorithms are becoming more sophisticated every day.
        Companies are investing heavily in AI research and development. The potential applications are virtually limitless.
        From healthcare to transportation, AI is making significant impacts. However, we must also consider the ethical implications.
        Privacy concerns and job displacement are important issues to address. The future of AI depends on responsible development.
      `;

      const result = await textTransformer.generateSummary(longContent, { maxLength: 150 });

      expect(result.success).toBe(true);
      expect(result.data.length).toBeLessThanOrEqual(150);
      expect(result.summary.compressionRatio).toBeLessThan(1);
      expect(result.summary.sentenceCount).toBeGreaterThan(0);
    });

    test('should handle short content appropriately', async () => {
      const shortContent = 'This is a short sentence.';

      const result = await textTransformer.generateSummary(shortContent);

      expect(result.success).toBe(true);
      expect(result.data).toBe(shortContent);
      expect(result.summary.compressionRatio).toBe(1.0);
    });
  });

  describe('Hashtag Extraction', () => {
    let textTransformer;

    beforeEach(() => {
      textTransformer = new TextTransformer();
    });

    test('should extract existing hashtags', async () => {
      const content = 'Love this new #technology and #innovation in #AI development!';

      const result = await textTransformer.extractHashtags(content);

      expect(result.success).toBe(true);
      expect(result.data.existing).toEqual(['#technology', '#innovation', '#AI']);
      expect(result.data.suggestions).toBeDefined();
    });

    test('should suggest hashtags based on content', async () => {
      const content = 'Just finished an amazing workout at the gym. Feeling strong and motivated!';

      const result = await textTransformer.extractHashtags(content, { platform: 'instagram', maxHashtags: 5 });

      expect(result.success).toBe(true);
      expect(result.data.suggestions.length).toBeGreaterThan(0);
      expect(result.data.suggestions.every(tag => tag.startsWith('#'))).toBe(true);
    });
  });

  describe('Quality Analysis', () => {
    let textTransformer;

    beforeEach(() => {
      textTransformer = new TextTransformer();
    });

    test('should analyze content quality', async () => {
      const goodContent = 'Are you ready to transform your business? Our innovative solutions help companies achieve remarkable growth through strategic planning and execution. #business #innovation #growth #strategy';

      const result = await textTransformer.analyzeQuality(goodContent, { platform: 'linkedin' });

      expect(result.success).toBe(true);
      expect(result.data.qualityScore.score).toBeGreaterThan(0);
      expect(result.data.qualityScore.grade).toMatch(/[A-F]/);
      expect(result.data.analysis.readabilityScore).toBeDefined();
      expect(result.data.analysis.sentiment).toBeDefined();
    });

    test('should provide recommendations for improvement', async () => {
      const poorContent = 'a';

      const result = await textTransformer.analyzeQuality(poorContent);

      expect(result.success).toBe(true);
      expect(result.data.qualityScore.score).toBeLessThan(50);
      expect(result.data.qualityScore.recommendations.length).toBeGreaterThan(0);
    });
  });

  describe('Image Transformer Platform Optimization', () => {
    let imageTransformer;

    beforeEach(() => {
      imageTransformer = new ImageTransformer();
    });

    test('should get platform specifications', () => {
      const youtubeSpecs = imageTransformer.getPlatformSpecs('youtube');
      expect(youtubeSpecs).toBeDefined();
      expect(youtubeSpecs.thumbnail.width).toBe(1280);
      expect(youtubeSpecs.thumbnail.height).toBe(720);

      const instagramSpecs = imageTransformer.getPlatformSpecs('instagram');
      expect(instagramSpecs).toBeDefined();
      expect(instagramSpecs.post.width).toBe(1080);
      expect(instagramSpecs.post.height).toBe(1080);
    });

    test('should handle unsupported platforms', () => {
      const specs = imageTransformer.getPlatformSpecs('unsupported-platform');
      expect(specs).toBeNull();
    });

    test('should calculate watermark positions correctly', () => {
      const imageMetadata = { width: 1000, height: 600 };
      const watermarkSize = { width: 200, height: 100 };

      const topLeft = imageTransformer.calculateWatermarkPosition(imageMetadata, 'top-left', watermarkSize);
      expect(topLeft).toEqual({ left: 20, top: 20 });

      const center = imageTransformer.calculateWatermarkPosition(imageMetadata, 'center', watermarkSize);
      expect(center.left).toBe(400); // (1000 - 200) / 2
      expect(center.top).toBe(250);  // (600 - 100) / 2
    });
  });
});
