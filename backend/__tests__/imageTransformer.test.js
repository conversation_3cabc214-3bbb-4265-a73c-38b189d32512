const ImageTransformer = require('../services/transform/image/imageTransformer');
const path = require('path');
const fs = require('fs').promises;

describe('Image Transformer', () => {
  let imageTransformer;

  beforeEach(() => {
    imageTransformer = new ImageTransformer({
      tempDir: path.join(__dirname, 'temp')
    });
  });

  afterEach(async () => {
    // Clean up test files
    try {
      await fs.rmdir(path.join(__dirname, 'temp'), { recursive: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('Platform Specifications', () => {
    test('should return YouTube specifications', () => {
      const specs = imageTransformer.getPlatformSpecs('youtube');
      
      expect(specs).toBeDefined();
      expect(specs.thumbnail).toEqual({
        width: 1280,
        height: 720,
        format: 'image/jpeg',
        quality: 90,
        fit: 'cover'
      });
    });

    test('should return Instagram specifications', () => {
      const specs = imageTransformer.getPlatformSpecs('instagram');
      
      expect(specs).toBeDefined();
      expect(specs.post).toEqual({
        width: 1080,
        height: 1080,
        format: 'image/jpeg',
        quality: 85,
        fit: 'cover'
      });
      expect(specs.story).toEqual({
        width: 1080,
        height: 1920,
        format: 'image/jpeg',
        quality: 85,
        fit: 'cover'
      });
    });

    test('should return LinkedIn specifications', () => {
      const specs = imageTransformer.getPlatformSpecs('linkedin');
      
      expect(specs).toBeDefined();
      expect(specs.post).toEqual({
        width: 1200,
        height: 627,
        format: 'image/jpeg',
        quality: 85,
        fit: 'cover'
      });
    });

    test('should return null for unsupported platforms', () => {
      const specs = imageTransformer.getPlatformSpecs('unsupported');
      expect(specs).toBeNull();
    });

    test('should be case insensitive', () => {
      const specs1 = imageTransformer.getPlatformSpecs('YouTube');
      const specs2 = imageTransformer.getPlatformSpecs('youtube');
      
      expect(specs1).toEqual(specs2);
    });
  });

  describe('Watermark Position Calculation', () => {
    const imageMetadata = { width: 1000, height: 600 };
    const watermarkSize = { width: 200, height: 100 };

    test('should calculate top-left position', () => {
      const position = imageTransformer.calculateWatermarkPosition(imageMetadata, 'top-left', watermarkSize);
      expect(position).toEqual({ left: 20, top: 20 });
    });

    test('should calculate top-right position', () => {
      const position = imageTransformer.calculateWatermarkPosition(imageMetadata, 'top-right', watermarkSize);
      expect(position).toEqual({ left: 780, top: 20 }); // 1000 - 200 - 20
    });

    test('should calculate bottom-left position', () => {
      const position = imageTransformer.calculateWatermarkPosition(imageMetadata, 'bottom-left', watermarkSize);
      expect(position).toEqual({ left: 20, top: 480 }); // 600 - 100 - 20
    });

    test('should calculate bottom-right position', () => {
      const position = imageTransformer.calculateWatermarkPosition(imageMetadata, 'bottom-right', watermarkSize);
      expect(position).toEqual({ left: 780, top: 480 });
    });

    test('should calculate center position', () => {
      const position = imageTransformer.calculateWatermarkPosition(imageMetadata, 'center', watermarkSize);
      expect(position).toEqual({ 
        left: Math.round((1000 - 200) / 2), // 400
        top: Math.round((600 - 100) / 2)   // 250
      });
    });

    test('should default to bottom-right for unknown positions', () => {
      const position = imageTransformer.calculateWatermarkPosition(imageMetadata, 'unknown', watermarkSize);
      expect(position).toEqual({ left: 780, top: 480 });
    });
  });

  describe('Format Options', () => {
    test('should return JPEG format options', () => {
      const options = imageTransformer.getFormatOptions('jpeg', { quality: 95 });
      
      expect(options).toEqual({
        quality: 95,
        progressive: true,
        chromaSubsampling: '4:2:0',
        trellisQuantisation: true
      });
    });

    test('should return PNG format options', () => {
      const options = imageTransformer.getFormatOptions('png', { compressionLevel: 9 });
      
      expect(options).toEqual({
        progressive: true,
        compressionLevel: 9,
        adaptiveFiltering: true
      });
    });

    test('should return WebP format options', () => {
      const options = imageTransformer.getFormatOptions('webp', { quality: 80, lossless: true });
      
      expect(options).toEqual({
        quality: 80,
        alphaQuality: 100,
        lossless: true,
        nearLossless: false
      });
    });

    test('should use default quality when not specified', () => {
      const transformer = new ImageTransformer({ quality: 75 });
      const options = transformer.getFormatOptions('jpeg', {});
      
      expect(options.quality).toBe(75);
    });
  });

  describe('MIME Type to Format Conversion', () => {
    test('should convert MIME types to format strings', () => {
      expect(imageTransformer.getFormatFromMimeType('image/jpeg')).toBe('jpeg');
      expect(imageTransformer.getFormatFromMimeType('image/png')).toBe('png');
      expect(imageTransformer.getFormatFromMimeType('image/webp')).toBe('webp');
      expect(imageTransformer.getFormatFromMimeType('image/avif')).toBe('avif');
      expect(imageTransformer.getFormatFromMimeType('image/tiff')).toBe('tiff');
    });

    test('should return null for unsupported MIME types', () => {
      expect(imageTransformer.getFormatFromMimeType('image/bmp')).toBeNull();
      expect(imageTransformer.getFormatFromMimeType('text/plain')).toBeNull();
    });
  });

  describe('Input Validation', () => {
    test('should validate supported input types', () => {
      expect(imageTransformer.supportsInputType('image/jpeg')).toBe(true);
      expect(imageTransformer.supportsInputType('image/png')).toBe(true);
      expect(imageTransformer.supportsInputType('image/gif')).toBe(true);
      expect(imageTransformer.supportsInputType('text/plain')).toBe(false);
    });

    test('should validate supported output types', () => {
      expect(imageTransformer.supportsOutputType('image/jpeg')).toBe(true);
      expect(imageTransformer.supportsOutputType('image/webp')).toBe(true);
      expect(imageTransformer.supportsOutputType('image/gif')).toBe(false); // Not in output types
    });

    test('should validate input data', () => {
      const validation1 = imageTransformer.validateInput('test-data', 'image/jpeg', 'image/png');
      expect(validation1.success).toBe(true);

      const validation2 = imageTransformer.validateInput(null, 'image/jpeg', 'image/png');
      expect(validation2.success).toBe(false);
      expect(validation2.error).toContain('Input data is required');

      const validation3 = imageTransformer.validateInput('test-data', 'text/plain', 'image/png');
      expect(validation3.success).toBe(false);
      expect(validation3.error).toContain('Unsupported input type');
    });
  });

  describe('Responsive Size Generation', () => {
    test('should generate size configuration for responsive images', async () => {
      const sizes = [
        { width: 320, height: 240, suffix: 'small' },
        { width: 640, height: 480, suffix: 'medium' },
        { width: 1280, height: 960, suffix: 'large' }
      ];

      // Mock the transform method to avoid actual image processing
      const originalTransform = imageTransformer.transform;
      imageTransformer.transform = jest.fn().mockResolvedValue({
        success: true,
        data: Buffer.from('mock-image-data'),
        metadata: { width: 320, height: 240, size: 1024 }
      });

      const result = await imageTransformer.generateResponsiveSizes('mock-input', 'image/jpeg', sizes);

      expect(result.success).toBe(true);
      expect(result.sizes).toHaveLength(3);
      expect(result.sizes[0]).toEqual(expect.objectContaining({
        size: 'small',
        width: 320,
        height: 240
      }));

      // Restore original method
      imageTransformer.transform = originalTransform;
    });
  });
});
