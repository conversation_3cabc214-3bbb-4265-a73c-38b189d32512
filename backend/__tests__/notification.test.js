const request = require('supertest');
const app = require('../app');
const supabase = require('../config/supabase');
const notificationController = require('../controllers/notificationController');

// Mock Supabase
jest.mock('../config/supabase', () => ({
  auth: {
    getUser: jest.fn()
  },
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  single: jest.fn(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  range: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis()
}));

// Mock nodemailer
jest.mock('nodemailer', () => ({
  createTransport: jest.fn().mockReturnValue({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'mock-message-id' })
  }),
  createTestAccount: jest.fn().mockResolvedValue({
    user: '<EMAIL>',
    pass: 'testpassword'
  }),
  getTestMessageUrl: jest.fn().mockReturnValue('https://ethereal.email/message/mock')
}));

describe('Notification API', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/notifications', () => {
    it('should return user notifications', async () => {
      // Mock authentication
      supabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } },
        error: null
      });

      // Mock notifications query
      supabase.from().select().eq().order().range.mockResolvedValue({
        data: [
          {
            id: 'notif1',
            title: 'Test Notification',
            message: 'This is a test notification',
            type: 'info',
            is_read: false,
            created_at: new Date().toISOString()
          }
        ],
        error: null
      });

      // Mock count query
      supabase.from().select().eq.mockResolvedValueOnce({
        count: 5,
        error: null
      });

      // Mock unread count query
      supabase.from().select().eq().eq.mockResolvedValueOnce({
        count: 2,
        error: null
      });

      const res = await request(app)
        .get('/api/notifications')
        .set('Authorization', 'Bearer valid-token');

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('notifications');
      expect(res.body).toHaveProperty('unread_count');
      expect(res.body).toHaveProperty('pagination');
    });
  });

  describe('POST /api/notifications', () => {
    it('should create a new notification', async () => {
      // Mock authentication
      supabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'admin123' } },
        error: null
      });

      // Mock notification creation
      supabase.from().insert().select().single.mockResolvedValue({
        data: {
          id: 'new-notif',
          sender_id: 'admin123',
          recipient_id: 'user123',
          title: 'New Notification',
          message: 'This is a new notification',
          type: 'info',
          is_read: false,
          created_at: new Date().toISOString()
        },
        error: null
      });

      // Mock recipient query
      supabase.from().select().eq().single.mockResolvedValue({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
          preferences: { notifications: { email: true } }
        },
        error: null
      });

      const res = await request(app)
        .post('/api/notifications')
        .set('Authorization', 'Bearer valid-token')
        .send({
          recipient_id: 'user123',
          type: 'info',
          title: 'New Notification',
          message: 'This is a new notification',
          send_email: true
        });

      expect(res.statusCode).toEqual(201);
      expect(res.body).toHaveProperty('notification');
      expect(res.body.message).toEqual('Notification created successfully');
    });
  });

  describe('PUT /api/notifications/:id/read', () => {
    it('should mark a notification as read', async () => {
      // Mock authentication
      supabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } },
        error: null
      });

      // Mock notification query
      supabase.from().select().eq().single.mockResolvedValue({
        data: { recipient_id: 'user123' },
        error: null
      });

      // Mock notification update
      supabase.from().update().eq.mockResolvedValue({
        error: null
      });

      const res = await request(app)
        .put('/api/notifications/notif1/read')
        .set('Authorization', 'Bearer valid-token');

      expect(res.statusCode).toEqual(200);
      expect(res.body.message).toEqual('Notification marked as read');
    });
  });

  describe('PUT /api/notifications/read-all', () => {
    it('should mark all notifications as read', async () => {
      // Mock authentication
      supabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } },
        error: null
      });

      // Mock notification update
      supabase.from().update().eq().eq.mockResolvedValue({
        error: null
      });

      const res = await request(app)
        .put('/api/notifications/read-all')
        .set('Authorization', 'Bearer valid-token');

      expect(res.statusCode).toEqual(200);
      expect(res.body.message).toEqual('All notifications marked as read');
    });
  });

  describe('DELETE /api/notifications/:id', () => {
    it('should delete a notification', async () => {
      // Mock authentication
      supabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } },
        error: null
      });

      // Mock notification query
      supabase.from().select().eq().single.mockResolvedValue({
        data: { recipient_id: 'user123' },
        error: null
      });

      // Mock notification delete
      supabase.from().delete().eq.mockResolvedValue({
        error: null
      });

      const res = await request(app)
        .delete('/api/notifications/notif1')
        .set('Authorization', 'Bearer valid-token');

      expect(res.statusCode).toEqual(200);
      expect(res.body.message).toEqual('Notification deleted successfully');
    });
  });

  describe('createSystemNotifications function', () => {
    it('should create system notifications for multiple users', async () => {
      // Mock notification creation
      supabase.from().insert().select.mockResolvedValue({
        data: [
          {
            id: 'sys-notif-1',
            recipient_id: 'user1',
            type: 'system',
            title: 'System Notification',
            message: 'This is a system notification',
            is_read: false
          },
          {
            id: 'sys-notif-2',
            recipient_id: 'user2',
            type: 'system',
            title: 'System Notification',
            message: 'This is a system notification',
            is_read: false
          }
        ],
        error: null
      });

      // Mock recipients query
      supabase.from().select().in.mockResolvedValue({
        data: [
          {
            id: 'user1',
            email: '<EMAIL>',
            name: 'User One',
            preferences: { notifications: { email: true } }
          },
          {
            id: 'user2',
            email: '<EMAIL>',
            name: 'User Two',
            preferences: { notifications: { email: false } }
          }
        ],
        error: null
      });

      const result = await notificationController.createSystemNotifications(
        ['user1', 'user2'],
        'system',
        'System Notification',
        'This is a system notification',
        {},
        true
      );

      expect(result.success).toBe(true);
      expect(result.notifications).toHaveLength(2);
    });
  });
});
