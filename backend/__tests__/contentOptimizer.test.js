const ContentOptimizer = require('../services/transform/text/contentOptimizer');

describe('Content Optimizer', () => {
  let optimizer;

  beforeEach(() => {
    optimizer = new ContentOptimizer();
  });

  describe('Content Analysis', () => {
    test('should analyze basic content structure', () => {
      const content = 'This is a test post. #testing #contentforge @user1 Check out https://example.com for more info!';
      const analysis = optimizer.analyzeContent(content);

      expect(analysis.length).toBe(content.length);
      expect(analysis.wordCount).toBeGreaterThan(0);
      expect(analysis.sentenceCount).toBe(2);
      expect(analysis.hashtags).toEqual(['#testing', '#contentforge']);
      expect(analysis.mentions).toEqual(['@user1']);
      expect(analysis.urls).toEqual(['https://example.com']);
    });

    test('should extract keywords correctly', () => {
      const content = 'Content marketing is essential for business growth. Marketing strategies help businesses reach their target audience.';
      const analysis = optimizer.analyzeContent(content);

      expect(analysis.keywords).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ word: 'marketing' }),
          expect.objectContaining({ word: 'content' }),
          expect.objectContaining({ word: 'business' })
        ])
      );
    });

    test('should calculate readability score', () => {
      const simpleContent = 'This is easy to read. Short sentences work well.';
      const complexContent = 'The implementation of sophisticated algorithmic methodologies necessitates comprehensive understanding of multifaceted computational paradigms.';

      const simpleAnalysis = optimizer.analyzeContent(simpleContent);
      const complexAnalysis = optimizer.analyzeContent(complexContent);

      expect(simpleAnalysis.readabilityScore).toBeGreaterThan(complexAnalysis.readabilityScore);
    });
  });

  describe('Platform Optimization', () => {
    test('should optimize content for Twitter', () => {
      const longContent = 'This is a very long tweet that exceeds the character limit for Twitter and needs to be truncated to fit within the platform constraints while maintaining readability and engagement.';
      
      const result = optimizer.optimizeForPlatform(longContent, 'twitter');
      
      expect(result.success).toBe(true);
      expect(result.optimizedContent.length).toBeLessThanOrEqual(280);
      expect(result.withinLimit).toBe(true);
    });

    test('should optimize content for Instagram', () => {
      const content = 'Check out this amazing photo! #photography #nature #beautiful';
      
      const result = optimizer.optimizeForPlatform(content, 'instagram');
      
      expect(result.success).toBe(true);
      expect(result.optimizedContent).toBe(content); // Should not be truncated
      expect(result.withinLimit).toBe(true);
    });

    test('should handle unsupported platforms', () => {
      const content = 'Test content';
      
      const result = optimizer.optimizeForPlatform(content, 'unsupported-platform');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Unsupported platform');
    });
  });

  describe('Hashtag Generation', () => {
    test('should generate relevant hashtags', () => {
      const content = 'Just finished an amazing workout at the gym. Feeling strong and motivated!';
      
      const hashtags = optimizer.generateHashtagSuggestions(content, 'instagram', 5);
      
      expect(hashtags).toHaveLength(5);
      expect(hashtags.every(tag => tag.startsWith('#'))).toBe(true);
    });

    test('should include platform-specific hashtags', () => {
      const content = 'Professional development tips for career growth';
      
      const hashtags = optimizer.generateHashtagSuggestions(content, 'linkedin', 3);
      
      expect(hashtags).toContain('#professional');
    });
  });

  describe('Quality Scoring', () => {
    test('should score high-quality content highly', () => {
      const goodContent = 'Are you ready to transform your business? Our innovative solutions help companies achieve remarkable growth. #business #innovation #growth';
      
      const score = optimizer.calculateQualityScore(goodContent, 'linkedin');
      
      expect(score.score).toBeGreaterThan(70);
      expect(score.grade).toMatch(/[A-C]/);
    });

    test('should score low-quality content poorly', () => {
      const poorContent = 'a';
      
      const score = optimizer.calculateQualityScore(poorContent, 'twitter');
      
      expect(score.score).toBeLessThan(50);
      expect(score.grade).toMatch(/[D-F]/);
    });

    test('should provide recommendations for improvement', () => {
      const content = 'This is a very long post that exceeds the character limit for most social media platforms and needs significant improvement to be effective.'.repeat(10);
      
      const score = optimizer.calculateQualityScore(content, 'twitter');
      
      expect(score.recommendations).toContain(expect.stringContaining('character limit'));
    });
  });

  describe('Sentiment Analysis', () => {
    test('should detect positive sentiment', () => {
      const positiveContent = 'I love this amazing product! It\'s fantastic and wonderful.';
      const analysis = optimizer.analyzeContent(positiveContent);
      
      expect(analysis.sentiment.sentiment).toBe('positive');
      expect(analysis.sentiment.confidence).toBeGreaterThan(0.5);
    });

    test('should detect negative sentiment', () => {
      const negativeContent = 'This is terrible and awful. I hate it and feel disappointed.';
      const analysis = optimizer.analyzeContent(negativeContent);
      
      expect(analysis.sentiment.sentiment).toBe('negative');
      expect(analysis.sentiment.confidence).toBeGreaterThan(0.5);
    });

    test('should detect neutral sentiment', () => {
      const neutralContent = 'The weather today is partly cloudy with temperatures around 20 degrees.';
      const analysis = optimizer.analyzeContent(neutralContent);
      
      expect(analysis.sentiment.sentiment).toBe('neutral');
    });
  });

  describe('Content Truncation', () => {
    test('should truncate at sentence boundaries when possible', () => {
      const content = 'First sentence is short. This is a much longer second sentence that should be truncated.';
      
      const result = optimizer.optimizeForTwitter(content, { maxLength: 50 }, {});
      
      expect(result).toContain('First sentence is short.');
      expect(result).not.toContain('This is a much longer');
    });

    test('should truncate at word boundaries when sentence truncation fails', () => {
      const content = 'This is one very long sentence that cannot be truncated at sentence boundaries and must be cut at word boundaries instead.';
      
      const result = optimizer.optimizeForTwitter(content, { maxLength: 50 }, {});
      
      expect(result.length).toBeLessThanOrEqual(50);
      expect(result).toEndWith('...');
      expect(result.split(' ').every(word => word.length > 0)).toBe(true); // No broken words
    });
  });
});
