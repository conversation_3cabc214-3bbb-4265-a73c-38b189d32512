{"name": "backend", "version": "0.1.0", "main": "index.js", "license": "MIT", "scripts": {"dev": "nodemon index.js", "start": "node index.js", "lint": "eslint .", "test": "NODE_ENV=test jest --setupFilesAfterEnv=./jest.setup.js", "setup-n8n": "node scripts/setup-n8n.js", "setup": "npm run setup-n8n"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "axios": "^1.6.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.3", "fluent-ffmpeg": "^2.1.2", "form-data": "^4.0.2", "googleapis": "^149.0.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.12", "sharp": "^0.33.3", "uuid": "^9.0.1", "ws": "^8.18.2"}, "devDependencies": {"eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "supertest": "^6.3.4"}}