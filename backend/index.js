const app = require('./app');
const queueManager = require('./services/queue/queueManager');
const schedulingService = require('./services/schedulingService');
require('dotenv').config();

const PORT = process.env.PORT || 4000;

// Initialize and start services
async function startServer() {
  try {
    // Initialize queue manager
    const queueInitialized = await queueManager.initialize();
    if (queueInitialized) {
      queueManager.startAll();
      console.log('Queue Manager started successfully');
    } else {
      console.warn('Queue Manager failed to initialize, continuing without background processing');
    }

    // Start server
    app.listen(PORT, () => {
      console.log(`ContentForge backend server running on port ${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);

      // Start the scheduling service
      schedulingService.start();
      console.log('Scheduling service started');
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  schedulingService.stop();
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  schedulingService.stop();
  process.exit(0);
});
