// Jest setup file for loading test environment variables
require('dotenv').config({ path: '.env.test' });

// Mock Supabase client for testing
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: null, error: null })),
          order: jest.fn(() => Promise.resolve({ data: [], error: null }))
        })),
        insert: jest.fn(() => ({
          select: jest.fn(() => Promise.resolve({ data: [], error: null }))
        })),
        update: jest.fn(() => Promise.resolve({ data: null, error: null })),
        delete: jest.fn(() => Promise.resolve({ data: null, error: null }))
      }))
    }))
  }))
}));

// Mock Google APIs
jest.mock('googleapis', () => ({
  google: {
    auth: {
      OAuth2: jest.fn(() => ({
        generateAuthUrl: jest.fn(() => 'https://mock-auth-url.com'),
        getToken: jest.fn(() => Promise.resolve({ tokens: { access_token: 'mock-token' } })),
        setCredentials: jest.fn(),
        refreshAccessToken: jest.fn(() => Promise.resolve({ credentials: { access_token: 'new-token' } }))
      }))
    },
    youtube: jest.fn(() => ({
      channels: {
        list: jest.fn(() => Promise.resolve({
          data: {
            items: [{ id: 'channel-id', snippet: { title: 'Test Channel' } }]
          }
        }))
      },
      videos: {
        insert: jest.fn(() => Promise.resolve({
          data: { id: 'video-id' }
        }))
      }
    }))
  }
}));

// Mock Axios for API calls
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn(() => Promise.resolve({ data: {} })),
    post: jest.fn(() => Promise.resolve({ data: {} })),
    put: jest.fn(() => Promise.resolve({ data: {} })),
    delete: jest.fn(() => Promise.resolve({ data: {} }))
  })),
  get: jest.fn(() => Promise.resolve({ data: {} })),
  post: jest.fn(() => Promise.resolve({ data: {} }))
}));
