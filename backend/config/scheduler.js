const analyticsDataCollector = require('../services/analyticsDataCollector');

class AnalyticsScheduler {
  constructor() {
    this.intervals = new Map();
    this.isRunning = false;
  }

  /**
   * Start the analytics collection scheduler
   */
  start() {
    if (this.isRunning) {
      console.log('Analytics scheduler is already running');
      return;
    }

    console.log('Starting analytics collection scheduler...');
    this.isRunning = true;

    // Schedule hourly analytics collection for recent content
    this.scheduleHourlyCollection();
    
    // Schedule daily comprehensive analytics collection
    this.scheduleDailyCollection();
    
    // Schedule weekly analytics cleanup
    this.scheduleWeeklyCleanup();

    console.log('Analytics scheduler started successfully');
  }

  /**
   * Stop the analytics collection scheduler
   */
  stop() {
    if (!this.isRunning) {
      console.log('Analytics scheduler is not running');
      return;
    }

    console.log('Stopping analytics collection scheduler...');
    
    // Clear all intervals
    this.intervals.forEach((interval, name) => {
      clearInterval(interval);
      console.log(`Stopped ${name} scheduler`);
    });
    
    this.intervals.clear();
    this.isRunning = false;

    console.log('Analytics scheduler stopped successfully');
  }

  /**
   * Schedule hourly analytics collection for recent content (last 24 hours)
   */
  scheduleHourlyCollection() {
    const hourlyInterval = setInterval(async () => {
      try {
        console.log('Starting hourly analytics collection...');
        const result = await analyticsDataCollector.collectAllAnalytics(1); // Last 1 day
        
        if (result.success) {
          console.log(`Hourly collection completed: ${result.totalAnalyticsCollected} analytics collected for ${result.usersProcessed} users`);
        } else {
          console.error('Hourly analytics collection failed:', result.error);
        }
      } catch (error) {
        console.error('Hourly analytics collection error:', error);
      }
    }, 60 * 60 * 1000); // Every hour

    this.intervals.set('hourly', hourlyInterval);
    console.log('Scheduled hourly analytics collection');
  }

  /**
   * Schedule daily comprehensive analytics collection (last 7 days)
   */
  scheduleDailyCollection() {
    const dailyInterval = setInterval(async () => {
      try {
        console.log('Starting daily comprehensive analytics collection...');
        const result = await analyticsDataCollector.collectAllAnalytics(7); // Last 7 days
        
        if (result.success) {
          console.log(`Daily collection completed: ${result.totalAnalyticsCollected} analytics collected for ${result.usersProcessed} users`);
          
          // Generate daily summary report
          await this.generateDailySummary(result);
        } else {
          console.error('Daily analytics collection failed:', result.error);
        }
      } catch (error) {
        console.error('Daily analytics collection error:', error);
      }
    }, 24 * 60 * 60 * 1000); // Every 24 hours

    this.intervals.set('daily', dailyInterval);
    console.log('Scheduled daily analytics collection');
  }

  /**
   * Schedule weekly analytics data cleanup
   */
  scheduleWeeklyCleanup() {
    const weeklyInterval = setInterval(async () => {
      try {
        console.log('Starting weekly analytics cleanup...');
        await this.cleanupOldAnalytics();
        console.log('Weekly analytics cleanup completed');
      } catch (error) {
        console.error('Weekly analytics cleanup error:', error);
      }
    }, 7 * 24 * 60 * 60 * 1000); // Every 7 days

    this.intervals.set('weekly', weeklyInterval);
    console.log('Scheduled weekly analytics cleanup');
  }

  /**
   * Manual trigger for analytics collection
   * @param {string} userId - Optional user ID to collect for specific user
   * @param {number} daysBack - Number of days to look back
   * @returns {Object} Collection result
   */
  async triggerCollection(userId = null, daysBack = 7) {
    try {
      console.log(`Manually triggering analytics collection${userId ? ` for user ${userId}` : ' for all users'}...`);
      
      let result;
      if (userId) {
        result = await analyticsDataCollector.collectAllUserAnalytics(userId, daysBack);
      } else {
        result = await analyticsDataCollector.collectAllAnalytics(daysBack);
      }

      console.log('Manual analytics collection completed:', result);
      return result;
    } catch (error) {
      console.error('Manual analytics collection error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate daily summary report
   * @param {Object} collectionResult - Collection result data
   */
  async generateDailySummary(collectionResult) {
    try {
      const summary = {
        date: new Date().toISOString().split('T')[0],
        usersProcessed: collectionResult.usersProcessed,
        totalAnalyticsCollected: collectionResult.totalAnalyticsCollected,
        totalFailed: collectionResult.totalFailed,
        successRate: collectionResult.totalAnalyticsCollected > 0 ? 
          ((collectionResult.totalAnalyticsCollected / (collectionResult.totalAnalyticsCollected + collectionResult.totalFailed)) * 100).toFixed(2) : 0,
        timestamp: new Date().toISOString()
      };

      // Store summary in database (you might want to create a collection_summaries table)
      console.log('Daily Analytics Summary:', summary);
      
      // Here you could also send notifications to admins about collection status
      if (collectionResult.totalFailed > 0) {
        console.warn(`⚠️  ${collectionResult.totalFailed} analytics collections failed today`);
      }

    } catch (error) {
      console.error('Failed to generate daily summary:', error);
    }
  }

  /**
   * Clean up old analytics data (older than retention period)
   */
  async cleanupOldAnalytics() {
    try {
      const supabase = require('./supabase');
      
      // Keep analytics data for 90 days (configurable)
      const retentionDays = process.env.ANALYTICS_RETENTION_DAYS || 90;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const { data, error } = await supabase
        .from('analytics')
        .delete()
        .lt('date', cutoffDate.toISOString().split('T')[0]);

      if (error) {
        throw new Error(`Failed to cleanup old analytics: ${error.message}`);
      }

      console.log(`Cleaned up analytics data older than ${retentionDays} days`);
      
    } catch (error) {
      console.error('Analytics cleanup error:', error);
    }
  }

  /**
   * Get scheduler status
   * @returns {Object} Scheduler status information
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeSchedules: Array.from(this.intervals.keys()),
      nextHourlyRun: this.isRunning ? new Date(Date.now() + (60 * 60 * 1000)) : null,
      nextDailyRun: this.isRunning ? new Date(Date.now() + (24 * 60 * 60 * 1000)) : null,
      nextWeeklyRun: this.isRunning ? new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)) : null
    };
  }

  /**
   * Update collection frequency
   * @param {string} type - Collection type (hourly, daily, weekly)
   * @param {number} intervalMs - New interval in milliseconds
   */
  updateSchedule(type, intervalMs) {
    if (!this.isRunning) {
      throw new Error('Scheduler is not running');
    }

    if (this.intervals.has(type)) {
      clearInterval(this.intervals.get(type));
      this.intervals.delete(type);
    }

    let collectionFunction;
    switch (type) {
      case 'hourly':
        collectionFunction = () => analyticsDataCollector.collectAllAnalytics(1);
        break;
      case 'daily':
        collectionFunction = () => analyticsDataCollector.collectAllAnalytics(7);
        break;
      case 'weekly':
        collectionFunction = () => this.cleanupOldAnalytics();
        break;
      default:
        throw new Error(`Unknown schedule type: ${type}`);
    }

    const newInterval = setInterval(async () => {
      try {
        console.log(`Starting ${type} analytics collection...`);
        await collectionFunction();
        console.log(`${type} analytics collection completed`);
      } catch (error) {
        console.error(`${type} analytics collection error:`, error);
      }
    }, intervalMs);

    this.intervals.set(type, newInterval);
    console.log(`Updated ${type} schedule to run every ${intervalMs}ms`);
  }
}

module.exports = new AnalyticsScheduler();
