const supabase = require('../config/supabase');
const { google } = require('googleapis');
const axios = require('axios');

/**
 * OAuth Controller for handling platform authentication
 */

/**
 * Get OAuth authorization URL for a platform
 */
exports.getAuthUrl = async (req, res) => {
  try {
    const { platform } = req.params;
    const { user } = req;
    
    if (!platform) {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: 'Platform parameter is required' 
      });
    }

    let authUrl;
    let state = `${user.id}:${platform}:${Date.now()}`;

    switch (platform.toLowerCase()) {
      case 'youtube':
        authUrl = await getYouTubeAuthUrl(state);
        break;
      case 'instagram':
        authUrl = await getInstagramAuthUrl(state);
        break;
      case 'linkedin':
        authUrl = await getLinkedInAuthUrl(state);
        break;
      default:
        return res.status(400).json({ 
          error: 'Unsupported Platform', 
          message: `Platform '${platform}' is not supported` 
        });
    }

    res.json({
      success: true,
      authUrl,
      state,
      platform
    });
  } catch (error) {
    console.error('OAuth auth URL error:', error);
    res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to generate authorization URL' 
    });
  }
};

/**
 * Handle OAuth callback and exchange code for tokens
 */
exports.handleCallback = async (req, res) => {
  try {
    const { platform } = req.params;
    const { code, state, error: oauthError } = req.query;

    if (oauthError) {
      return res.status(400).json({ 
        error: 'OAuth Error', 
        message: `OAuth authorization failed: ${oauthError}` 
      });
    }

    if (!code || !state) {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: 'Authorization code and state are required' 
      });
    }

    // Verify state parameter
    const [userId, platformFromState, timestamp] = state.split(':');
    if (platformFromState !== platform) {
      return res.status(400).json({ 
        error: 'Invalid State', 
        message: 'State parameter mismatch' 
      });
    }

    // Check if state is not too old (5 minutes)
    const stateAge = Date.now() - parseInt(timestamp);
    if (stateAge > 5 * 60 * 1000) {
      return res.status(400).json({ 
        error: 'Expired State', 
        message: 'Authorization state has expired' 
      });
    }

    let tokenData;
    switch (platform.toLowerCase()) {
      case 'youtube':
        tokenData = await exchangeYouTubeCode(code);
        break;
      case 'instagram':
        tokenData = await exchangeInstagramCode(code);
        break;
      case 'linkedin':
        tokenData = await exchangeLinkedInCode(code);
        break;
      default:
        return res.status(400).json({ 
          error: 'Unsupported Platform', 
          message: `Platform '${platform}' is not supported` 
        });
    }

    // Store tokens in database
    const { data: existingToken, error: fetchError } = await supabase
      .from('oauth_tokens')
      .select('id')
      .eq('user_id', userId)
      .eq('platform', platform.toLowerCase())
      .single();

    const tokenRecord = {
      user_id: userId,
      platform: platform.toLowerCase(),
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      token_type: tokenData.token_type || 'Bearer',
      expires_at: tokenData.expires_at ? new Date(tokenData.expires_at) : null,
      scope: tokenData.scope,
      platform_user_id: tokenData.platform_user_id,
      platform_username: tokenData.platform_username,
      is_active: true
    };

    if (existingToken) {
      // Update existing token
      const { error: updateError } = await supabase
        .from('oauth_tokens')
        .update(tokenRecord)
        .eq('id', existingToken.id);

      if (updateError) {
        console.error('Token update error:', updateError);
        return res.status(500).json({ 
          error: 'Database Error', 
          message: 'Failed to update OAuth tokens' 
        });
      }
    } else {
      // Insert new token
      const { error: insertError } = await supabase
        .from('oauth_tokens')
        .insert(tokenRecord);

      if (insertError) {
        console.error('Token insert error:', insertError);
        return res.status(500).json({ 
          error: 'Database Error', 
          message: 'Failed to store OAuth tokens' 
        });
      }
    }

    // Redirect to success page or return success response
    res.json({
      success: true,
      message: `Successfully connected to ${platform}`,
      platform,
      user_info: {
        platform_user_id: tokenData.platform_user_id,
        platform_username: tokenData.platform_username
      }
    });
  } catch (error) {
    console.error('OAuth callback error:', error);
    res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to process OAuth callback' 
    });
  }
};

/**
 * Get connected platforms for a user
 */
exports.getConnectedPlatforms = async (req, res) => {
  try {
    const { user } = req;

    const { data: tokens, error } = await supabase
      .from('oauth_tokens')
      .select('platform, platform_user_id, platform_username, is_active, created_at, expires_at')
      .eq('user_id', user.id)
      .eq('is_active', true);

    if (error) {
      console.error('Fetch connected platforms error:', error);
      return res.status(500).json({ 
        error: 'Database Error', 
        message: 'Failed to fetch connected platforms' 
      });
    }

    const connectedPlatforms = tokens.map(token => ({
      platform: token.platform,
      platform_user_id: token.platform_user_id,
      platform_username: token.platform_username,
      connected_at: token.created_at,
      expires_at: token.expires_at,
      is_expired: token.expires_at ? new Date(token.expires_at) < new Date() : false
    }));

    res.json({
      success: true,
      connected_platforms: connectedPlatforms
    });
  } catch (error) {
    console.error('Get connected platforms error:', error);
    res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to fetch connected platforms' 
    });
  }
};

/**
 * Disconnect a platform
 */
exports.disconnectPlatform = async (req, res) => {
  try {
    const { platform } = req.params;
    const { user } = req;

    const { error } = await supabase
      .from('oauth_tokens')
      .update({ is_active: false })
      .eq('user_id', user.id)
      .eq('platform', platform.toLowerCase());

    if (error) {
      console.error('Disconnect platform error:', error);
      return res.status(500).json({ 
        error: 'Database Error', 
        message: 'Failed to disconnect platform' 
      });
    }

    res.json({
      success: true,
      message: `Successfully disconnected from ${platform}`
    });
  } catch (error) {
    console.error('Disconnect platform error:', error);
    res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to disconnect platform' 
    });
  }
};

/**
 * Refresh OAuth tokens
 */
exports.refreshTokens = async (req, res) => {
  try {
    const { platform } = req.params;
    const { user } = req;

    // Get current token
    const { data: tokenRecord, error: fetchError } = await supabase
      .from('oauth_tokens')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', platform.toLowerCase())
      .eq('is_active', true)
      .single();

    if (fetchError || !tokenRecord) {
      return res.status(404).json({ 
        error: 'Not Found', 
        message: 'No active OAuth token found for this platform' 
      });
    }

    if (!tokenRecord.refresh_token) {
      return res.status(400).json({ 
        error: 'No Refresh Token', 
        message: 'No refresh token available for this platform' 
      });
    }

    let newTokenData;
    switch (platform.toLowerCase()) {
      case 'youtube':
        newTokenData = await refreshYouTubeToken(tokenRecord.refresh_token);
        break;
      case 'instagram':
        newTokenData = await refreshInstagramToken(tokenRecord.refresh_token);
        break;
      case 'linkedin':
        newTokenData = await refreshLinkedInToken(tokenRecord.refresh_token);
        break;
      default:
        return res.status(400).json({ 
          error: 'Unsupported Platform', 
          message: `Platform '${platform}' is not supported` 
        });
    }

    // Update token in database
    const updateData = {
      access_token: newTokenData.access_token,
      expires_at: newTokenData.expires_at ? new Date(newTokenData.expires_at) : null
    };

    if (newTokenData.refresh_token) {
      updateData.refresh_token = newTokenData.refresh_token;
    }

    const { error: updateError } = await supabase
      .from('oauth_tokens')
      .update(updateData)
      .eq('id', tokenRecord.id);

    if (updateError) {
      console.error('Token refresh update error:', updateError);
      return res.status(500).json({ 
        error: 'Database Error', 
        message: 'Failed to update refreshed tokens' 
      });
    }

    res.json({
      success: true,
      message: `Successfully refreshed ${platform} tokens`,
      expires_at: updateData.expires_at
    });
  } catch (error) {
    console.error('Refresh tokens error:', error);
    res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to refresh tokens' 
    });
  }
};

// Helper functions for platform-specific OAuth flows

async function getYouTubeAuthUrl(state) {
  const oauth2Client = new google.auth.OAuth2(
    process.env.YOUTUBE_CLIENT_ID,
    process.env.YOUTUBE_CLIENT_SECRET,
    process.env.YOUTUBE_REDIRECT_URI
  );

  const scopes = [
    'https://www.googleapis.com/auth/youtube.upload',
    'https://www.googleapis.com/auth/youtube',
    'https://www.googleapis.com/auth/youtube.readonly'
  ];

  return oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: scopes,
    state: state,
    prompt: 'consent'
  });
}

async function exchangeYouTubeCode(code) {
  const oauth2Client = new google.auth.OAuth2(
    process.env.YOUTUBE_CLIENT_ID,
    process.env.YOUTUBE_CLIENT_SECRET,
    process.env.YOUTUBE_REDIRECT_URI
  );

  const { tokens } = await oauth2Client.getToken(code);
  
  // Get user info
  oauth2Client.setCredentials(tokens);
  const youtube = google.youtube({ version: 'v3', auth: oauth2Client });
  const channelResponse = await youtube.channels.list({
    part: 'snippet',
    mine: true
  });

  const channel = channelResponse.data.items[0];

  return {
    access_token: tokens.access_token,
    refresh_token: tokens.refresh_token,
    token_type: tokens.token_type,
    expires_at: tokens.expiry_date,
    scope: tokens.scope,
    platform_user_id: channel.id,
    platform_username: channel.snippet.title
  };
}

async function refreshYouTubeToken(refreshToken) {
  const oauth2Client = new google.auth.OAuth2(
    process.env.YOUTUBE_CLIENT_ID,
    process.env.YOUTUBE_CLIENT_SECRET,
    process.env.YOUTUBE_REDIRECT_URI
  );

  oauth2Client.setCredentials({ refresh_token: refreshToken });
  const { credentials } = await oauth2Client.refreshAccessToken();

  return {
    access_token: credentials.access_token,
    refresh_token: credentials.refresh_token || refreshToken,
    expires_at: credentials.expiry_date
  };
}

async function getInstagramAuthUrl(state) {
  const params = new URLSearchParams({
    client_id: process.env.INSTAGRAM_CLIENT_ID,
    redirect_uri: process.env.INSTAGRAM_REDIRECT_URI,
    scope: 'user_profile,user_media',
    response_type: 'code',
    state: state
  });

  return `https://api.instagram.com/oauth/authorize?${params.toString()}`;
}

async function exchangeInstagramCode(code) {
  // Step 1: Exchange code for short-lived token
  const tokenResponse = await axios.post('https://api.instagram.com/oauth/access_token', {
    client_id: process.env.INSTAGRAM_CLIENT_ID,
    client_secret: process.env.INSTAGRAM_CLIENT_SECRET,
    grant_type: 'authorization_code',
    redirect_uri: process.env.INSTAGRAM_REDIRECT_URI,
    code: code
  });

  const shortLivedToken = tokenResponse.data.access_token;
  const userId = tokenResponse.data.user_id;

  // Step 2: Exchange for long-lived token
  const longLivedResponse = await axios.get('https://graph.instagram.com/access_token', {
    params: {
      grant_type: 'ig_exchange_token',
      client_secret: process.env.INSTAGRAM_CLIENT_SECRET,
      access_token: shortLivedToken
    }
  });

  // Step 3: Get user info
  const userResponse = await axios.get(`https://graph.instagram.com/${userId}`, {
    params: {
      fields: 'id,username',
      access_token: longLivedResponse.data.access_token
    }
  });

  return {
    access_token: longLivedResponse.data.access_token,
    token_type: longLivedResponse.data.token_type,
    expires_at: Date.now() + (longLivedResponse.data.expires_in * 1000),
    platform_user_id: userResponse.data.id,
    platform_username: userResponse.data.username
  };
}

async function refreshInstagramToken(accessToken) {
  const response = await axios.get('https://graph.instagram.com/refresh_access_token', {
    params: {
      grant_type: 'ig_refresh_token',
      access_token: accessToken
    }
  });

  return {
    access_token: response.data.access_token,
    expires_at: Date.now() + (response.data.expires_in * 1000)
  };
}

async function getLinkedInAuthUrl(state) {
  const params = new URLSearchParams({
    response_type: 'code',
    client_id: process.env.LINKEDIN_CLIENT_ID,
    redirect_uri: process.env.LINKEDIN_REDIRECT_URI,
    state: state,
    scope: 'r_liteprofile r_emailaddress w_member_social'
  });

  return `https://www.linkedin.com/oauth/v2/authorization?${params.toString()}`;
}

async function exchangeLinkedInCode(code) {
  // Exchange code for token
  const tokenResponse = await axios.post('https://www.linkedin.com/oauth/v2/accessToken', {
    grant_type: 'authorization_code',
    code: code,
    redirect_uri: process.env.LINKEDIN_REDIRECT_URI,
    client_id: process.env.LINKEDIN_CLIENT_ID,
    client_secret: process.env.LINKEDIN_CLIENT_SECRET
  }, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });

  // Get user info
  const userResponse = await axios.get('https://api.linkedin.com/v2/me', {
    headers: {
      'Authorization': `Bearer ${tokenResponse.data.access_token}`
    }
  });

  return {
    access_token: tokenResponse.data.access_token,
    refresh_token: tokenResponse.data.refresh_token,
    token_type: tokenResponse.data.token_type,
    expires_at: Date.now() + (tokenResponse.data.expires_in * 1000),
    scope: tokenResponse.data.scope,
    platform_user_id: userResponse.data.id,
    platform_username: `${userResponse.data.localizedFirstName} ${userResponse.data.localizedLastName}`
  };
}

async function refreshLinkedInToken(refreshToken) {
  const response = await axios.post('https://www.linkedin.com/oauth/v2/accessToken', {
    grant_type: 'refresh_token',
    refresh_token: refreshToken,
    client_id: process.env.LINKEDIN_CLIENT_ID,
    client_secret: process.env.LINKEDIN_CLIENT_SECRET
  }, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });

  return {
    access_token: response.data.access_token,
    refresh_token: response.data.refresh_token || refreshToken,
    expires_at: Date.now() + (response.data.expires_in * 1000)
  };
}
