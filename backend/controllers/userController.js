const supabase = require('../config/supabase');
const authMiddleware = require('../middleware/authMiddleware');

/**
 * Register a new user
 */
exports.register = async (req, res) => {
  try {
    const { email, password, name } = req.body;

    if (!email || !password) {
      return res.status(400).json({ error: 'Bad Request', message: 'Email and password are required' });
    }

    // Register user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
    });

    if (authError) {
      return res.status(400).json({ error: 'Registration Failed', message: authError.message });
    }

    // Create user profile in the profiles table
    if (authData.user) {
      // Get the first user count to determine if this is the first user (admin)
      const { count, error: countError } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // Determine role - first user is admin, others are viewers by default
      const role = count === 0 ? 'admin' : 'viewer';

      const { error: profileError } = await supabase
        .from('profiles')
        .insert([
          {
            id: authData.user.id,
            name: name || email.split('@')[0],
            email,
            role,
            permissions: {},
            is_active: true,
            created_at: new Date(),
            updated_at: new Date()
          }
        ]);

      if (profileError) {
        console.error('Error creating user profile:', profileError);
        // We don't return an error here as the auth user was created successfully
      }
    }

    return res.status(201).json({
      message: 'User registered successfully',
      user: {
        id: authData.user.id,
        email: authData.user.email
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Registration failed' });
  }
};

/**
 * Login a user
 */
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ error: 'Bad Request', message: 'Email and password are required' });
    }

    // Sign in user with Supabase Auth
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      return res.status(401).json({ error: 'Authentication Failed', message: error.message });
    }

    // Get user profile including role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role, is_active')
      .eq('id', data.user.id)
      .single();

    // Check if user is active
    if (profile && profile.is_active === false) {
      // Sign out the user
      await supabase.auth.signOut();
      return res.status(403).json({ error: 'Account Deactivated', message: 'Your account has been deactivated. Please contact an administrator.' });
    }

    // Update last login timestamp
    await supabase
      .from('profiles')
      .update({ last_login: new Date() })
      .eq('id', data.user.id);

    return res.status(200).json({
      message: 'Login successful',
      user: {
        id: data.user.id,
        email: data.user.email,
        role: profile?.role || 'viewer'
      },
      session: {
        access_token: data.session.access_token,
        expires_at: data.session.expires_at
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Login failed' });
  }
};

/**
 * Get user profile
 */
exports.getProfile = async (req, res) => {
  try {
    const { user } = req;

    // Get user profile from the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) {
      return res.status(404).json({ error: 'Not Found', message: 'User profile not found' });
    }

    // Remove sensitive information
    const { password, ...profile } = data;

    return res.status(200).json({ profile });
  } catch (error) {
    console.error('Get profile error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch profile' });
  }
};

/**
 * Update user profile
 */
exports.updateProfile = async (req, res) => {
  try {
    const { user } = req;
    const { name, bio, avatar_url } = req.body;

    // Update user profile in the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .update({
        name,
        bio,
        avatar_url,
        updated_at: new Date()
      })
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }

    return res.status(200).json({
      message: 'Profile updated successfully',
      profile: data
    });
  } catch (error) {
    console.error('Update profile error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to update profile' });
  }
};

/**
 * Forgot password
 */
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Bad Request', message: 'Email is required' });
    }

    // Send password reset email
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.FRONTEND_URL}/reset-password`,
    });

    if (error) {
      return res.status(400).json({ error: 'Request Failed', message: error.message });
    }

    return res.status(200).json({ message: 'Password reset email sent' });
  } catch (error) {
    console.error('Forgot password error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to send reset email' });
  }
};

/**
 * Reset password
 */
exports.resetPassword = async (req, res) => {
  try {
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({ error: 'Bad Request', message: 'New password is required' });
    }

    // Update user password
    const { error } = await supabase.auth.updateUser({
      password
    });

    if (error) {
      return res.status(400).json({ error: 'Reset Failed', message: error.message });
    }

    return res.status(200).json({ message: 'Password reset successful' });
  } catch (error) {
    console.error('Reset password error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to reset password' });
  }
};

/**
 * Get user preferences
 */
exports.getPreferences = async (req, res) => {
  try {
    const { user } = req;

    // Get user preferences from the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .select('preferences')
      .eq('id', user.id)
      .single();

    if (error) {
      return res.status(404).json({ error: 'Not Found', message: 'User preferences not found' });
    }

    return res.status(200).json({ preferences: data.preferences || {} });
  } catch (error) {
    console.error('Get preferences error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch preferences' });
  }
};

/**
 * Update user preferences
 */
exports.updatePreferences = async (req, res) => {
  try {
    const { user } = req;
    const { preferences } = req.body;

    if (!preferences || typeof preferences !== 'object') {
      return res.status(400).json({ error: 'Bad Request', message: 'Valid preferences object is required' });
    }

    // Update user preferences in the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .update({
        preferences,
        updated_at: new Date()
      })
      .eq('id', user.id)
      .select('preferences')
      .single();

    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }

    return res.status(200).json({
      message: 'Preferences updated successfully',
      preferences: data.preferences
    });
  } catch (error) {
    console.error('Update preferences error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to update preferences' });
  }
};

/**
 * Get all users (admin only)
 */
exports.getAllUsers = async (req, res) => {
  try {
    const { limit = 20, page = 1, search } = req.query;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build query
    let query = supabase
      .from('profiles')
      .select('id, name, email, role, created_at, updated_at, last_login')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Add search filter if provided
    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%`);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      return res.status(400).json({ error: 'Query Failed', message: error.message });
    }

    // Get total count for pagination
    const { count: totalCount, error: countError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Count error:', countError);
    }

    return res.status(200).json({
      users: data,
      pagination: {
        total: totalCount || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil((totalCount || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Get all users error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch users' });
  }
};

/**
 * Get user by ID (admin only)
 */
exports.getUserById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get user profile from the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .select('id, name, email, role, bio, avatar_url, preferences, permissions, created_at, updated_at, last_login')
      .eq('id', id)
      .single();

    if (error) {
      return res.status(404).json({ error: 'Not Found', message: 'User not found' });
    }

    return res.status(200).json({ user: data });
  } catch (error) {
    console.error('Get user by ID error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch user' });
  }
};

/**
 * Update user role (admin only)
 */
exports.updateUserRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { role } = req.body;

    // Validate role
    const validRoles = Object.keys(authMiddleware.getRoles());
    if (!role || !validRoles.includes(role)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Invalid role. Valid roles are: ${validRoles.join(', ')}`
      });
    }

    // Update user role in the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .update({
        role,
        updated_at: new Date()
      })
      .eq('id', id)
      .select('id, name, email, role')
      .single();

    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }

    return res.status(200).json({
      message: 'User role updated successfully',
      user: data
    });
  } catch (error) {
    console.error('Update user role error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to update user role' });
  }
};

/**
 * Update user permissions (admin only)
 */
exports.updateUserPermissions = async (req, res) => {
  try {
    const { id } = req.params;
    const { permissions } = req.body;

    if (!permissions || typeof permissions !== 'object') {
      return res.status(400).json({ error: 'Bad Request', message: 'Valid permissions object is required' });
    }

    // Get system permissions for validation
    const systemPermissions = authMiddleware.getPermissions();

    // Validate permissions structure
    for (const [resource, actions] of Object.entries(permissions)) {
      // Check if resource is valid
      if (!systemPermissions[resource]) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `Invalid resource: ${resource}. Valid resources are: ${Object.keys(systemPermissions).join(', ')}`
        });
      }

      // Check if actions are valid
      if (typeof actions !== 'object') {
        return res.status(400).json({
          error: 'Bad Request',
          message: `Actions for resource ${resource} must be an object`
        });
      }

      for (const [action, value] of Object.entries(actions)) {
        // Check if action is valid for this resource
        if (!systemPermissions[resource][action]) {
          return res.status(400).json({
            error: 'Bad Request',
            message: `Invalid action: ${action} for resource ${resource}. Valid actions are: ${Object.keys(systemPermissions[resource]).join(', ')}`
          });
        }

        // Check if value is boolean
        if (typeof value !== 'boolean') {
          return res.status(400).json({
            error: 'Bad Request',
            message: `Permission value must be a boolean (true/false)`
          });
        }
      }
    }

    // Update user permissions in the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .update({
        permissions,
        updated_at: new Date()
      })
      .eq('id', id)
      .select('id, name, email, role, permissions')
      .single();

    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }

    return res.status(200).json({
      message: 'User permissions updated successfully',
      user: data
    });
  } catch (error) {
    console.error('Update user permissions error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to update user permissions' });
  }
};

/**
 * Get available roles and permissions
 */
exports.getRolesAndPermissions = async (req, res) => {
  try {
    const roles = authMiddleware.getRoles();
    const permissions = authMiddleware.getPermissions();

    return res.status(200).json({
      roles,
      permissions
    });
  } catch (error) {
    console.error('Get roles and permissions error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch roles and permissions' });
  }
};

/**
 * Deactivate user (admin only)
 */
exports.deactivateUser = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if user exists
    const { data: existingUser, error: fetchError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', id)
      .single();

    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'User not found' });
    }

    // Update user status in the profiles table
    const { error } = await supabase
      .from('profiles')
      .update({
        is_active: false,
        updated_at: new Date()
      })
      .eq('id', id);

    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }

    return res.status(200).json({
      message: 'User deactivated successfully'
    });
  } catch (error) {
    console.error('Deactivate user error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to deactivate user' });
  }
};

/**
 * Reactivate user (admin only)
 */
exports.reactivateUser = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if user exists
    const { data: existingUser, error: fetchError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', id)
      .single();

    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'User not found' });
    }

    // Update user status in the profiles table
    const { error } = await supabase
      .from('profiles')
      .update({
        is_active: true,
        updated_at: new Date()
      })
      .eq('id', id);

    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }

    return res.status(200).json({
      message: 'User reactivated successfully'
    });
  } catch (error) {
    console.error('Reactivate user error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to reactivate user' });
  }
};
