const supabase = require('../config/supabase');
const nodemailer = require('nodemailer');

// Initialize email transporter (for development, we'll use a test account)
let transporter;

// Initialize email transporter asynchronously
async function initializeTransporter() {
  if (process.env.NODE_ENV === 'production') {
    // Production email configuration
    transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD
      }
    });
  } else {
    // For development/testing, use Ethereal (fake SMTP service)
    const testAccount = await nodemailer.createTestAccount();
    transporter = nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: testAccount.user,
        pass: testAccount.pass
      }
    });
    console.log('Ethereal email account created for testing:', testAccount.user);
  }
}

// Initialize the transporter when the module is loaded
initializeTransporter().catch(console.error);

/**
 * Create a new notification
 */
exports.createNotification = async (req, res) => {
  try {
    const { user } = req;
    const { recipient_id, type, title, message, data, send_email } = req.body;
    
    if (!recipient_id || !type || !title || !message) {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: 'Recipient ID, type, title, and message are required' 
      });
    }
    
    // Validate notification type
    const validTypes = ['info', 'success', 'warning', 'error', 'content', 'workflow', 'publish', 'system'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: `Invalid notification type. Valid types are: ${validTypes.join(', ')}` 
      });
    }
    
    // Create notification in the database
    const { data: notification, error } = await supabase
      .from('notifications')
      .insert([
        {
          sender_id: user.id,
          recipient_id,
          type,
          title,
          message,
          data: data || {},
          is_read: false,
          created_at: new Date()
        }
      ])
      .select()
      .single();
    
    if (error) {
      return res.status(400).json({ error: 'Creation Failed', message: error.message });
    }
    
    // Send email notification if requested
    if (send_email) {
      try {
        // Get recipient's email
        const { data: recipient, error: recipientError } = await supabase
          .from('profiles')
          .select('email, name, preferences')
          .eq('id', recipient_id)
          .single();
        
        if (recipientError || !recipient) {
          console.error('Error fetching recipient:', recipientError);
        } else {
          // Check if user has email notifications enabled in preferences
          const emailNotificationsEnabled = recipient.preferences?.notifications?.email !== false;
          
          if (emailNotificationsEnabled) {
            // Send email
            const emailResult = await sendEmailNotification(
              recipient.email,
              recipient.name || recipient.email.split('@')[0],
              title,
              message,
              type
            );
            
            // Update notification with email status
            await supabase
              .from('notifications')
              .update({ 
                email_sent: true,
                email_sent_at: new Date()
              })
              .eq('id', notification.id);
          }
        }
      } catch (emailError) {
        console.error('Error sending email notification:', emailError);
        // We don't fail the request if email sending fails
      }
    }
    
    return res.status(201).json({ 
      message: 'Notification created successfully',
      notification
    });
  } catch (error) {
    console.error('Create notification error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to create notification' });
  }
};

/**
 * Get all notifications for the current user
 */
exports.getNotifications = async (req, res) => {
  try {
    const { user } = req;
    const { limit = 20, page = 1, unread_only = false } = req.query;
    
    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    
    // Build query
    let query = supabase
      .from('notifications')
      .select('*, sender:sender_id(id, name, email, avatar_url)')
      .eq('recipient_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    // Add unread filter if requested
    if (unread_only === 'true' || unread_only === true) {
      query = query.eq('is_read', false);
    }
    
    // Execute query
    const { data, error } = await query;
    
    if (error) {
      return res.status(400).json({ error: 'Query Failed', message: error.message });
    }
    
    // Get total count for pagination
    let countQuery = supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('recipient_id', user.id);
    
    // Add unread filter to count query if requested
    if (unread_only === 'true' || unread_only === true) {
      countQuery = countQuery.eq('is_read', false);
    }
    
    const { count: totalCount, error: countError } = await countQuery;
    
    if (countError) {
      console.error('Count error:', countError);
    }
    
    // Get unread count
    const { count: unreadCount, error: unreadCountError } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('recipient_id', user.id)
      .eq('is_read', false);
    
    if (unreadCountError) {
      console.error('Unread count error:', unreadCountError);
    }
    
    return res.status(200).json({ 
      notifications: data,
      unread_count: unreadCount || 0,
      pagination: {
        total: totalCount || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil((totalCount || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Get notifications error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch notifications' });
  }
};

/**
 * Mark notification as read
 */
exports.markAsRead = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    
    // Check if notification exists and belongs to the user
    const { data: notification, error: fetchError } = await supabase
      .from('notifications')
      .select('recipient_id')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Notification not found' });
    }
    
    if (notification.recipient_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to update this notification' });
    }
    
    // Update notification
    const { error } = await supabase
      .from('notifications')
      .update({ 
        is_read: true,
        read_at: new Date()
      })
      .eq('id', id);
    
    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }
    
    return res.status(200).json({ message: 'Notification marked as read' });
  } catch (error) {
    console.error('Mark as read error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to mark notification as read' });
  }
};

/**
 * Mark all notifications as read
 */
exports.markAllAsRead = async (req, res) => {
  try {
    const { user } = req;
    
    // Update all notifications for the user
    const { error } = await supabase
      .from('notifications')
      .update({ 
        is_read: true,
        read_at: new Date()
      })
      .eq('recipient_id', user.id)
      .eq('is_read', false);
    
    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }
    
    return res.status(200).json({ message: 'All notifications marked as read' });
  } catch (error) {
    console.error('Mark all as read error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to mark all notifications as read' });
  }
};

/**
 * Delete notification
 */
exports.deleteNotification = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    
    // Check if notification exists and belongs to the user
    const { data: notification, error: fetchError } = await supabase
      .from('notifications')
      .select('recipient_id')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Notification not found' });
    }
    
    if (notification.recipient_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to delete this notification' });
    }
    
    // Delete notification
    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('id', id);
    
    if (error) {
      return res.status(400).json({ error: 'Deletion Failed', message: error.message });
    }
    
    return res.status(200).json({ message: 'Notification deleted successfully' });
  } catch (error) {
    console.error('Delete notification error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to delete notification' });
  }
};

/**
 * Delete all notifications
 */
exports.deleteAllNotifications = async (req, res) => {
  try {
    const { user } = req;
    
    // Delete all notifications for the user
    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('recipient_id', user.id);
    
    if (error) {
      return res.status(400).json({ error: 'Deletion Failed', message: error.message });
    }
    
    return res.status(200).json({ message: 'All notifications deleted successfully' });
  } catch (error) {
    console.error('Delete all notifications error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to delete all notifications' });
  }
};

/**
 * Get notification preferences
 */
exports.getNotificationPreferences = async (req, res) => {
  try {
    const { user } = req;
    
    // Get user preferences from the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .select('preferences')
      .eq('id', user.id)
      .single();
    
    if (error) {
      return res.status(404).json({ error: 'Not Found', message: 'User preferences not found' });
    }
    
    // Extract notification preferences or return defaults
    const notificationPreferences = data.preferences?.notifications || {
      email: true,
      in_app: true,
      content_updates: true,
      workflow_updates: true,
      publish_updates: true,
      system_updates: true
    };
    
    return res.status(200).json({ preferences: notificationPreferences });
  } catch (error) {
    console.error('Get notification preferences error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch notification preferences' });
  }
};

/**
 * Update notification preferences
 */
exports.updateNotificationPreferences = async (req, res) => {
  try {
    const { user } = req;
    const { preferences } = req.body;
    
    if (!preferences || typeof preferences !== 'object') {
      return res.status(400).json({ error: 'Bad Request', message: 'Valid preferences object is required' });
    }
    
    // Get current user preferences
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('preferences')
      .eq('id', user.id)
      .single();
    
    if (userError) {
      return res.status(404).json({ error: 'Not Found', message: 'User preferences not found' });
    }
    
    // Merge new notification preferences with existing preferences
    const currentPreferences = userData.preferences || {};
    const updatedPreferences = {
      ...currentPreferences,
      notifications: {
        ...(currentPreferences.notifications || {}),
        ...preferences
      }
    };
    
    // Update user preferences in the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .update({ 
        preferences: updatedPreferences,
        updated_at: new Date()
      })
      .eq('id', user.id)
      .select('preferences')
      .single();
    
    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }
    
    return res.status(200).json({ 
      message: 'Notification preferences updated successfully',
      preferences: data.preferences.notifications
    });
  } catch (error) {
    console.error('Update notification preferences error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to update notification preferences' });
  }
};

/**
 * Send email notification
 * @param {string} email - Recipient email
 * @param {string} name - Recipient name
 * @param {string} title - Notification title
 * @param {string} message - Notification message
 * @param {string} type - Notification type
 * @returns {Promise<object>} - Email sending result
 */
async function sendEmailNotification(email, name, title, message, type) {
  try {
    // Ensure transporter is initialized
    if (!transporter) {
      await initializeTransporter();
    }
    
    // Create email content
    const mailOptions = {
      from: `"ContentForge" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: email,
      subject: title,
      text: message,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #333;">${title}</h2>
          <p style="color: #666; font-size: 16px;">${message}</p>
          <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #999; font-size: 12px;">
            <p>This is an automated message from ContentForge. Please do not reply to this email.</p>
            <p>You can manage your notification preferences in your account settings.</p>
          </div>
        </div>
      `
    };
    
    // Send email
    const info = await transporter.sendMail(mailOptions);
    
    // For development, log the test URL
    if (process.env.NODE_ENV !== 'production') {
      console.log('Email preview URL:', nodemailer.getTestMessageUrl(info));
    }
    
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Email sending error:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Create a system notification for multiple users
 * This is an internal function that can be used by other controllers
 * @param {Array<string>} recipientIds - Array of recipient user IDs
 * @param {string} type - Notification type
 * @param {string} title - Notification title
 * @param {string} message - Notification message
 * @param {object} data - Additional data for the notification
 * @param {boolean} sendEmail - Whether to send email notifications
 * @returns {Promise<Array>} - Array of created notifications
 */
exports.createSystemNotifications = async (recipientIds, type, title, message, data = {}, sendEmail = false) => {
  try {
    if (!recipientIds || !Array.isArray(recipientIds) || recipientIds.length === 0) {
      console.error('Invalid recipient IDs for system notification');
      return { success: false, error: 'Invalid recipient IDs' };
    }
    
    // Create notifications for all recipients
    const notifications = recipientIds.map(recipientId => ({
      sender_id: null, // System notification has no sender
      recipient_id: recipientId,
      type,
      title,
      message,
      data,
      is_read: false,
      created_at: new Date()
    }));
    
    // Insert notifications
    const { data: createdNotifications, error } = await supabase
      .from('notifications')
      .insert(notifications)
      .select();
    
    if (error) {
      console.error('Error creating system notifications:', error);
      return { success: false, error: error.message };
    }
    
    // Send emails if requested
    if (sendEmail) {
      // Get recipient emails
      const { data: recipients, error: recipientsError } = await supabase
        .from('profiles')
        .select('id, email, name, preferences')
        .in('id', recipientIds);
      
      if (!recipientsError && recipients) {
        // Send emails in parallel
        const emailPromises = recipients
          .filter(recipient => recipient.preferences?.notifications?.email !== false)
          .map(recipient => 
            sendEmailNotification(
              recipient.email,
              recipient.name || recipient.email.split('@')[0],
              title,
              message,
              type
            )
          );
        
        await Promise.all(emailPromises);
        
        // Update notifications with email sent status
        await supabase
          .from('notifications')
          .update({ 
            email_sent: true,
            email_sent_at: new Date()
          })
          .in('id', createdNotifications.map(n => n.id));
      }
    }
    
    return { success: true, notifications: createdNotifications };
  } catch (error) {
    console.error('Create system notifications error:', error);
    return { success: false, error: error.message };
  }
};
