const { transformerFactory } = require('../services/transform');
const path = require('path');
const fs = require('fs').promises;
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads');
    // Ensure the directory exists
    fs.mkdir(uploadDir, { recursive: true })
      .then(() => cb(null, uploadDir))
      .catch(err => cb(err));
  },
  filename: (req, file, cb) => {
    const uniqueFilename = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueFilename);
  }
});

// Create multer upload instance
const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB file size limit
  },
  fileFilter: (req, file, cb) => {
    // Check if the file type is supported
    const supportedTypes = [
      // Text types
      'text/plain', 'text/markdown', 'text/html', 'text/csv', 'application/json',
      // Image types
      'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/tiff', 'image/bmp',
      // Video types
      'video/mp4', 'video/webm', 'video/ogg', 'video/quicktime', 'video/x-msvideo', 'video/x-matroska',
      // Audio types
      'audio/mpeg', 'audio/mp4', 'audio/ogg', 'audio/wav', 'audio/webm', 'audio/flac', 'audio/aac'
    ];

    if (supportedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Unsupported file type: ${file.mimetype}`));
    }
  }
});

/**
 * Get information about available transformers
 */
exports.getTransformerInfo = async (req, res) => {
  try {
    const transformerInfo = transformerFactory.getTransformerInfo();

    return res.status(200).json({
      transformers: transformerInfo
    });
  } catch (error) {
    console.error('Get transformer info error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to get transformer information' });
  }
};

/**
 * Transform content
 * This endpoint handles file uploads and transformation
 */
exports.transformContent = async (req, res) => {
  // Use multer to handle the file upload
  upload.single('file')(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ error: 'Upload Failed', message: err.message });
    }

    try {
      // Check if a file was uploaded
      if (!req.file) {
        return res.status(400).json({ error: 'Bad Request', message: 'No file uploaded' });
      }

      const { inputType, outputType } = req.body;

      if (!inputType || !outputType) {
        return res.status(400).json({ error: 'Bad Request', message: 'Input type and output type are required' });
      }

      // Parse options from request body
      let options = {};
      if (req.body.options) {
        try {
          options = JSON.parse(req.body.options);
        } catch (error) {
          return res.status(400).json({ error: 'Bad Request', message: 'Invalid options format' });
        }
      }

      // Get the appropriate transformer
      const transformer = transformerFactory.getTransformer(inputType, outputType, options);

      if (!transformer) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `No suitable transformer found for converting ${inputType} to ${outputType}`
        });
      }

      // Transform the content
      const result = await transformer.transform(req.file.path, inputType, outputType, options);

      if (!result.success) {
        return res.status(400).json({ error: 'Transformation Failed', message: result.error });
      }

      // If the result contains a file path, send the file
      if (result.filePath) {
        // Set appropriate content type
        res.setHeader('Content-Type', outputType);
        res.setHeader('Content-Disposition', `attachment; filename="transformed.${path.extname(result.filePath).substring(1)}"`);

        // Stream the file
        const fileStream = fs.createReadStream(result.filePath);
        fileStream.pipe(res);

        // Clean up the file after sending
        fileStream.on('end', async () => {
          try {
            // Delete the input and output files
            await fs.unlink(req.file.path);
            await fs.unlink(result.filePath);
          } catch (error) {
            console.error('Error cleaning up files:', error);
          }
        });
      } else {
        // For text transformations, send the data directly
        return res.status(200).json({
          message: 'Transformation successful',
          result: result.data,
          metadata: result.metadata || {}
        });
      }
    } catch (error) {
      console.error('Transform content error:', error);
      return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to transform content' });
    }
  });
};

/**
 * Transform text content
 * This endpoint handles text transformation without file upload
 */
exports.transformText = async (req, res) => {
  try {
    const { text, inputType, outputType, options } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'Bad Request', message: 'Text content is required' });
    }

    if (!inputType || !outputType) {
      return res.status(400).json({ error: 'Bad Request', message: 'Input type and output type are required' });
    }

    // Validate input type is a text type
    const validTextTypes = ['text/plain', 'text/markdown', 'text/html', 'text/csv', 'application/json'];
    if (!validTextTypes.includes(inputType)) {
      return res.status(400).json({ error: 'Bad Request', message: `Invalid input type: ${inputType}. Must be a text type.` });
    }

    // Get the text transformer
    const transformer = transformerFactory.getTransformer(inputType, outputType, options || {});

    if (!transformer) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `No suitable transformer found for converting ${inputType} to ${outputType}`
      });
    }

    // Transform the text
    const result = await transformer.transform(text, inputType, outputType, options || {});

    if (!result.success) {
      return res.status(400).json({ error: 'Transformation Failed', message: result.error });
    }

    return res.status(200).json({
      message: 'Transformation successful',
      result: result.data,
      stats: result.stats || {}
    });
  } catch (error) {
    console.error('Transform text error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to transform text' });
  }
};

/**
 * Get supported transformation types
 */
exports.getSupportedTypes = async (req, res) => {
  try {
    const supportedTypes = {
      text: [
        'text/plain',
        'text/markdown',
        'text/html',
        'text/csv',
        'application/json'
      ],
      image: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
        'image/tiff',
        'image/bmp',
        'image/avif'
      ],
      video: [
        'video/mp4',
        'video/webm',
        'video/ogg',
        'video/quicktime',
        'video/x-msvideo',
        'video/x-matroska',
        'video/gif'
      ],
      audio: [
        'audio/mpeg',
        'audio/mp4',
        'audio/ogg',
        'audio/wav',
        'audio/webm',
        'audio/flac',
        'audio/aac'
      ]
    };

    return res.status(200).json({
      supportedTypes
    });
  } catch (error) {
    console.error('Get supported types error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to get supported types' });
  }
};

/**
 * Optimize content for specific platform
 */
exports.optimizeForPlatform = async (req, res) => {
  try {
    const { content, platform, contentType = 'text', options = {} } = req.body;

    if (!content || !platform) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Content and platform are required'
      });
    }

    let result;

    if (contentType === 'text') {
      const { TextTransformer } = require('../services/transform');
      const textTransformer = new TextTransformer();

      result = await textTransformer.optimizeForPlatform(content, platform, options);
    } else if (contentType === 'image') {
      const { ImageTransformer } = require('../services/transform');
      const imageTransformer = new ImageTransformer();

      // For image optimization, content should be a file path or buffer
      result = await imageTransformer.optimizeForPlatform(content, 'image/jpeg', platform, options);
    } else {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Unsupported content type: ${contentType}`
      });
    }

    if (!result.success) {
      return res.status(400).json({ error: 'Optimization Failed', message: result.error });
    }

    return res.status(200).json({
      message: 'Content optimized successfully',
      optimizedContent: result.data,
      optimization: result.optimization || {},
      metadata: result.metadata || {}
    });
  } catch (error) {
    console.error('Optimize for platform error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to optimize content' });
  }
};

/**
 * Generate content summary
 */
exports.generateSummary = async (req, res) => {
  try {
    const { content, maxLength = 200, options = {} } = req.body;

    if (!content) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Content is required'
      });
    }

    const { TextTransformer } = require('../services/transform');
    const textTransformer = new TextTransformer();

    const result = await textTransformer.generateSummary(content, { maxLength, ...options });

    if (!result.success) {
      return res.status(400).json({ error: 'Summary Generation Failed', message: result.error });
    }

    return res.status(200).json({
      message: 'Summary generated successfully',
      summary: result.data,
      stats: result.summary
    });
  } catch (error) {
    console.error('Generate summary error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to generate summary' });
  }
};

/**
 * Extract and suggest hashtags
 */
exports.extractHashtags = async (req, res) => {
  try {
    const { content, platform = 'general', maxHashtags = 10 } = req.body;

    if (!content) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Content is required'
      });
    }

    const { TextTransformer } = require('../services/transform');
    const textTransformer = new TextTransformer();

    const result = await textTransformer.extractHashtags(content, { platform, maxHashtags });

    if (!result.success) {
      return res.status(400).json({ error: 'Hashtag Extraction Failed', message: result.error });
    }

    return res.status(200).json({
      message: 'Hashtags extracted successfully',
      hashtags: result.data,
      analysis: result.analysis
    });
  } catch (error) {
    console.error('Extract hashtags error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to extract hashtags' });
  }
};

/**
 * Analyze content quality
 */
exports.analyzeQuality = async (req, res) => {
  try {
    const { content, platform = 'general' } = req.body;

    if (!content) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Content is required'
      });
    }

    const { TextTransformer } = require('../services/transform');
    const textTransformer = new TextTransformer();

    const result = await textTransformer.analyzeQuality(content, { platform });

    if (!result.success) {
      return res.status(400).json({ error: 'Quality Analysis Failed', message: result.error });
    }

    return res.status(200).json({
      message: 'Content quality analyzed successfully',
      analysis: result.data.analysis,
      qualityScore: result.data.qualityScore,
      platform: result.data.platform
    });
  } catch (error) {
    console.error('Analyze quality error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to analyze content quality' });
  }
};

/**
 * Get platform specifications
 */
exports.getPlatformSpecs = async (req, res) => {
  try {
    const { platform } = req.params;

    if (!platform) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Platform is required'
      });
    }

    const { ImageTransformer } = require('../services/transform');
    const imageTransformer = new ImageTransformer();

    const specs = imageTransformer.getPlatformSpecs(platform);

    if (!specs) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Platform specifications not found for: ${platform}`
      });
    }

    return res.status(200).json({
      platform,
      specifications: specs
    });
  } catch (error) {
    console.error('Get platform specs error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to get platform specifications' });
  }
};
