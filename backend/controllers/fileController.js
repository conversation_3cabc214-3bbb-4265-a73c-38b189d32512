const FileStorageService = require('../services/storage/fileStorageService');
const FileProcessingQueue = require('../services/queue/fileProcessingQueue');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

// Initialize services
const fileStorage = new FileStorageService();
const processingQueue = new FileProcessingQueue();

// Configure multer for temporary file storage
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'temp', 'uploads');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueFilename = `${uuidv4()}-${Date.now()}-${file.originalname}`;
    cb(null, uniqueFilename);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
    files: 10 // Maximum 10 files per request
  },
  fileFilter: (req, file, cb) => {
    // File validation will be handled by FileStorageService
    cb(null, true);
  }
});

/**
 * Upload single file
 */
exports.uploadFile = async (req, res) => {
  upload.single('file')(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ 
        error: 'Upload Failed', 
        message: err.message 
      });
    }

    try {
      const { user } = req;
      
      if (!req.file) {
        return res.status(400).json({ 
          error: 'Bad Request', 
          message: 'No file uploaded' 
        });
      }

      const options = {
        checkDuplicates: req.body.checkDuplicates === 'true',
        autoProcess: req.body.autoProcess !== 'false' // Default to true
      };

      // Upload file to storage
      const uploadResult = await fileStorage.uploadFile(req.file, user.id, options);

      if (!uploadResult.success) {
        // Clean up temporary file
        try {
          await fs.unlink(req.file.path);
        } catch (cleanupError) {
          console.warn('Failed to clean up temporary file:', cleanupError);
        }

        return res.status(400).json({
          error: 'Upload Failed',
          message: uploadResult.error || 'Unknown upload error',
          errors: uploadResult.errors
        });
      }

      // Add processing jobs if auto-processing is enabled
      if (options.autoProcess && !uploadResult.isDuplicate) {
        const processingJobs = [];

        // Add metadata extraction job
        const metadataJob = await processingQueue.addJob(
          uploadResult.file.id,
          'metadata',
          {},
          3 // Medium priority
        );
        if (metadataJob.success) {
          processingJobs.push(metadataJob.job);
        }

        // Add thumbnail generation for images and videos
        if (req.file.mimetype.startsWith('image/') || req.file.mimetype.startsWith('video/')) {
          const thumbnailJob = await processingQueue.addJob(
            uploadResult.file.id,
            'thumbnail',
            { width: 300, height: 300 },
            2 // High priority
          );
          if (thumbnailJob.success) {
            processingJobs.push(thumbnailJob.job);
          }
        }

        uploadResult.processingJobs = processingJobs;
      }

      return res.status(201).json({
        message: uploadResult.isDuplicate ? 'File already exists' : 'File uploaded successfully',
        file: uploadResult.file,
        metadata: uploadResult.metadata,
        isDuplicate: uploadResult.isDuplicate,
        processingJobs: uploadResult.processingJobs || []
      });

    } catch (error) {
      console.error('Upload file error:', error);
      
      // Clean up temporary file
      if (req.file) {
        try {
          await fs.unlink(req.file.path);
        } catch (cleanupError) {
          console.warn('Failed to clean up temporary file:', cleanupError);
        }
      }

      return res.status(500).json({ 
        error: 'Internal Server Error', 
        message: 'Failed to upload file' 
      });
    }
  });
};

/**
 * Upload multiple files
 */
exports.uploadMultipleFiles = async (req, res) => {
  upload.array('files', 10)(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ 
        error: 'Upload Failed', 
        message: err.message 
      });
    }

    try {
      const { user } = req;
      
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ 
          error: 'Bad Request', 
          message: 'No files uploaded' 
        });
      }

      const options = {
        checkDuplicates: req.body.checkDuplicates === 'true',
        autoProcess: req.body.autoProcess !== 'false'
      };

      const results = [];
      const errors = [];

      // Process each file
      for (const file of req.files) {
        try {
          const uploadResult = await fileStorage.uploadFile(file, user.id, options);
          
          if (uploadResult.success) {
            // Add processing jobs if auto-processing is enabled
            if (options.autoProcess && !uploadResult.isDuplicate) {
              const processingJobs = [];

              // Add metadata extraction job
              const metadataJob = await processingQueue.addJob(
                uploadResult.file.id,
                'metadata',
                {},
                3
              );
              if (metadataJob.success) {
                processingJobs.push(metadataJob.job);
              }

              // Add thumbnail generation for images and videos
              if (file.mimetype.startsWith('image/') || file.mimetype.startsWith('video/')) {
                const thumbnailJob = await processingQueue.addJob(
                  uploadResult.file.id,
                  'thumbnail',
                  { width: 300, height: 300 },
                  2
                );
                if (thumbnailJob.success) {
                  processingJobs.push(thumbnailJob.job);
                }
              }

              uploadResult.processingJobs = processingJobs;
            }

            results.push(uploadResult);
          } else {
            errors.push({
              filename: file.originalname,
              error: uploadResult.error,
              errors: uploadResult.errors
            });
          }
        } catch (fileError) {
          errors.push({
            filename: file.originalname,
            error: fileError.message
          });
        }

        // Clean up temporary file
        try {
          await fs.unlink(file.path);
        } catch (cleanupError) {
          console.warn('Failed to clean up temporary file:', cleanupError);
        }
      }

      return res.status(results.length > 0 ? 201 : 400).json({
        message: `${results.length} files uploaded successfully`,
        files: results.map(r => r.file),
        results,
        errors,
        totalUploaded: results.length,
        totalErrors: errors.length
      });

    } catch (error) {
      console.error('Upload multiple files error:', error);
      
      // Clean up temporary files
      if (req.files) {
        for (const file of req.files) {
          try {
            await fs.unlink(file.path);
          } catch (cleanupError) {
            console.warn('Failed to clean up temporary file:', cleanupError);
          }
        }
      }

      return res.status(500).json({ 
        error: 'Internal Server Error', 
        message: 'Failed to upload files' 
      });
    }
  });
};

/**
 * Get file by ID
 */
exports.getFile = async (req, res) => {
  try {
    const { user } = req;
    const { fileId } = req.params;

    const file = await fileStorage.getFile(fileId, user.id);
    
    if (!file) {
      return res.status(404).json({ 
        error: 'Not Found', 
        message: 'File not found' 
      });
    }

    return res.status(200).json({
      file
    });

  } catch (error) {
    console.error('Get file error:', error);
    return res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to get file' 
    });
  }
};

/**
 * List user files
 */
exports.listFiles = async (req, res) => {
  try {
    const { user } = req;
    const {
      page = 1,
      limit = 20,
      mimeType,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      mimeType,
      sortBy,
      sortOrder
    };

    const result = await fileStorage.listFiles(user.id, options);

    if (!result.success) {
      return res.status(400).json({
        error: 'Bad Request',
        message: result.error
      });
    }

    return res.status(200).json({
      files: result.files,
      pagination: result.pagination
    });

  } catch (error) {
    console.error('List files error:', error);
    return res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to list files' 
    });
  }
};

/**
 * Delete file
 */
exports.deleteFile = async (req, res) => {
  try {
    const { user } = req;
    const { fileId } = req.params;

    const result = await fileStorage.deleteFile(fileId, user.id);

    if (!result.success) {
      return res.status(result.error === 'File not found' ? 404 : 400).json({
        error: result.error === 'File not found' ? 'Not Found' : 'Bad Request',
        message: result.error
      });
    }

    return res.status(200).json({
      message: result.message
    });

  } catch (error) {
    console.error('Delete file error:', error);
    return res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to delete file' 
    });
  }
};

/**
 * Get storage usage statistics
 */
exports.getStorageUsage = async (req, res) => {
  try {
    const { user } = req;

    const result = await fileStorage.getStorageUsage(user.id);

    if (!result.success) {
      return res.status(400).json({
        error: 'Bad Request',
        message: result.error
      });
    }

    return res.status(200).json({
      usage: result.usage
    });

  } catch (error) {
    console.error('Get storage usage error:', error);
    return res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to get storage usage' 
    });
  }
};

/**
 * Process file (add to processing queue)
 */
exports.processFile = async (req, res) => {
  try {
    const { user } = req;
    const { fileId } = req.params;
    const { processingType, options = {}, priority = 5 } = req.body;

    if (!processingType) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Processing type is required'
      });
    }

    // Verify file exists and belongs to user
    const file = await fileStorage.getFile(fileId, user.id);
    if (!file) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'File not found'
      });
    }

    // Add processing job
    const result = await processingQueue.addJob(fileId, processingType, options, priority);

    if (!result.success) {
      return res.status(400).json({
        error: 'Bad Request',
        message: result.error
      });
    }

    return res.status(201).json({
      message: 'Processing job added successfully',
      job: result.job
    });

  } catch (error) {
    console.error('Process file error:', error);
    return res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to add processing job' 
    });
  }
};

/**
 * Get processing queue statistics
 */
exports.getQueueStats = async (req, res) => {
  try {
    const result = await processingQueue.getQueueStats();

    if (!result.success) {
      return res.status(400).json({
        error: 'Bad Request',
        message: result.error
      });
    }

    return res.status(200).json({
      stats: result.stats
    });

  } catch (error) {
    console.error('Get queue stats error:', error);
    return res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to get queue statistics' 
    });
  }
};
