const supabase = require('../config/supabase');
const n8nService = require('../services/n8n/n8nService');
const workflowTemplateService = require('../services/n8n/workflowTemplateService');
const { v4: uuidv4 } = require('uuid');

/**
 * Create a new workflow
 */
exports.createWorkflow = async (req, res) => {
  try {
    const { user } = req;
    const { name, description, steps, n8n_data } = req.body;

    if (!name || !steps || !Array.isArray(steps)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Name and steps array are required'
      });
    }

    // Create workflow in n8n if n8n_data is provided
    let n8nWorkflowId = null;
    let n8nWorkflowData = null;

    if (n8n_data) {
      // Prepare workflow data for n8n
      const n8nWorkflow = {
        name,
        nodes: n8n_data.nodes || [],
        connections: n8n_data.connections || {},
        active: n8n_data.active || false,
        tags: n8n_data.tags || ['contentforge']
      };

      // Create workflow in n8n
      const n8nResult = await n8nService.createWorkflow(n8nWorkflow);

      if (!n8nResult.success) {
        console.error('n8n workflow creation error:', n8nResult.error);
        // Continue without n8n integration if it fails
      } else {
        n8nWorkflowId = n8nResult.data.id;
        n8nWorkflowData = n8nResult.data;
      }
    }

    // Create workflow in the database
    const { data, error } = await supabase
      .from('workflows')
      .insert([
        {
          user_id: user.id,
          name,
          description,
          steps,
          n8n_workflow_id: n8nWorkflowId,
          n8n_data: n8nWorkflowData,
          created_at: new Date(),
          updated_at: new Date()
        }
      ])
      .select()
      .single();

    if (error) {
      // If we created an n8n workflow but failed to create the database record,
      // try to clean up the n8n workflow
      if (n8nWorkflowId) {
        await n8nService.deleteWorkflow(n8nWorkflowId).catch(err => {
          console.error('Failed to clean up n8n workflow after database error:', err);
        });
      }

      return res.status(400).json({ error: 'Creation Failed', message: error.message });
    }

    return res.status(201).json({
      message: 'Workflow created successfully',
      workflow: data
    });
  } catch (error) {
    console.error('Create workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to create workflow' });
  }
};

/**
 * Get all workflows for the user
 */
exports.getAllWorkflows = async (req, res) => {
  try {
    const { user } = req;
    const { limit = 10, page = 1 } = req.query;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get workflows from the database
    const { data, error } = await supabase
      .from('workflows')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      return res.status(400).json({ error: 'Query Failed', message: error.message });
    }

    // Get total count for pagination
    const { count: totalCount, error: countError } = await supabase
      .from('workflows')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    if (countError) {
      console.error('Count error:', countError);
    }

    return res.status(200).json({
      workflows: data,
      pagination: {
        total: totalCount || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil((totalCount || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Get all workflows error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch workflows' });
  }
};

/**
 * Get workflow by ID
 */
exports.getWorkflowById = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;

    // Get workflow from the database
    const { data, error } = await supabase
      .from('workflows')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      return res.status(404).json({ error: 'Not Found', message: 'Workflow not found' });
    }

    // Check if the user owns the workflow
    if (data.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to access this workflow' });
    }

    return res.status(200).json({ workflow: data });
  } catch (error) {
    console.error('Get workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch workflow' });
  }
};

/**
 * Update workflow
 */
exports.updateWorkflow = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    const { name, description, steps, n8n_data } = req.body;

    // Check if workflow exists and belongs to the user
    const { data: existingWorkflow, error: fetchError } = await supabase
      .from('workflows')
      .select('user_id, n8n_workflow_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Workflow not found' });
    }

    if (existingWorkflow.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to update this workflow' });
    }

    // Update workflow in n8n if n8n_data is provided
    let n8nWorkflowId = existingWorkflow.n8n_workflow_id;
    let n8nWorkflowData = null;

    if (n8n_data) {
      // Prepare workflow data for n8n
      const n8nWorkflow = {
        name,
        nodes: n8n_data.nodes || [],
        connections: n8n_data.connections || {},
        active: n8n_data.active || false,
        tags: n8n_data.tags || ['contentforge']
      };

      if (n8nWorkflowId) {
        // Update existing n8n workflow
        const n8nResult = await n8nService.updateWorkflow(n8nWorkflowId, n8nWorkflow);

        if (!n8nResult.success) {
          console.error('n8n workflow update error:', n8nResult.error);
          // Continue without n8n integration if it fails
        } else {
          n8nWorkflowData = n8nResult.data;
        }
      } else {
        // Create new n8n workflow
        const n8nResult = await n8nService.createWorkflow(n8nWorkflow);

        if (!n8nResult.success) {
          console.error('n8n workflow creation error:', n8nResult.error);
          // Continue without n8n integration if it fails
        } else {
          n8nWorkflowId = n8nResult.data.id;
          n8nWorkflowData = n8nResult.data;
        }
      }
    }

    // Update workflow in the database
    const updateData = {
      name,
      description,
      steps,
      updated_at: new Date()
    };

    // Only update n8n fields if we have new data
    if (n8nWorkflowId) {
      updateData.n8n_workflow_id = n8nWorkflowId;
    }

    if (n8nWorkflowData) {
      updateData.n8n_data = n8nWorkflowData;
    }

    const { data, error } = await supabase
      .from('workflows')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return res.status(400).json({ error: 'Update Failed', message: error.message });
    }

    return res.status(200).json({
      message: 'Workflow updated successfully',
      workflow: data
    });
  } catch (error) {
    console.error('Update workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to update workflow' });
  }
};

/**
 * Delete workflow
 */
exports.deleteWorkflow = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;

    // Check if workflow exists and belongs to the user
    const { data: existingWorkflow, error: fetchError } = await supabase
      .from('workflows')
      .select('user_id, n8n_workflow_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      return res.status(404).json({ error: 'Not Found', message: 'Workflow not found' });
    }

    if (existingWorkflow.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to delete this workflow' });
    }

    // Delete workflow from n8n if it exists there
    if (existingWorkflow.n8n_workflow_id) {
      const n8nResult = await n8nService.deleteWorkflow(existingWorkflow.n8n_workflow_id);

      if (!n8nResult.success) {
        console.error('n8n workflow deletion error:', n8nResult.error);
        // Continue with database deletion even if n8n deletion fails
      }
    }

    // Delete workflow from the database
    const { error } = await supabase
      .from('workflows')
      .delete()
      .eq('id', id);

    if (error) {
      return res.status(400).json({ error: 'Deletion Failed', message: error.message });
    }

    return res.status(200).json({ message: 'Workflow deleted successfully' });
  } catch (error) {
    console.error('Delete workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to delete workflow' });
  }
};

/**
 * Execute workflow
 */
exports.executeWorkflow = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    const { content_id, parameters } = req.body;

    if (!content_id) {
      return res.status(400).json({ error: 'Bad Request', message: 'Content ID is required' });
    }

    // Check if workflow exists and belongs to the user
    const { data: workflow, error: workflowError } = await supabase
      .from('workflows')
      .select('*')
      .eq('id', id)
      .single();

    if (workflowError) {
      return res.status(404).json({ error: 'Not Found', message: 'Workflow not found' });
    }

    if (workflow.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to execute this workflow' });
    }

    // Check if content exists and belongs to the user
    const { data: content, error: contentError } = await supabase
      .from('content')
      .select('*')
      .eq('id', content_id)
      .single();

    if (contentError) {
      return res.status(404).json({ error: 'Not Found', message: 'Content not found' });
    }

    if (content.user_id !== user.id) {
      return res.status(403).json({ error: 'Forbidden', message: 'You do not have permission to use this content' });
    }

    // Create workflow execution record
    const { data: execution, error: executionError } = await supabase
      .from('workflow_executions')
      .insert([
        {
          workflow_id: id,
          content_id,
          user_id: user.id,
          status: 'pending',
          parameters,
          started_at: new Date()
        }
      ])
      .select()
      .single();

    if (executionError) {
      return res.status(400).json({ error: 'Execution Failed', message: executionError.message });
    }

    // Execute workflow in n8n if n8n_workflow_id exists
    if (workflow.n8n_workflow_id) {
      try {
        // Prepare data for n8n execution
        const executionData = {
          content,
          parameters,
          execution_id: execution.id,
          workflow_id: id,
          user_id: user.id,
          // Add webhook URL for n8n to call back with results
          webhook: n8nService.createWebhookUrl(id, 'execution_complete')
        };

        // Execute workflow in n8n
        const n8nResult = await n8nService.executeWorkflow(workflow.n8n_workflow_id, executionData);

        if (!n8nResult.success) {
          throw new Error(n8nResult.error);
        }

        // Update execution with n8n execution ID
        await supabase
          .from('workflow_executions')
          .update({
            status: 'running',
            n8n_execution_id: n8nResult.data.executionId || null
          })
          .eq('id', execution.id);

        return res.status(202).json({
          message: 'Workflow execution started in n8n',
          execution_id: execution.id,
          n8n_execution_id: n8nResult.data.executionId,
          status: 'running'
        });
      } catch (n8nError) {
        console.error('n8n execution error:', n8nError);

        // Update execution status to 'failed'
        await supabase
          .from('workflow_executions')
          .update({
            status: 'failed',
            error: n8nError.message,
            completed_at: new Date()
          })
          .eq('id', execution.id);

        return res.status(500).json({ error: 'Execution Failed', message: 'Failed to execute workflow in n8n' });
      }
    } else {
      // Execute workflow locally if no n8n integration
      try {
        // Update execution status to 'running'
        await supabase
          .from('workflow_executions')
          .update({ status: 'running' })
          .eq('id', execution.id);

        // Execute each step in the workflow
        const results = [];
        for (const step of workflow.steps) {
          // This is a simplified example - in a real implementation,
          // you would have a more sophisticated step execution system
          results.push({
            step: step.name,
            status: 'completed',
            result: `Executed ${step.name}`
          });
        }

        // Update execution with results
        await supabase
          .from('workflow_executions')
          .update({
            status: 'completed',
            results,
            completed_at: new Date()
          })
          .eq('id', execution.id);

        return res.status(202).json({
          message: 'Workflow execution started locally',
          execution_id: execution.id,
          status: 'running'
        });
      } catch (localError) {
        console.error('Local execution error:', localError);

        // Update execution status to 'failed'
        await supabase
          .from('workflow_executions')
          .update({
            status: 'failed',
            error: localError.message,
            completed_at: new Date()
          })
          .eq('id', execution.id);

        return res.status(500).json({ error: 'Execution Failed', message: 'Failed to execute workflow locally' });
      }
    }
  } catch (error) {
    console.error('Execute workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to execute workflow' });
  }
};

/**
 * Get workflow execution status
 */
exports.getWorkflowStatus = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    const { execution_id } = req.query;

    if (!execution_id) {
      // Get all executions for this workflow
      const { data, error } = await supabase
        .from('workflow_executions')
        .select('*')
        .eq('workflow_id', id)
        .eq('user_id', user.id)
        .order('started_at', { ascending: false })
        .limit(10);

      if (error) {
        return res.status(400).json({ error: 'Query Failed', message: error.message });
      }

      return res.status(200).json({ executions: data });
    } else {
      // Get specific execution
      const { data: execution, error } = await supabase
        .from('workflow_executions')
        .select('*, workflow:workflow_id(*)')
        .eq('id', execution_id)
        .eq('user_id', user.id)
        .single();

      if (error) {
        return res.status(404).json({ error: 'Not Found', message: 'Execution not found' });
      }

      // If execution is still running and has an n8n execution ID, check status in n8n
      if (execution.status === 'running' && execution.n8n_execution_id && execution.workflow?.n8n_workflow_id) {
        try {
          // Get execution status from n8n
          const n8nResult = await n8nService.getExecution(execution.n8n_execution_id);

          if (n8nResult.success) {
            const n8nExecution = n8nResult.data;

            // Update execution status based on n8n status
            let newStatus = execution.status;
            let results = execution.results || null;
            let error = execution.error || null;
            let completedAt = execution.completed_at || null;

            if (n8nExecution.finished) {
              if (n8nExecution.status === 'success') {
                newStatus = 'completed';
                results = n8nExecution.data;
              } else if (n8nExecution.status === 'error') {
                newStatus = 'failed';
                error = n8nExecution.error?.message || 'Unknown error';
              } else if (n8nExecution.status === 'canceled') {
                newStatus = 'cancelled';
              }

              completedAt = new Date();

              // Update execution in database
              await supabase
                .from('workflow_executions')
                .update({
                  status: newStatus,
                  results,
                  error,
                  completed_at: completedAt,
                  n8n_data: n8nExecution
                })
                .eq('id', execution_id);

              // Update execution object for response
              execution.status = newStatus;
              execution.results = results;
              execution.error = error;
              execution.completed_at = completedAt;
              execution.n8n_data = n8nExecution;
            }
          }
        } catch (n8nError) {
          console.error('Error fetching n8n execution status:', n8nError);
          // Continue with the current execution data even if n8n status check fails
        }
      }

      return res.status(200).json({ execution });
    }
  } catch (error) {
    console.error('Get workflow status error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to get workflow status' });
  }
};

/**
 * Cancel workflow execution
 */
exports.cancelWorkflow = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;
    const { execution_id } = req.body;

    if (!execution_id) {
      return res.status(400).json({ error: 'Bad Request', message: 'Execution ID is required' });
    }

    // Check if execution exists and belongs to the user
    const { data: execution, error: executionError } = await supabase
      .from('workflow_executions')
      .select('*, workflow:workflow_id(*)')
      .eq('id', execution_id)
      .eq('user_id', user.id)
      .single();

    if (executionError) {
      return res.status(404).json({ error: 'Not Found', message: 'Execution not found' });
    }

    // Check if execution is in a cancellable state
    if (execution.status !== 'pending' && execution.status !== 'running') {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot cancel execution with status '${execution.status}'`
      });
    }

    // Cancel execution in n8n if it has an n8n execution ID
    if (execution.n8n_execution_id && execution.workflow?.n8n_workflow_id) {
      try {
        // Cancel execution in n8n
        const n8nResult = await n8nService.deleteExecution(execution.n8n_execution_id);

        if (!n8nResult.success) {
          console.error('n8n cancellation error:', n8nResult.error);
          // Continue with database update even if n8n cancellation fails
        }
      } catch (n8nError) {
        console.error('n8n cancellation error:', n8nError);
        // Continue with database update even if n8n cancellation fails
      }
    }

    // Update execution status to 'cancelled'
    await supabase
      .from('workflow_executions')
      .update({
        status: 'cancelled',
        completed_at: new Date()
      })
      .eq('id', execution_id);

    return res.status(200).json({
      message: 'Workflow execution cancelled',
      execution_id
    });
  } catch (error) {
    console.error('Cancel workflow error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to cancel workflow' });
  }
};

/**
 * Get workflow templates
 */
exports.getWorkflowTemplates = async (req, res) => {
  try {
    // Get workflow templates from the database
    const { data: dbTemplates, error } = await supabase
      .from('workflow_templates')
      .select('*')
      .order('name', { ascending: true });

    if (error) {
      return res.status(400).json({ error: 'Query Failed', message: error.message });
    }

    // Get workflow templates from files
    const fileTemplates = await workflowTemplateService.getAllTemplates();

    // Get workflow templates from n8n
    const n8nResult = await n8nService.getTemplates();
    let n8nTemplates = [];

    if (n8nResult.success) {
      n8nTemplates = n8nResult.data.map(template => ({
        ...template,
        source: 'n8n'
      }));
    } else {
      console.error('Error fetching n8n templates:', n8nResult.error);
    }

    // Combine templates from all sources
    const templates = [
      ...dbTemplates.map(template => ({ ...template, source: 'database' })),
      ...fileTemplates,
      ...n8nTemplates
    ];

    // Get template statistics
    const stats = await workflowTemplateService.getTemplateStats();

    return res.status(200).json({
      templates,
      stats
    });
  } catch (error) {
    console.error('Get workflow templates error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to fetch workflow templates' });
  }
};

/**
 * Clone workflow template
 */
exports.cloneWorkflowTemplate = async (req, res) => {
  try {
    const { user } = req;
    const { templateId } = req.params;
    const { name, description, source } = req.body;

    // Handle different template sources
    if (source === 'n8n') {
      // Clone n8n template
      const customizations = {
        name: name || `Template Clone (${new Date().toISOString().split('T')[0]})`,
        active: false,
        tags: ['contentforge', 'cloned']
      };

      // Create workflow in n8n from template
      const n8nResult = await n8nService.createWorkflowFromTemplate(templateId, customizations);

      if (!n8nResult.success) {
        return res.status(400).json({ error: 'Clone Failed', message: n8nResult.error });
      }

      // Create workflow in the database
      const { data, error } = await supabase
        .from('workflows')
        .insert([
          {
            user_id: user.id,
            name: customizations.name,
            description: description || 'Cloned from n8n template',
            steps: [
              { name: 'n8n workflow', type: 'n8n', config: {} }
            ],
            n8n_workflow_id: n8nResult.data.id,
            n8n_data: n8nResult.data,
            created_at: new Date(),
            updated_at: new Date(),
            cloned_from: templateId,
            cloned_from_source: 'n8n'
          }
        ])
        .select()
        .single();

      if (error) {
        // Clean up n8n workflow if database insert fails
        await n8nService.deleteWorkflow(n8nResult.data.id).catch(err => {
          console.error('Failed to clean up n8n workflow after database error:', err);
        });

        return res.status(400).json({ error: 'Clone Failed', message: error.message });
      }

      return res.status(201).json({
        message: 'n8n workflow template cloned successfully',
        workflow: data
      });
    } else if (source === 'file') {
      // Clone file template
      const template = await workflowTemplateService.getTemplate(templateId);

      if (!template) {
        return res.status(404).json({ error: 'Not Found', message: 'Template not found' });
      }

      // Import template to n8n
      const importResult = await workflowTemplateService.importTemplateToN8n(templateId, {
        name: name || `${template.name} (Copy)`,
        active: false,
        tags: ['contentforge', 'cloned', 'file-template']
      });

      let n8nWorkflowId = null;
      let n8nWorkflowData = null;

      if (importResult.success) {
        n8nWorkflowId = importResult.data.id;
        n8nWorkflowData = importResult.data;
      } else {
        console.error('Failed to import template to n8n:', importResult.error);
      }

      // Create workflow in the database
      const { data, error } = await supabase
        .from('workflows')
        .insert([
          {
            user_id: user.id,
            name: name || `${template.name} (Copy)`,
            description: description || template.description,
            steps: template.nodes ? template.nodes.map(node => ({
              name: node.name,
              type: node.type,
              config: node.parameters || {}
            })) : [],
            n8n_workflow_id: n8nWorkflowId,
            n8n_data: n8nWorkflowData,
            created_at: new Date(),
            updated_at: new Date(),
            cloned_from: templateId,
            cloned_from_source: 'file'
          }
        ])
        .select()
        .single();

      if (error) {
        // Clean up n8n workflow if database insert fails
        if (n8nWorkflowId) {
          await n8nService.deleteWorkflow(n8nWorkflowId).catch(err => {
            console.error('Failed to clean up n8n workflow after database error:', err);
          });
        }

        return res.status(400).json({ error: 'Clone Failed', message: error.message });
      }

      return res.status(201).json({
        message: 'File workflow template cloned successfully',
        workflow: data
      });
    } else {
      // Default: Clone database template
      // Get template from the database
      const { data: template, error: templateError } = await supabase
        .from('workflow_templates')
        .select('*')
        .eq('id', templateId)
        .single();

      if (templateError) {
        return res.status(404).json({ error: 'Not Found', message: 'Template not found' });
      }

      // Create new workflow based on template
      const { data, error } = await supabase
        .from('workflows')
        .insert([
          {
            user_id: user.id,
            name: name || `${template.name} (Copy)`,
            description: description || template.description,
            steps: template.steps,
            created_at: new Date(),
            updated_at: new Date(),
            cloned_from: templateId,
            cloned_from_source: 'database'
          }
        ])
        .select()
        .single();

      if (error) {
        return res.status(400).json({ error: 'Clone Failed', message: error.message });
      }

      return res.status(201).json({
        message: 'Workflow template cloned successfully',
        workflow: data
      });
    }
  } catch (error) {
    console.error('Clone workflow template error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to clone workflow template' });
  }
};

/**
 * Import template to n8n
 */
exports.importTemplateToN8n = async (req, res) => {
  try {
    const { templateId } = req.params;
    const { name, active, tags } = req.body;

    const result = await workflowTemplateService.importTemplateToN8n(templateId, {
      name,
      active: active || false,
      tags: tags || ['contentforge', 'imported']
    });

    if (!result.success) {
      return res.status(400).json({ error: 'Import Failed', message: result.error });
    }

    return res.status(201).json({
      message: 'Template imported to n8n successfully',
      n8nWorkflow: result.data
    });
  } catch (error) {
    console.error('Import template to n8n error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to import template to n8n' });
  }
};

/**
 * Export n8n workflow as template
 */
exports.exportN8nWorkflowAsTemplate = async (req, res) => {
  try {
    const { n8nWorkflowId } = req.params;
    const { templateId, name, description, tags } = req.body;

    if (!templateId) {
      return res.status(400).json({ error: 'Bad Request', message: 'Template ID is required' });
    }

    const result = await workflowTemplateService.exportFromN8n(n8nWorkflowId, templateId, {
      name,
      description,
      tags: tags || ['exported']
    });

    if (!result.success) {
      return res.status(400).json({ error: 'Export Failed', message: result.error });
    }

    return res.status(201).json({
      message: 'n8n workflow exported as template successfully',
      template: result.data
    });
  } catch (error) {
    console.error('Export n8n workflow as template error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to export n8n workflow as template' });
  }
};

/**
 * Get template statistics
 */
exports.getTemplateStats = async (req, res) => {
  try {
    const stats = await workflowTemplateService.getTemplateStats();
    return res.status(200).json({ stats });
  } catch (error) {
    console.error('Get template stats error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to get template statistics' });
  }
};

/**
 * Validate template
 */
exports.validateTemplate = async (req, res) => {
  try {
    const template = req.body;
    const validation = workflowTemplateService.validateTemplate(template);

    return res.status(200).json({
      valid: validation.valid,
      errors: validation.errors
    });
  } catch (error) {
    console.error('Validate template error:', error);
    return res.status(500).json({ error: 'Internal Server Error', message: 'Failed to validate template' });
  }
};
