/**
 * Instagram Insights API Service
 * Handles Instagram analytics data collection and processing
 */

const axios = require('axios');

class InstagramInsights {
  constructor() {
    this.baseURL = 'https://graph.facebook.com/v18.0';
  }

  /**
   * Get account insights
   * @param {string} accessToken - Instagram access token
   * @param {string} accountId - Instagram business account ID
   * @param {Object} options - Insights options
   * @returns {Object} Account insights data
   */
  async getAccountInsights(accessToken, accountId, options = {}) {
    try {
      const {
        metrics = 'impressions,reach,profile_views,website_clicks',
        period = 'day',
        since = Math.floor(Date.now() / 1000) - (7 * 24 * 60 * 60), // 7 days ago
        until = Math.floor(Date.now() / 1000)
      } = options;

      const response = await axios.get(`${this.baseURL}/${accountId}/insights`, {
        params: {
          metric: metrics,
          period,
          since,
          until,
          access_token: accessToken
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Instagram Account Insights Error:', error);
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }

  /**
   * Get media insights
   * @param {string} accessToken - Instagram access token
   * @param {string} mediaId - Instagram media ID
   * @param {Object} options - Insights options
   * @returns {Object} Media insights data
   */
  async getMediaInsights(accessToken, mediaId, options = {}) {
    try {
      const {
        metrics = 'impressions,reach,engagement,saves,video_views'
      } = options;

      const response = await axios.get(`${this.baseURL}/${mediaId}/insights`, {
        params: {
          metric: metrics,
          access_token: accessToken
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Instagram Media Insights Error:', error);
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }

  /**
   * Get story insights
   * @param {string} accessToken - Instagram access token
   * @param {string} storyId - Instagram story ID
   * @param {Object} options - Insights options
   * @returns {Object} Story insights data
   */
  async getStoryInsights(accessToken, storyId, options = {}) {
    try {
      const {
        metrics = 'impressions,reach,replies,exits,taps_forward,taps_back'
      } = options;

      const response = await axios.get(`${this.baseURL}/${storyId}/insights`, {
        params: {
          metric: metrics,
          access_token: accessToken
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Instagram Story Insights Error:', error);
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }

  /**
   * Get audience insights
   * @param {string} accessToken - Instagram access token
   * @param {string} accountId - Instagram business account ID
   * @param {Object} options - Insights options
   * @returns {Object} Audience insights data
   */
  async getAudienceInsights(accessToken, accountId, options = {}) {
    try {
      const {
        metrics = 'audience_gender_age,audience_locale,audience_country',
        period = 'lifetime'
      } = options;

      const response = await axios.get(`${this.baseURL}/${accountId}/insights`, {
        params: {
          metric: metrics,
          period,
          access_token: accessToken
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Instagram Audience Insights Error:', error);
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }

  /**
   * Get hashtag insights
   * @param {string} accessToken - Instagram access token
   * @param {string} hashtagId - Instagram hashtag ID
   * @param {Object} options - Insights options
   * @returns {Object} Hashtag insights data
   */
  async getHashtagInsights(accessToken, hashtagId, options = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/${hashtagId}`, {
        params: {
          fields: 'id,name,media_count',
          access_token: accessToken
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Instagram Hashtag Insights Error:', error);
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message
      };
    }
  }
}

module.exports = new InstagramInsights();
