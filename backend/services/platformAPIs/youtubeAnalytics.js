/**
 * YouTube Analytics API Service
 * Handles YouTube analytics data collection and processing
 */

const { google } = require('googleapis');

class YouTubeAnalytics {
  constructor() {
    this.youtube = google.youtube('v3');
    this.youtubeAnalytics = google.youtubeAnalytics('v2');
  }

  /**
   * Get channel analytics data
   * @param {string} accessToken - OAuth access token
   * @param {string} channelId - YouTube channel ID
   * @param {Object} options - Analytics options (startDate, endDate, metrics)
   * @returns {Object} Analytics data
   */
  async getChannelAnalytics(accessToken, channelId, options = {}) {
    try {
      const auth = new google.auth.OAuth2();
      auth.setCredentials({ access_token: accessToken });

      const {
        startDate = '2024-01-01',
        endDate = new Date().toISOString().split('T')[0],
        metrics = 'views,estimatedMinutesWatched,subscribersGained'
      } = options;

      const response = await this.youtubeAnalytics.reports.query({
        auth,
        ids: `channel==${channelId}`,
        startDate,
        endDate,
        metrics,
        dimensions: 'day'
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('YouTube Analytics Error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get video analytics data
   * @param {string} accessToken - OAuth access token
   * @param {string} videoId - YouTube video ID
   * @param {Object} options - Analytics options
   * @returns {Object} Video analytics data
   */
  async getVideoAnalytics(accessToken, videoId, options = {}) {
    try {
      const auth = new google.auth.OAuth2();
      auth.setCredentials({ access_token: accessToken });

      const {
        startDate = '2024-01-01',
        endDate = new Date().toISOString().split('T')[0],
        metrics = 'views,likes,dislikes,comments,shares'
      } = options;

      const response = await this.youtubeAnalytics.reports.query({
        auth,
        ids: `video==${videoId}`,
        startDate,
        endDate,
        metrics,
        dimensions: 'day'
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('YouTube Video Analytics Error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get channel statistics
   * @param {string} accessToken - OAuth access token
   * @param {string} channelId - YouTube channel ID
   * @returns {Object} Channel statistics
   */
  async getChannelStatistics(accessToken, channelId) {
    try {
      const auth = new google.auth.OAuth2();
      auth.setCredentials({ access_token: accessToken });

      const response = await this.youtube.channels.list({
        auth,
        part: 'statistics',
        id: channelId
      });

      return {
        success: true,
        data: response.data.items[0]?.statistics || {}
      };
    } catch (error) {
      console.error('YouTube Channel Statistics Error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new YouTubeAnalytics();
