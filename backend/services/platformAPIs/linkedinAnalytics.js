/**
 * LinkedIn Analytics API Service
 * Handles LinkedIn analytics data collection and processing
 */

const axios = require('axios');

class LinkedInAnalytics {
  constructor() {
    this.baseURL = 'https://api.linkedin.com/v2';
  }

  /**
   * Get organization analytics
   * @param {string} accessToken - LinkedIn access token
   * @param {string} organizationId - LinkedIn organization ID
   * @param {Object} options - Analytics options
   * @returns {Object} Organization analytics data
   */
  async getOrganizationAnalytics(accessToken, organizationId, options = {}) {
    try {
      const {
        timeGranularity = 'DAY',
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        endDate = new Date().toISOString().split('T')[0]
      } = options;

      const response = await axios.get(`${this.baseURL}/organizationalEntityFollowerStatistics`, {
        params: {
          q: 'organizationalEntity',
          organizationalEntity: `urn:li:organization:${organizationId}`,
          timeIntervals: `(timeGranularity:${timeGranularity},timeRange:(start:(day:${startDate.split('-')[2]},month:${startDate.split('-')[1]},year:${startDate.split('-')[0]}),end:(day:${endDate.split('-')[2]},month:${endDate.split('-')[1]},year:${endDate.split('-')[0]})))`
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn Organization Analytics Error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get post analytics
   * @param {string} accessToken - LinkedIn access token
   * @param {string} postId - LinkedIn post ID
   * @param {Object} options - Analytics options
   * @returns {Object} Post analytics data
   */
  async getPostAnalytics(accessToken, postId, options = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/socialActions/${postId}`, {
        params: {
          projection: '(likesSummary,commentsSummary,sharesSummary)'
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn Post Analytics Error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get page statistics
   * @param {string} accessToken - LinkedIn access token
   * @param {string} organizationId - LinkedIn organization ID
   * @param {Object} options - Statistics options
   * @returns {Object} Page statistics data
   */
  async getPageStatistics(accessToken, organizationId, options = {}) {
    try {
      const {
        timeGranularity = 'DAY',
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        endDate = new Date().toISOString().split('T')[0]
      } = options;

      const response = await axios.get(`${this.baseURL}/organizationalEntityShareStatistics`, {
        params: {
          q: 'organizationalEntity',
          organizationalEntity: `urn:li:organization:${organizationId}`,
          timeIntervals: `(timeGranularity:${timeGranularity},timeRange:(start:(day:${startDate.split('-')[2]},month:${startDate.split('-')[1]},year:${startDate.split('-')[0]}),end:(day:${endDate.split('-')[2]},month:${endDate.split('-')[1]},year:${endDate.split('-')[0]})))`
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn Page Statistics Error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get follower demographics
   * @param {string} accessToken - LinkedIn access token
   * @param {string} organizationId - LinkedIn organization ID
   * @param {Object} options - Demographics options
   * @returns {Object} Follower demographics data
   */
  async getFollowerDemographics(accessToken, organizationId, options = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/organizationalEntityFollowerStatistics`, {
        params: {
          q: 'organizationalEntity',
          organizationalEntity: `urn:li:organization:${organizationId}`,
          projection: '(followerCountsByAssociationType,followerCountsByIndustry,followerCountsByFunction,followerCountsBySeniority,followerCountsByStaffCountRange,followerCountsByRegion)'
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn Follower Demographics Error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get content impressions
   * @param {string} accessToken - LinkedIn access token
   * @param {string} organizationId - LinkedIn organization ID
   * @param {Object} options - Impressions options
   * @returns {Object} Content impressions data
   */
  async getContentImpressions(accessToken, organizationId, options = {}) {
    try {
      const {
        timeGranularity = 'DAY',
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        endDate = new Date().toISOString().split('T')[0]
      } = options;

      const response = await axios.get(`${this.baseURL}/organizationalEntityShareStatistics`, {
        params: {
          q: 'organizationalEntity',
          organizationalEntity: `urn:li:organization:${organizationId}`,
          timeIntervals: `(timeGranularity:${timeGranularity},timeRange:(start:(day:${startDate.split('-')[2]},month:${startDate.split('-')[1]},year:${startDate.split('-')[0]}),end:(day:${endDate.split('-')[2]},month:${endDate.split('-')[1]},year:${endDate.split('-')[0]})))`,
          projection: '(totalShareStatistics:(impressionCount,clickCount,likeCount,commentCount,shareCount))'
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn Content Impressions Error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }
}

module.exports = new LinkedInAnalytics();
