const supabase = require('../config/supabase');
const platformManager = require('./platformManager');

/**
 * Scheduling Service for handling scheduled publications
 */
class SchedulingService {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.checkInterval = 60000; // Check every minute
  }

  /**
   * Start the scheduling service
   */
  start() {
    if (this.isRunning) {
      console.log('Scheduling service is already running');
      return;
    }

    console.log('Starting scheduling service...');
    this.isRunning = true;
    
    // Run immediately
    this.processScheduledPublications();
    
    // Set up interval
    this.intervalId = setInterval(() => {
      this.processScheduledPublications();
    }, this.checkInterval);
  }

  /**
   * Stop the scheduling service
   */
  stop() {
    if (!this.isRunning) {
      console.log('Scheduling service is not running');
      return;
    }

    console.log('Stopping scheduling service...');
    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Process scheduled publications that are due
   */
  async processScheduledPublications() {
    try {
      // Get publications that are due for publishing
      const { data: scheduledPublications, error } = await supabase
        .from('scheduled_publications')
        .select(`
          *,
          content:content_id (
            id,
            title,
            description,
            content_type,
            metadata
          )
        `)
        .eq('status', 'scheduled')
        .lte('scheduled_time', new Date().toISOString())
        .order('scheduled_time', { ascending: true })
        .limit(10); // Process up to 10 at a time

      if (error) {
        console.error('Error fetching scheduled publications:', error);
        return;
      }

      if (!scheduledPublications || scheduledPublications.length === 0) {
        return; // No publications to process
      }

      console.log(`Processing ${scheduledPublications.length} scheduled publications`);

      // Process each scheduled publication
      for (const publication of scheduledPublications) {
        await this.processScheduledPublication(publication);
      }
    } catch (error) {
      console.error('Error processing scheduled publications:', error);
    }
  }

  /**
   * Process a single scheduled publication
   * @param {Object} publication - The scheduled publication record
   */
  async processScheduledPublication(publication) {
    try {
      // Update status to 'publishing'
      await supabase
        .from('scheduled_publications')
        .update({ status: 'publishing' })
        .eq('id', publication.id);

      // Prepare content for publishing
      const content = {
        id: publication.content.id,
        title: publication.content.title,
        description: publication.content.description,
        content_type: publication.content.content_type,
        metadata: publication.content.metadata || {}
      };

      // Publish using platform manager
      const result = await platformManager.publishToMultiplePlatforms(
        publication.user_id,
        content,
        [publication.platform],
        {}
      );

      if (result.success && result.results[publication.platform]) {
        // Publication successful
        const publishResult = result.results[publication.platform];
        
        await supabase
          .from('scheduled_publications')
          .update({
            status: 'published',
            published_url: publishResult.videoUrl || publishResult.permalink || publishResult.url,
            platform_post_id: publishResult.videoId || publishResult.postId || publishResult.articleId
          })
          .eq('id', publication.id);

        console.log(`Successfully published scheduled content to ${publication.platform}`);
      } else {
        // Publication failed
        const error = result.errors[publication.platform];
        const retryCount = publication.retry_count + 1;
        
        if (retryCount < publication.max_retries) {
          // Retry later
          const nextRetryTime = new Date(Date.now() + (retryCount * 5 * 60 * 1000)); // Exponential backoff
          
          await supabase
            .from('scheduled_publications')
            .update({
              status: 'scheduled',
              retry_count: retryCount,
              scheduled_time: nextRetryTime.toISOString(),
              error_message: error?.message || 'Unknown error'
            })
            .eq('id', publication.id);

          console.log(`Scheduled retry ${retryCount} for ${publication.platform} publication`);
        } else {
          // Max retries reached, mark as failed
          await supabase
            .from('scheduled_publications')
            .update({
              status: 'failed',
              error_message: error?.message || 'Max retries reached'
            })
            .eq('id', publication.id);

          console.error(`Failed to publish scheduled content to ${publication.platform} after ${publication.max_retries} retries`);
        }
      }
    } catch (error) {
      console.error(`Error processing scheduled publication ${publication.id}:`, error);
      
      // Mark as failed
      await supabase
        .from('scheduled_publications')
        .update({
          status: 'failed',
          error_message: error.message
        })
        .eq('id', publication.id);
    }
  }

  /**
   * Schedule a publication
   * @param {string} userId - The user ID
   * @param {string} contentId - The content ID
   * @param {string} platform - The platform name
   * @param {Date} scheduledTime - When to publish
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Scheduling result
   */
  async schedulePublication(userId, contentId, platform, scheduledTime, options = {}) {
    try {
      // Validate scheduled time
      if (scheduledTime <= new Date()) {
        return {
          success: false,
          error: 'Invalid scheduled time',
          message: 'Scheduled time must be in the future'
        };
      }

      // Check if content exists
      const { data: content, error: contentError } = await supabase
        .from('content')
        .select('id, title')
        .eq('id', contentId)
        .eq('user_id', userId)
        .single();

      if (contentError || !content) {
        return {
          success: false,
          error: 'Content not found',
          message: 'The specified content does not exist or you do not have permission to access it'
        };
      }

      // Check if user has connected the platform
      const { data: token, error: tokenError } = await supabase
        .from('oauth_tokens')
        .select('platform')
        .eq('user_id', userId)
        .eq('platform', platform)
        .eq('is_active', true)
        .single();

      if (tokenError || !token) {
        return {
          success: false,
          error: 'Platform not connected',
          message: `You need to connect your ${platform} account before scheduling publications`
        };
      }

      // Create scheduled publication record
      const publicationRecord = {
        user_id: userId,
        content_id: contentId,
        platform: platform,
        scheduled_time: scheduledTime.toISOString(),
        status: 'scheduled',
        retry_count: 0,
        max_retries: options.maxRetries || 3
      };

      const { data: scheduled, error: insertError } = await supabase
        .from('scheduled_publications')
        .insert(publicationRecord)
        .select()
        .single();

      if (insertError) {
        return {
          success: false,
          error: 'Database error',
          message: 'Failed to schedule publication'
        };
      }

      return {
        success: true,
        scheduled_publication: scheduled,
        message: `Successfully scheduled publication to ${platform} for ${scheduledTime.toISOString()}`
      };
    } catch (error) {
      console.error('Schedule publication error:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to schedule publication'
      };
    }
  }

  /**
   * Cancel a scheduled publication
   * @param {string} userId - The user ID
   * @param {string} publicationId - The scheduled publication ID
   * @returns {Promise<Object>} - Cancellation result
   */
  async cancelScheduledPublication(userId, publicationId) {
    try {
      const { data: publication, error: fetchError } = await supabase
        .from('scheduled_publications')
        .select('*')
        .eq('id', publicationId)
        .eq('user_id', userId)
        .single();

      if (fetchError || !publication) {
        return {
          success: false,
          error: 'Publication not found',
          message: 'The specified scheduled publication does not exist or you do not have permission to access it'
        };
      }

      if (publication.status !== 'scheduled') {
        return {
          success: false,
          error: 'Cannot cancel',
          message: `Cannot cancel publication with status: ${publication.status}`
        };
      }

      const { error: updateError } = await supabase
        .from('scheduled_publications')
        .update({ status: 'cancelled' })
        .eq('id', publicationId);

      if (updateError) {
        return {
          success: false,
          error: 'Database error',
          message: 'Failed to cancel scheduled publication'
        };
      }

      return {
        success: true,
        message: 'Successfully cancelled scheduled publication'
      };
    } catch (error) {
      console.error('Cancel scheduled publication error:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to cancel scheduled publication'
      };
    }
  }

  /**
   * Get scheduled publications for a user
   * @param {string} userId - The user ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} - Scheduled publications
   */
  async getScheduledPublications(userId, options = {}) {
    try {
      let query = supabase
        .from('scheduled_publications')
        .select(`
          *,
          content:content_id (
            id,
            title,
            description,
            content_type
          )
        `)
        .eq('user_id', userId);

      // Add filters
      if (options.status) {
        query = query.eq('status', options.status);
      }

      if (options.platform) {
        query = query.eq('platform', options.platform);
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      // Order by scheduled time
      query = query.order('scheduled_time', { ascending: false });

      const { data: publications, error } = await query;

      if (error) {
        return {
          success: false,
          error: error.message,
          message: 'Failed to fetch scheduled publications'
        };
      }

      return {
        success: true,
        scheduled_publications: publications,
        count: publications.length
      };
    } catch (error) {
      console.error('Get scheduled publications error:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to fetch scheduled publications'
      };
    }
  }
}

module.exports = new SchedulingService();
