const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

/**
 * Service for interacting with n8n API
 */
class N8nService {
  /**
   * Constructor for the N8nService
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || process.env.N8N_API_URL || 'http://localhost:5678';
    this.apiKey = options.apiKey || process.env.N8N_API_KEY;
    this.webhookBaseUrl = options.webhookBaseUrl || process.env.N8N_WEBHOOK_BASE_URL || this.baseUrl;
    
    // Initialize axios instance with default config
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        ...(this.apiKey ? { 'X-N8N-API-KEY': this.apiKey } : {})
      },
      timeout: 30000 // 30 seconds timeout
    });
  }

  /**
   * Get all workflows from n8n
   * @param {Object} options - Query options
   * @returns {Promise<Object>} - Response with workflows
   */
  async getWorkflows(options = {}) {
    try {
      const { active, tags } = options;
      let url = '/workflows';
      
      // Add query parameters if provided
      const queryParams = [];
      if (active !== undefined) {
        queryParams.push(`active=${active}`);
      }
      if (tags && tags.length > 0) {
        queryParams.push(`tags=${tags.join(',')}`);
      }
      
      if (queryParams.length > 0) {
        url += `?${queryParams.join('&')}`;
      }
      
      const response = await this.client.get(url);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error fetching workflows from n8n:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get a specific workflow by ID
   * @param {string} workflowId - The ID of the workflow to fetch
   * @returns {Promise<Object>} - Response with workflow data
   */
  async getWorkflow(workflowId) {
    try {
      const response = await this.client.get(`/workflows/${workflowId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error(`Error fetching workflow ${workflowId} from n8n:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Create a new workflow in n8n
   * @param {Object} workflowData - The workflow data to create
   * @returns {Promise<Object>} - Response with created workflow
   */
  async createWorkflow(workflowData) {
    try {
      // Ensure the workflow has a name
      if (!workflowData.name) {
        workflowData.name = `Workflow ${new Date().toISOString()}`;
      }
      
      const response = await this.client.post('/workflows', workflowData);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error creating workflow in n8n:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Update an existing workflow in n8n
   * @param {string} workflowId - The ID of the workflow to update
   * @param {Object} workflowData - The updated workflow data
   * @returns {Promise<Object>} - Response with updated workflow
   */
  async updateWorkflow(workflowId, workflowData) {
    try {
      const response = await this.client.put(`/workflows/${workflowId}`, workflowData);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error(`Error updating workflow ${workflowId} in n8n:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Delete a workflow from n8n
   * @param {string} workflowId - The ID of the workflow to delete
   * @returns {Promise<Object>} - Response with deletion status
   */
  async deleteWorkflow(workflowId) {
    try {
      const response = await this.client.delete(`/workflows/${workflowId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error(`Error deleting workflow ${workflowId} from n8n:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Activate a workflow in n8n
   * @param {string} workflowId - The ID of the workflow to activate
   * @returns {Promise<Object>} - Response with activation status
   */
  async activateWorkflow(workflowId) {
    try {
      const response = await this.client.post(`/workflows/${workflowId}/activate`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error(`Error activating workflow ${workflowId} in n8n:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Deactivate a workflow in n8n
   * @param {string} workflowId - The ID of the workflow to deactivate
   * @returns {Promise<Object>} - Response with deactivation status
   */
  async deactivateWorkflow(workflowId) {
    try {
      const response = await this.client.post(`/workflows/${workflowId}/deactivate`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error(`Error deactivating workflow ${workflowId} in n8n:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Execute a workflow in n8n
   * @param {string} workflowId - The ID of the workflow to execute
   * @param {Object} data - The data to pass to the workflow
   * @returns {Promise<Object>} - Response with execution result
   */
  async executeWorkflow(workflowId, data = {}) {
    try {
      const response = await this.client.post(`/workflows/${workflowId}/execute`, data);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error(`Error executing workflow ${workflowId} in n8n:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get workflow executions
   * @param {string} workflowId - The ID of the workflow
   * @param {Object} options - Query options
   * @returns {Promise<Object>} - Response with executions
   */
  async getExecutions(workflowId, options = {}) {
    try {
      const { limit = 20, lastId, status } = options;
      let url = `/executions`;
      
      // Add query parameters
      const queryParams = [`limit=${limit}`];
      if (workflowId) {
        queryParams.push(`workflowId=${workflowId}`);
      }
      if (lastId) {
        queryParams.push(`lastId=${lastId}`);
      }
      if (status) {
        queryParams.push(`status=${status}`);
      }
      
      url += `?${queryParams.join('&')}`;
      
      const response = await this.client.get(url);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error fetching executions from n8n:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get a specific execution by ID
   * @param {string} executionId - The ID of the execution to fetch
   * @returns {Promise<Object>} - Response with execution data
   */
  async getExecution(executionId) {
    try {
      const response = await this.client.get(`/executions/${executionId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error(`Error fetching execution ${executionId} from n8n:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Delete an execution from n8n
   * @param {string} executionId - The ID of the execution to delete
   * @returns {Promise<Object>} - Response with deletion status
   */
  async deleteExecution(executionId) {
    try {
      const response = await this.client.delete(`/executions/${executionId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error(`Error deleting execution ${executionId} from n8n:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Create a webhook URL for n8n to call back to our application
   * @param {string} workflowId - The ID of the workflow
   * @param {string} event - The event type
   * @returns {string} - The webhook URL
   */
  createWebhookUrl(workflowId, event) {
    const webhookId = uuidv4();
    return `${this.webhookBaseUrl}/webhook/${webhookId}?workflowId=${workflowId}&event=${event}`;
  }

  /**
   * Get available workflow templates
   * @returns {Promise<Object>} - Response with templates
   */
  async getTemplates() {
    // This is a placeholder for a real implementation
    // In a real implementation, you would fetch templates from n8n or your own template repository
    return {
      success: true,
      data: [
        {
          id: 'content-creation',
          name: 'Content Creation Workflow',
          description: 'A workflow for creating and publishing content',
          tags: ['content', 'creation', 'publishing'],
          nodes: [
            {
              id: 'start',
              type: 'n8n-nodes-base.start',
              position: [100, 300]
            },
            {
              id: 'contentCreation',
              type: 'n8n-nodes-base.function',
              position: [300, 300],
              parameters: {
                functionCode: 'return { data: { content: "Sample content" } };'
              }
            },
            {
              id: 'contentPublishing',
              type: 'n8n-nodes-base.function',
              position: [500, 300],
              parameters: {
                functionCode: 'return { data: { published: true } };'
              }
            }
          ],
          connections: {
            start: {
              main: [
                [
                  {
                    node: 'contentCreation',
                    type: 'main',
                    index: 0
                  }
                ]
              ]
            },
            contentCreation: {
              main: [
                [
                  {
                    node: 'contentPublishing',
                    type: 'main',
                    index: 0
                  }
                ]
              ]
            }
          }
        },
        {
          id: 'content-distribution',
          name: 'Content Distribution Workflow',
          description: 'A workflow for distributing content to multiple platforms',
          tags: ['content', 'distribution', 'social media'],
          nodes: [
            {
              id: 'start',
              type: 'n8n-nodes-base.start',
              position: [100, 300]
            },
            {
              id: 'contentPreparation',
              type: 'n8n-nodes-base.function',
              position: [300, 300],
              parameters: {
                functionCode: 'return { data: { content: "Sample content for distribution" } };'
              }
            },
            {
              id: 'socialMediaDistribution',
              type: 'n8n-nodes-base.function',
              position: [500, 300],
              parameters: {
                functionCode: 'return { data: { distributed: true } };'
              }
            }
          ],
          connections: {
            start: {
              main: [
                [
                  {
                    node: 'contentPreparation',
                    type: 'main',
                    index: 0
                  }
                ]
              ]
            },
            contentPreparation: {
              main: [
                [
                  {
                    node: 'socialMediaDistribution',
                    type: 'main',
                    index: 0
                  }
                ]
              ]
            }
          }
        },
        {
          id: 'content-analytics',
          name: 'Content Analytics Workflow',
          description: 'A workflow for collecting and analyzing content performance',
          tags: ['content', 'analytics', 'reporting'],
          nodes: [
            {
              id: 'start',
              type: 'n8n-nodes-base.start',
              position: [100, 300]
            },
            {
              id: 'dataCollection',
              type: 'n8n-nodes-base.function',
              position: [300, 300],
              parameters: {
                functionCode: 'return { data: { metrics: { views: 1000, likes: 500, shares: 200 } } };'
              }
            },
            {
              id: 'dataAnalysis',
              type: 'n8n-nodes-base.function',
              position: [500, 300],
              parameters: {
                functionCode: 'return { data: { analysis: "Performance is good" } };'
              }
            },
            {
              id: 'reportGeneration',
              type: 'n8n-nodes-base.function',
              position: [700, 300],
              parameters: {
                functionCode: 'return { data: { report: "Content Performance Report" } };'
              }
            }
          ],
          connections: {
            start: {
              main: [
                [
                  {
                    node: 'dataCollection',
                    type: 'main',
                    index: 0
                  }
                ]
              ]
            },
            dataCollection: {
              main: [
                [
                  {
                    node: 'dataAnalysis',
                    type: 'main',
                    index: 0
                  }
                ]
              ]
            },
            dataAnalysis: {
              main: [
                [
                  {
                    node: 'reportGeneration',
                    type: 'main',
                    index: 0
                  }
                ]
              ]
            }
          }
        }
      ]
    };
  }

  /**
   * Get a specific workflow template by ID
   * @param {string} templateId - The ID of the template to fetch
   * @returns {Promise<Object>} - Response with template data
   */
  async getTemplate(templateId) {
    const templates = await this.getTemplates();
    if (!templates.success) {
      return templates;
    }
    
    const template = templates.data.find(t => t.id === templateId);
    if (!template) {
      return {
        success: false,
        error: `Template with ID ${templateId} not found`
      };
    }
    
    return {
      success: true,
      data: template
    };
  }

  /**
   * Create a workflow from a template
   * @param {string} templateId - The ID of the template to use
   * @param {Object} customizations - Customizations to apply to the template
   * @returns {Promise<Object>} - Response with created workflow
   */
  async createWorkflowFromTemplate(templateId, customizations = {}) {
    try {
      // Get the template
      const templateResult = await this.getTemplate(templateId);
      if (!templateResult.success) {
        return templateResult;
      }
      
      const template = templateResult.data;
      
      // Create a new workflow based on the template
      const workflowData = {
        name: customizations.name || template.name,
        nodes: template.nodes,
        connections: template.connections,
        active: customizations.active || false,
        tags: customizations.tags || template.tags
      };
      
      // Apply customizations to nodes if provided
      if (customizations.nodes) {
        for (const nodeId in customizations.nodes) {
          const nodeIndex = workflowData.nodes.findIndex(n => n.id === nodeId);
          if (nodeIndex !== -1) {
            workflowData.nodes[nodeIndex] = {
              ...workflowData.nodes[nodeIndex],
              ...customizations.nodes[nodeId]
            };
          }
        }
      }
      
      // Create the workflow in n8n
      return await this.createWorkflow(workflowData);
    } catch (error) {
      console.error(`Error creating workflow from template ${templateId}:`, error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Create and export a singleton instance
const n8nService = new N8nService();
module.exports = n8nService;
