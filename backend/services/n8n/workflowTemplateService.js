const fs = require('fs').promises;
const path = require('path');
const n8nService = require('./n8nService');

/**
 * Service for managing workflow templates
 */
class WorkflowTemplateService {
  constructor() {
    this.templatesPath = path.join(__dirname, '../../../workflows');
    this.loadedTemplates = new Map();
  }

  /**
   * Load all workflow templates from the workflows directory
   * @returns {Promise<Array>} Array of workflow templates
   */
  async loadTemplates() {
    try {
      const files = await fs.readdir(this.templatesPath);
      const jsonFiles = files.filter(file => file.endsWith('.json'));
      
      const templates = [];
      
      for (const file of jsonFiles) {
        try {
          const filePath = path.join(this.templatesPath, file);
          const content = await fs.readFile(filePath, 'utf8');
          const template = JSON.parse(content);
          
          // Add metadata
          const templateId = file.replace('.json', '');
          const templateWithMeta = {
            id: templateId,
            filename: file,
            ...template,
            source: 'file',
            loadedAt: new Date().toISOString()
          };
          
          templates.push(templateWithMeta);
          this.loadedTemplates.set(templateId, templateWithMeta);
        } catch (error) {
          console.error(`Error loading template ${file}:`, error);
        }
      }
      
      console.log(`Loaded ${templates.length} workflow templates`);
      return templates;
    } catch (error) {
      console.error('Error loading workflow templates:', error);
      return [];
    }
  }

  /**
   * Get a specific template by ID
   * @param {string} templateId - The ID of the template
   * @returns {Promise<Object|null>} The template or null if not found
   */
  async getTemplate(templateId) {
    if (this.loadedTemplates.has(templateId)) {
      return this.loadedTemplates.get(templateId);
    }
    
    // Try to load templates if not already loaded
    await this.loadTemplates();
    return this.loadedTemplates.get(templateId) || null;
  }

  /**
   * Get all available templates
   * @returns {Promise<Array>} Array of all templates
   */
  async getAllTemplates() {
    if (this.loadedTemplates.size === 0) {
      await this.loadTemplates();
    }
    
    return Array.from(this.loadedTemplates.values());
  }

  /**
   * Import a template into n8n
   * @param {string} templateId - The ID of the template to import
   * @param {Object} options - Import options
   * @returns {Promise<Object>} Result of the import operation
   */
  async importTemplateToN8n(templateId, options = {}) {
    try {
      const template = await this.getTemplate(templateId);
      
      if (!template) {
        return {
          success: false,
          error: `Template ${templateId} not found`
        };
      }

      // Prepare workflow data for n8n
      const workflowData = {
        name: options.name || template.name,
        nodes: template.nodes || [],
        connections: template.connections || {},
        active: options.active || false,
        tags: options.tags || ['contentforge', 'template', templateId]
      };

      // Create workflow in n8n
      const result = await n8nService.createWorkflow(workflowData);
      
      if (result.success) {
        console.log(`Successfully imported template ${templateId} to n8n as workflow ${result.data.id}`);
      }
      
      return result;
    } catch (error) {
      console.error(`Error importing template ${templateId} to n8n:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a new template from an existing n8n workflow
   * @param {string} n8nWorkflowId - The n8n workflow ID
   * @param {string} templateId - The new template ID
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} Result of the export operation
   */
  async exportFromN8n(n8nWorkflowId, templateId, metadata = {}) {
    try {
      // Get workflow from n8n
      const workflowResult = await n8nService.getWorkflow(n8nWorkflowId);
      
      if (!workflowResult.success) {
        return {
          success: false,
          error: `Failed to get workflow from n8n: ${workflowResult.error}`
        };
      }

      const workflow = workflowResult.data;
      
      // Create template structure
      const template = {
        name: metadata.name || workflow.name,
        description: metadata.description || `Exported from n8n workflow ${n8nWorkflowId}`,
        nodes: workflow.nodes || [],
        connections: workflow.connections || {},
        tags: metadata.tags || ['exported', 'n8n'],
        exportedAt: new Date().toISOString(),
        sourceWorkflowId: n8nWorkflowId
      };

      // Save template to file
      const filename = `${templateId}.json`;
      const filePath = path.join(this.templatesPath, filename);
      
      await fs.writeFile(filePath, JSON.stringify(template, null, 2));
      
      // Update loaded templates cache
      const templateWithMeta = {
        id: templateId,
        filename,
        ...template,
        source: 'exported',
        loadedAt: new Date().toISOString()
      };
      
      this.loadedTemplates.set(templateId, templateWithMeta);
      
      console.log(`Successfully exported n8n workflow ${n8nWorkflowId} as template ${templateId}`);
      
      return {
        success: true,
        data: templateWithMeta
      };
    } catch (error) {
      console.error(`Error exporting workflow ${n8nWorkflowId} as template:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validate a template structure
   * @param {Object} template - The template to validate
   * @returns {Object} Validation result
   */
  validateTemplate(template) {
    const errors = [];
    
    if (!template.name) {
      errors.push('Template name is required');
    }
    
    if (!template.nodes || !Array.isArray(template.nodes)) {
      errors.push('Template must have a nodes array');
    }
    
    if (!template.connections || typeof template.connections !== 'object') {
      errors.push('Template must have a connections object');
    }
    
    // Validate nodes
    if (template.nodes) {
      template.nodes.forEach((node, index) => {
        if (!node.name) {
          errors.push(`Node at index ${index} is missing a name`);
        }
        if (!node.type) {
          errors.push(`Node at index ${index} is missing a type`);
        }
        if (!node.position || !Array.isArray(node.position) || node.position.length !== 2) {
          errors.push(`Node at index ${index} has invalid position`);
        }
      });
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get template statistics
   * @returns {Promise<Object>} Template statistics
   */
  async getTemplateStats() {
    const templates = await this.getAllTemplates();
    
    const stats = {
      total: templates.length,
      bySource: {},
      byType: {},
      recentlyAdded: []
    };
    
    templates.forEach(template => {
      // Count by source
      stats.bySource[template.source] = (stats.bySource[template.source] || 0) + 1;
      
      // Count by type (based on name patterns)
      let type = 'other';
      if (template.name.toLowerCase().includes('blog')) type = 'blog';
      else if (template.name.toLowerCase().includes('social')) type = 'social';
      else if (template.name.toLowerCase().includes('video')) type = 'video';
      else if (template.name.toLowerCase().includes('image')) type = 'image';
      
      stats.byType[type] = (stats.byType[type] || 0) + 1;
    });
    
    // Get recently added templates (last 7 days)
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    
    stats.recentlyAdded = templates.filter(template => {
      const loadedDate = new Date(template.loadedAt || template.exportedAt || 0);
      return loadedDate > weekAgo;
    });
    
    return stats;
  }
}

// Create and export singleton instance
const workflowTemplateService = new WorkflowTemplateService();
module.exports = workflowTemplateService;
