const BasePlatformService = require('../basePlatformService');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs').promises;
const path = require('path');

/**
 * LinkedIn Platform Service for publishing content to LinkedIn
 */
class LinkedInService extends BasePlatformService {
  /**
   * Constructor for the LinkedInService
   * @param {Object} options - Configuration options for the service
   */
  constructor(options = {}) {
    super(options);
    this.platformName = 'linkedin';
    this.supportedContentTypes = [
      'text/plain',
      'image/jpeg',
      'image/png',
      'video/mp4',
      'application/pdf',
      'article' // Special type for article posts
    ];
    this.supportedActions = ['publish', 'update', 'delete', 'get', 'list', 'analytics', 'schedule'];
    
    // Set default options
    this.options = {
      apiVersion: 'v2',
      ...options
    };
    
    // Initialize API client
    this.apiBaseUrl = `https://api.linkedin.com/${this.options.apiVersion}`;
    this.client = axios.create({
      baseURL: this.apiBaseUrl,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Initialize the LinkedIn service with credentials
   * @param {Object} credentials - The credentials for LinkedIn
   * @returns {Promise<Object>} - Initialization result
   */
  async initialize(credentials) {
    try {
      if (!this.validateCredentials(credentials)) {
        return {
          success: false,
          error: 'Invalid credentials',
          message: 'Please provide valid LinkedIn API credentials'
        };
      }
      
      this.credentials = credentials;
      this.accessToken = credentials.access_token;
      
      // Set the authorization header for all requests
      this.client.defaults.headers.common['Authorization'] = `Bearer ${this.accessToken}`;
      
      // Test the connection by getting profile info
      const response = await this.client.get('/me');
      
      if (response.data && response.data.id) {
        this.profileInfo = response.data;
        this.isConfigured = true;
        
        // Get organization info if organization ID is provided
        if (credentials.organization_id) {
          this.organizationId = credentials.organization_id;
          const orgResponse = await this.client.get(`/organizations/${this.organizationId}`);
          
          if (orgResponse.data && orgResponse.data.id) {
            this.organizationInfo = orgResponse.data;
          }
        }
        
        return {
          success: true,
          message: 'LinkedIn service initialized successfully',
          profileInfo: {
            id: this.profileInfo.id,
            firstName: this.profileInfo.localizedFirstName,
            lastName: this.profileInfo.localizedLastName
          },
          organizationInfo: this.organizationInfo ? {
            id: this.organizationInfo.id,
            name: this.organizationInfo.localizedName
          } : null
        };
      } else {
        this.isConfigured = false;
        return {
          success: false,
          error: 'Profile not found',
          message: 'Could not find LinkedIn profile with the provided credentials'
        };
      }
    } catch (error) {
      this.isConfigured = false;
      console.error('LinkedIn initialization error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to initialize LinkedIn service'
      };
    }
  }

  /**
   * Validate the provided credentials
   * @param {Object} credentials - The credentials to validate
   * @returns {boolean} - Whether the credentials are valid
   */
  validateCredentials(credentials) {
    return (
      credentials &&
      credentials.access_token
    );
  }

  /**
   * Publish content to LinkedIn
   * @param {Object} content - The content to publish
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing result
   */
  async publish(content, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!this.supportsContentType(content.type)) {
        return {
          success: false,
          error: 'Unsupported content type',
          message: `Content type '${content.type}' is not supported by LinkedIn`
        };
      }

      // Determine the author
      const author = options.asOrganization && this.organizationId
        ? `urn:li:organization:${this.organizationId}`
        : `urn:li:person:${this.profileInfo.id}`;

      // Handle different content types
      if (content.type === 'text/plain') {
        return await this.publishTextPost(content, author, options);
      } else if (content.type.startsWith('image/')) {
        return await this.publishImagePost(content, author, options);
      } else if (content.type.startsWith('video/')) {
        return await this.publishVideoPost(content, author, options);
      } else if (content.type === 'application/pdf') {
        return await this.publishDocumentPost(content, author, options);
      } else if (content.type === 'article') {
        return await this.publishArticle(content, author, options);
      } else {
        return {
          success: false,
          error: 'Unsupported content type',
          message: `Content type '${content.type}' is not supported by LinkedIn`
        };
      }
    } catch (error) {
      console.error('LinkedIn publish error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to publish content to LinkedIn'
      };
    }
  }

  /**
   * Publish a text post to LinkedIn
   * @param {Object} content - The text content to publish
   * @param {string} author - The URN of the author
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing result
   */
  async publishTextPost(content, author, options = {}) {
    try {
      if (!content.text) {
        return {
          success: false,
          error: 'Missing text content',
          message: 'Text content is required for text posts'
        };
      }

      // Create the post
      const postData = {
        author,
        lifecycleState: 'PUBLISHED',
        specificContent: {
          'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
              text: content.text
            },
            shareMediaCategory: 'NONE'
          }
        },
        visibility: {
          'com.linkedin.ugc.MemberNetworkVisibility': options.visibility || 'PUBLIC'
        }
      };

      const response = await this.client.post('/ugcPosts', postData);

      if (!response.data || !response.data.id) {
        throw new Error('Failed to publish text post');
      }

      return {
        success: true,
        message: 'Text post published successfully to LinkedIn',
        postId: response.data.id,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn text post error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to publish text post to LinkedIn'
      };
    }
  }

  /**
   * Publish an image post to LinkedIn
   * @param {Object} content - The image content to publish
   * @param {string} author - The URN of the author
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing result
   */
  async publishImagePost(content, author, options = {}) {
    try {
      if (!content.filePath && !content.fileUrl) {
        return {
          success: false,
          error: 'Missing file path or URL',
          message: 'File path or URL is required for image posts'
        };
      }

      // Step 1: Register the image upload
      const registerUploadResponse = await this.client.post('/assets?action=registerUpload', {
        registerUploadRequest: {
          recipes: ['urn:li:digitalmediaRecipe:feedshare-image'],
          owner: author,
          serviceRelationships: [
            {
              relationshipType: 'OWNER',
              identifier: 'urn:li:userGeneratedContent'
            }
          ]
        }
      });

      if (!registerUploadResponse.data || !registerUploadResponse.data.value || !registerUploadResponse.data.value.uploadMechanism) {
        throw new Error('Failed to register image upload');
      }

      const uploadMechanism = registerUploadResponse.data.value.uploadMechanism['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest'];
      const assetId = registerUploadResponse.data.value.asset;

      // Step 2: Upload the image
      let imageData;
      if (content.filePath) {
        imageData = await fs.readFile(content.filePath);
      } else if (content.fileUrl) {
        const imageResponse = await axios.get(content.fileUrl, { responseType: 'arraybuffer' });
        imageData = imageResponse.data;
      }

      await axios.put(uploadMechanism.uploadUrl, imageData, {
        headers: {
          'Content-Type': content.type
        }
      });

      // Step 3: Create the post with the uploaded image
      const postData = {
        author,
        lifecycleState: 'PUBLISHED',
        specificContent: {
          'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
              text: content.text || ''
            },
            shareMediaCategory: 'IMAGE',
            media: [
              {
                status: 'READY',
                description: {
                  text: content.description || ''
                },
                media: assetId,
                title: {
                  text: content.title || ''
                }
              }
            ]
          }
        },
        visibility: {
          'com.linkedin.ugc.MemberNetworkVisibility': options.visibility || 'PUBLIC'
        }
      };

      const response = await this.client.post('/ugcPosts', postData);

      if (!response.data || !response.data.id) {
        throw new Error('Failed to publish image post');
      }

      return {
        success: true,
        message: 'Image post published successfully to LinkedIn',
        postId: response.data.id,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn image post error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to publish image post to LinkedIn'
      };
    }
  }

  /**
   * Publish a video post to LinkedIn
   * @param {Object} content - The video content to publish
   * @param {string} author - The URN of the author
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing result
   */
  async publishVideoPost(content, author, options = {}) {
    try {
      if (!content.filePath && !content.fileUrl) {
        return {
          success: false,
          error: 'Missing file path or URL',
          message: 'File path or URL is required for video posts'
        };
      }

      // Step 1: Register the video upload
      const registerUploadResponse = await this.client.post('/assets?action=registerUpload', {
        registerUploadRequest: {
          recipes: ['urn:li:digitalmediaRecipe:feedshare-video'],
          owner: author,
          serviceRelationships: [
            {
              relationshipType: 'OWNER',
              identifier: 'urn:li:userGeneratedContent'
            }
          ]
        }
      });

      if (!registerUploadResponse.data || !registerUploadResponse.data.value || !registerUploadResponse.data.value.uploadMechanism) {
        throw new Error('Failed to register video upload');
      }

      const uploadMechanism = registerUploadResponse.data.value.uploadMechanism['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest'];
      const assetId = registerUploadResponse.data.value.asset;

      // Step 2: Upload the video
      let videoData;
      if (content.filePath) {
        videoData = await fs.readFile(content.filePath);
      } else if (content.fileUrl) {
        const videoResponse = await axios.get(content.fileUrl, { responseType: 'arraybuffer' });
        videoData = videoResponse.data;
      }

      await axios.put(uploadMechanism.uploadUrl, videoData, {
        headers: {
          'Content-Type': content.type
        }
      });

      // Step 3: Create the post with the uploaded video
      const postData = {
        author,
        lifecycleState: 'PUBLISHED',
        specificContent: {
          'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
              text: content.text || ''
            },
            shareMediaCategory: 'VIDEO',
            media: [
              {
                status: 'READY',
                description: {
                  text: content.description || ''
                },
                media: assetId,
                title: {
                  text: content.title || ''
                }
              }
            ]
          }
        },
        visibility: {
          'com.linkedin.ugc.MemberNetworkVisibility': options.visibility || 'PUBLIC'
        }
      };

      const response = await this.client.post('/ugcPosts', postData);

      if (!response.data || !response.data.id) {
        throw new Error('Failed to publish video post');
      }

      return {
        success: true,
        message: 'Video post published successfully to LinkedIn',
        postId: response.data.id,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn video post error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to publish video post to LinkedIn'
      };
    }
  }

  /**
   * Publish a document post to LinkedIn
   * @param {Object} content - The document content to publish
   * @param {string} author - The URN of the author
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing result
   */
  async publishDocumentPost(content, author, options = {}) {
    try {
      if (!content.filePath && !content.fileUrl) {
        return {
          success: false,
          error: 'Missing file path or URL',
          message: 'File path or URL is required for document posts'
        };
      }

      // Step 1: Register the document upload
      const registerUploadResponse = await this.client.post('/assets?action=registerUpload', {
        registerUploadRequest: {
          recipes: ['urn:li:digitalmediaRecipe:feedshare-document'],
          owner: author,
          serviceRelationships: [
            {
              relationshipType: 'OWNER',
              identifier: 'urn:li:userGeneratedContent'
            }
          ]
        }
      });

      if (!registerUploadResponse.data || !registerUploadResponse.data.value || !registerUploadResponse.data.value.uploadMechanism) {
        throw new Error('Failed to register document upload');
      }

      const uploadMechanism = registerUploadResponse.data.value.uploadMechanism['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest'];
      const assetId = registerUploadResponse.data.value.asset;

      // Step 2: Upload the document
      let documentData;
      if (content.filePath) {
        documentData = await fs.readFile(content.filePath);
      } else if (content.fileUrl) {
        const documentResponse = await axios.get(content.fileUrl, { responseType: 'arraybuffer' });
        documentData = documentResponse.data;
      }

      await axios.put(uploadMechanism.uploadUrl, documentData, {
        headers: {
          'Content-Type': content.type
        }
      });

      // Step 3: Create the post with the uploaded document
      const postData = {
        author,
        lifecycleState: 'PUBLISHED',
        specificContent: {
          'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
              text: content.text || ''
            },
            shareMediaCategory: 'DOCUMENT',
            media: [
              {
                status: 'READY',
                description: {
                  text: content.description || ''
                },
                media: assetId,
                title: {
                  text: content.title || ''
                }
              }
            ]
          }
        },
        visibility: {
          'com.linkedin.ugc.MemberNetworkVisibility': options.visibility || 'PUBLIC'
        }
      };

      const response = await this.client.post('/ugcPosts', postData);

      if (!response.data || !response.data.id) {
        throw new Error('Failed to publish document post');
      }

      return {
        success: true,
        message: 'Document post published successfully to LinkedIn',
        postId: response.data.id,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn document post error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to publish document post to LinkedIn'
      };
    }
  }

  /**
   * Publish an article to LinkedIn
   * @param {Object} content - The article content to publish
   * @param {string} author - The URN of the author
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing result
   */
  async publishArticle(content, author, options = {}) {
    try {
      if (!content.title || !content.text) {
        return {
          success: false,
          error: 'Missing article content',
          message: 'Title and text are required for article posts'
        };
      }

      // For articles, we need to use the LinkedIn Articles API
      // This is a simplified implementation as the full Articles API is complex
      
      // Create the article
      const articleData = {
        author,
        lifecycleState: 'PUBLISHED',
        title: content.title,
        content: {
          'com.linkedin.article.ArticleContent': {
            contentEntities: [
              {
                entityLocation: 'urn:li:article:content',
                value: content.text
              }
            ]
          }
        }
      };

      // If there's a cover image, upload it first
      if (content.coverImagePath || content.coverImageUrl) {
        // Step 1: Register the image upload
        const registerUploadResponse = await this.client.post('/assets?action=registerUpload', {
          registerUploadRequest: {
            recipes: ['urn:li:digitalmediaRecipe:article-cover-image'],
            owner: author,
            serviceRelationships: [
              {
                relationshipType: 'OWNER',
                identifier: 'urn:li:article'
              }
            ]
          }
        });

        if (!registerUploadResponse.data || !registerUploadResponse.data.value || !registerUploadResponse.data.value.uploadMechanism) {
          throw new Error('Failed to register cover image upload');
        }

        const uploadMechanism = registerUploadResponse.data.value.uploadMechanism['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest'];
        const assetId = registerUploadResponse.data.value.asset;

        // Step 2: Upload the image
        let imageData;
        if (content.coverImagePath) {
          imageData = await fs.readFile(content.coverImagePath);
        } else if (content.coverImageUrl) {
          const imageResponse = await axios.get(content.coverImageUrl, { responseType: 'arraybuffer' });
          imageData = imageResponse.data;
        }

        await axios.put(uploadMechanism.uploadUrl, imageData, {
          headers: {
            'Content-Type': 'image/jpeg' // Assuming JPEG for cover images
          }
        });

        // Add the cover image to the article data
        articleData.coverImage = assetId;
      }

      const response = await this.client.post('/articles', articleData);

      if (!response.data || !response.data.id) {
        throw new Error('Failed to publish article');
      }

      return {
        success: true,
        message: 'Article published successfully to LinkedIn',
        articleId: response.data.id,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn article error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to publish article to LinkedIn'
      };
    }
  }

  /**
   * Update a published post on LinkedIn
   * @param {string} postId - The ID of the post to update
   * @param {Object} content - The updated content
   * @param {Object} options - Update options
   * @returns {Promise<Object>} - Update result
   */
  async update(postId, content, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!postId) {
        return {
          success: false,
          error: 'Missing post ID',
          message: 'Post ID is required for update'
        };
      }

      // LinkedIn doesn't support direct updates to posts
      // The recommended approach is to delete the old post and create a new one
      
      return {
        success: false,
        error: 'Not supported',
        message: 'LinkedIn does not support direct updates to posts. Please delete the post and create a new one.'
      };
    } catch (error) {
      console.error('LinkedIn update error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to update post on LinkedIn'
      };
    }
  }

  /**
   * Delete a published post from LinkedIn
   * @param {string} postId - The ID of the post to delete
   * @param {Object} options - Deletion options
   * @returns {Promise<Object>} - Deletion result
   */
  async delete(postId, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!postId) {
        return {
          success: false,
          error: 'Missing post ID',
          message: 'Post ID is required for deletion'
        };
      }

      // Delete the post
      await this.client.delete(`/ugcPosts/${postId}`);

      return {
        success: true,
        message: 'Post deleted successfully from LinkedIn',
        postId
      };
    } catch (error) {
      console.error('LinkedIn delete error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to delete post from LinkedIn'
      };
    }
  }

  /**
   * Get a published post from LinkedIn
   * @param {string} postId - The ID of the post to retrieve
   * @param {Object} options - Retrieval options
   * @returns {Promise<Object>} - Retrieval result
   */
  async get(postId, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!postId) {
        return {
          success: false,
          error: 'Missing post ID',
          message: 'Post ID is required for retrieval'
        };
      }

      // Get the post
      const response = await this.client.get(`/ugcPosts/${postId}`);

      if (!response.data) {
        throw new Error('Failed to retrieve post');
      }

      return {
        success: true,
        message: 'Post retrieved successfully from LinkedIn',
        post: response.data,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn get error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to retrieve post from LinkedIn'
      };
    }
  }

  /**
   * List published posts from LinkedIn
   * @param {Object} options - Listing options
   * @returns {Promise<Object>} - Listing result
   */
  async list(options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      // Determine the author
      const author = options.asOrganization && this.organizationId
        ? `urn:li:organization:${this.organizationId}`
        : `urn:li:person:${this.profileInfo.id}`;

      // Get the posts
      const response = await this.client.get('/ugcPosts', {
        params: {
          q: 'authors',
          authors: [author],
          count: options.count || 10,
          start: options.start || 0
        }
      });

      if (!response.data || !response.data.elements) {
        throw new Error('Failed to list posts');
      }

      return {
        success: true,
        message: 'Posts retrieved successfully from LinkedIn',
        posts: response.data.elements,
        paging: {
          count: response.data.paging.count,
          start: response.data.paging.start,
          total: response.data.paging.total
        },
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn list error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to list posts from LinkedIn'
      };
    }
  }

  /**
   * Get analytics for a published post from LinkedIn
   * @param {string} postId - The ID of the post to get analytics for
   * @param {Object} options - Analytics options
   * @returns {Promise<Object>} - Analytics result
   */
  async getAnalytics(postId, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!postId) {
        return {
          success: false,
          error: 'Missing post ID',
          message: 'Post ID is required for analytics'
        };
      }

      // Get the post analytics
      const response = await this.client.get(`/socialActions/${postId}`);

      if (!response.data) {
        throw new Error('Failed to retrieve post analytics');
      }

      // Format the analytics data
      const analytics = {
        likes: response.data.likesSummary?.totalLikes || 0,
        comments: response.data.commentsSummary?.totalComments || 0,
        shares: response.data.sharesSummary?.totalShares || 0
      };

      return {
        success: true,
        message: 'Post analytics retrieved successfully from LinkedIn',
        analytics,
        data: response.data
      };
    } catch (error) {
      console.error('LinkedIn analytics error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to retrieve post analytics from LinkedIn'
      };
    }
  }

  /**
   * Schedule content for publishing on LinkedIn
   * @param {Object} content - The content to schedule
   * @param {Date} publishDate - The date to publish the content
   * @param {Object} options - Scheduling options
   * @returns {Promise<Object>} - Scheduling result
   */
  async schedule(content, publishDate, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!this.supportsContentType(content.type)) {
        return {
          success: false,
          error: 'Unsupported content type',
          message: `Content type '${content.type}' is not supported by LinkedIn`
        };
      }

      if (!publishDate || !(publishDate instanceof Date)) {
        return {
          success: false,
          error: 'Invalid publish date',
          message: 'A valid publish date is required for scheduling'
        };
      }

      // Ensure publish date is in the future
      const now = new Date();
      if (publishDate <= now) {
        return {
          success: false,
          error: 'Invalid publish date',
          message: 'Publish date must be in the future'
        };
      }

      // Determine the author
      const author = options.asOrganization && this.organizationId
        ? `urn:li:organization:${this.organizationId}`
        : `urn:li:person:${this.profileInfo.id}`;

      // LinkedIn doesn't support native scheduling through the API
      // We'll return an error message with this information
      
      return {
        success: false,
        error: 'Not supported',
        message: 'LinkedIn does not support native scheduling through the API. Please use a third-party scheduling tool or implement a custom scheduling solution.'
      };
    } catch (error) {
      console.error('LinkedIn schedule error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Failed to schedule content on LinkedIn'
      };
    }
  }
}

module.exports = LinkedInService;
