const BasePlatformService = require('../basePlatformService');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs').promises;
const path = require('path');

/**
 * Instagram Platform Service for publishing content to Instagram
 * Uses the Facebook Graph API for Instagram Business accounts
 */
class InstagramService extends BasePlatformService {
  /**
   * Constructor for the InstagramService
   * @param {Object} options - Configuration options for the service
   */
  constructor(options = {}) {
    super(options);
    this.platformName = 'instagram';
    this.supportedContentTypes = [
      'image/jpeg', 
      'image/png', 
      'video/mp4',
      'image/carousel', // Special type for carousel posts
      'video/carousel'  // Special type for carousel posts with videos
    ];
    this.supportedActions = ['publish', 'delete', 'get', 'list', 'analytics'];
    
    // Set default options
    this.options = {
      apiVersion: 'v18.0',
      ...options
    };
    
    // Initialize API client
    this.apiBaseUrl = `https://graph.facebook.com/${this.options.apiVersion}`;
    this.client = axios.create({
      baseURL: this.apiBaseUrl
    });
  }

  /**
   * Initialize the Instagram service with credentials
   * @param {Object} credentials - The credentials for Instagram
   * @returns {Promise<Object>} - Initialization result
   */
  async initialize(credentials) {
    try {
      if (!this.validateCredentials(credentials)) {
        return {
          success: false,
          error: 'Invalid credentials',
          message: 'Please provide valid Instagram API credentials'
        };
      }
      
      this.credentials = credentials;
      this.accessToken = credentials.access_token;
      this.instagramBusinessAccountId = credentials.instagram_business_account_id;
      
      // Test the connection by getting account info
      const response = await this.client.get(`/${this.instagramBusinessAccountId}`, {
        params: {
          fields: 'name,username,profile_picture_url,ig_id',
          access_token: this.accessToken
        }
      });
      
      if (response.data && response.data.id) {
        this.accountInfo = response.data;
        this.isConfigured = true;
        
        return {
          success: true,
          message: 'Instagram service initialized successfully',
          accountInfo: {
            id: this.accountInfo.id,
            username: this.accountInfo.username,
            name: this.accountInfo.name,
            profilePictureUrl: this.accountInfo.profile_picture_url
          }
        };
      } else {
        this.isConfigured = false;
        return {
          success: false,
          error: 'Account not found',
          message: 'Could not find Instagram business account with the provided credentials'
        };
      }
    } catch (error) {
      this.isConfigured = false;
      console.error('Instagram initialization error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
        message: 'Failed to initialize Instagram service'
      };
    }
  }

  /**
   * Validate the provided credentials
   * @param {Object} credentials - The credentials to validate
   * @returns {boolean} - Whether the credentials are valid
   */
  validateCredentials(credentials) {
    return (
      credentials &&
      credentials.access_token &&
      credentials.instagram_business_account_id
    );
  }

  /**
   * Publish content to Instagram
   * @param {Object} content - The content to publish
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing result
   */
  async publish(content, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!this.supportsContentType(content.type)) {
        return {
          success: false,
          error: 'Unsupported content type',
          message: `Content type '${content.type}' is not supported by Instagram`
        };
      }

      if (!content.filePath && !content.fileUrls) {
        return {
          success: false,
          error: 'Missing file path or URLs',
          message: 'File path or URLs are required for Instagram publishing'
        };
      }

      // Handle different content types
      if (content.type === 'image/carousel' || content.type === 'video/carousel') {
        return await this.publishCarousel(content, options);
      } else if (content.type.startsWith('image/')) {
        return await this.publishImage(content, options);
      } else if (content.type.startsWith('video/')) {
        return await this.publishVideo(content, options);
      } else {
        return {
          success: false,
          error: 'Unsupported content type',
          message: `Content type '${content.type}' is not supported by Instagram`
        };
      }
    } catch (error) {
      console.error('Instagram publish error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
        message: 'Failed to publish content to Instagram'
      };
    }
  }

  /**
   * Publish an image to Instagram
   * @param {Object} content - The image content to publish
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing result
   */
  async publishImage(content, options = {}) {
    try {
      // Step 1: Upload the image to get a container ID
      let imageUrl;
      if (content.filePath) {
        // Upload from local file
        const formData = new FormData();
        formData.append('image_url', await fs.readFile(content.filePath));
        formData.append('caption', content.caption || '');
        formData.append('access_token', this.accessToken);
        
        const response = await this.client.post(
          `/${this.instagramBusinessAccountId}/media`,
          formData,
          {
            headers: formData.getHeaders()
          }
        );
        
        if (!response.data || !response.data.id) {
          throw new Error('Failed to upload image');
        }
        
        imageUrl = response.data.id;
      } else if (content.fileUrl) {
        // Use remote URL
        const response = await this.client.post(
          `/${this.instagramBusinessAccountId}/media`,
          null,
          {
            params: {
              image_url: content.fileUrl,
              caption: content.caption || '',
              access_token: this.accessToken
            }
          }
        );
        
        if (!response.data || !response.data.id) {
          throw new Error('Failed to upload image from URL');
        }
        
        imageUrl = response.data.id;
      } else {
        return {
          success: false,
          error: 'Missing file path or URL',
          message: 'File path or URL is required for image upload'
        };
      }
      
      // Step 2: Publish the container
      const publishResponse = await this.client.post(
        `/${this.instagramBusinessAccountId}/media_publish`,
        null,
        {
          params: {
            creation_id: imageUrl,
            access_token: this.accessToken
          }
        }
      );
      
      if (!publishResponse.data || !publishResponse.data.id) {
        throw new Error('Failed to publish image');
      }
      
      // Step 3: Get the published post details
      const postDetails = await this.client.get(
        `/${publishResponse.data.id}`,
        {
          params: {
            fields: 'id,permalink,media_type,media_url,timestamp,caption',
            access_token: this.accessToken
          }
        }
      );
      
      return {
        success: true,
        message: 'Image published successfully to Instagram',
        postId: publishResponse.data.id,
        permalink: postDetails.data.permalink,
        mediaUrl: postDetails.data.media_url,
        data: postDetails.data
      };
    } catch (error) {
      console.error('Instagram image publish error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
        message: 'Failed to publish image to Instagram'
      };
    }
  }

  /**
   * Publish a video to Instagram
   * @param {Object} content - The video content to publish
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing result
   */
  async publishVideo(content, options = {}) {
    try {
      // Step 1: Upload the video to get a container ID
      let videoUrl;
      if (content.filePath) {
        // For video uploads, we need to use a two-phase approach
        // First, initialize the container
        const initResponse = await this.client.post(
          `/${this.instagramBusinessAccountId}/media`,
          null,
          {
            params: {
              media_type: 'VIDEO',
              video_url: content.fileUrl || 'https://placeholder.url', // Temporary URL
              caption: content.caption || '',
              access_token: this.accessToken
            }
          }
        );
        
        if (!initResponse.data || !initResponse.data.id) {
          throw new Error('Failed to initialize video container');
        }
        
        // Then, upload the actual video
        const formData = new FormData();
        formData.append('video_url', await fs.readFile(content.filePath));
        formData.append('access_token', this.accessToken);
        
        const uploadResponse = await this.client.post(
          `/${initResponse.data.id}`,
          formData,
          {
            headers: formData.getHeaders()
          }
        );
        
        if (!uploadResponse.data || !uploadResponse.data.id) {
          throw new Error('Failed to upload video');
        }
        
        videoUrl = uploadResponse.data.id;
      } else if (content.fileUrl) {
        // Use remote URL
        const response = await this.client.post(
          `/${this.instagramBusinessAccountId}/media`,
          null,
          {
            params: {
              media_type: 'VIDEO',
              video_url: content.fileUrl,
              caption: content.caption || '',
              access_token: this.accessToken
            }
          }
        );
        
        if (!response.data || !response.data.id) {
          throw new Error('Failed to upload video from URL');
        }
        
        videoUrl = response.data.id;
      } else {
        return {
          success: false,
          error: 'Missing file path or URL',
          message: 'File path or URL is required for video upload'
        };
      }
      
      // Step 2: Check the upload status
      let status = 'IN_PROGRESS';
      let statusResponse;
      let attempts = 0;
      const maxAttempts = 30; // Maximum number of status check attempts
      
      while (status === 'IN_PROGRESS' && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds between checks
        
        statusResponse = await this.client.get(
          `/${videoUrl}`,
          {
            params: {
              fields: 'status_code',
              access_token: this.accessToken
            }
          }
        );
        
        status = statusResponse.data.status_code;
        attempts++;
      }
      
      if (status !== 'FINISHED') {
        throw new Error(`Video processing failed with status: ${status}`);
      }
      
      // Step 3: Publish the container
      const publishResponse = await this.client.post(
        `/${this.instagramBusinessAccountId}/media_publish`,
        null,
        {
          params: {
            creation_id: videoUrl,
            access_token: this.accessToken
          }
        }
      );
      
      if (!publishResponse.data || !publishResponse.data.id) {
        throw new Error('Failed to publish video');
      }
      
      // Step 4: Get the published post details
      const postDetails = await this.client.get(
        `/${publishResponse.data.id}`,
        {
          params: {
            fields: 'id,permalink,media_type,media_url,timestamp,caption',
            access_token: this.accessToken
          }
        }
      );
      
      return {
        success: true,
        message: 'Video published successfully to Instagram',
        postId: publishResponse.data.id,
        permalink: postDetails.data.permalink,
        mediaUrl: postDetails.data.media_url,
        data: postDetails.data
      };
    } catch (error) {
      console.error('Instagram video publish error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
        message: 'Failed to publish video to Instagram'
      };
    }
  }

  /**
   * Publish a carousel to Instagram
   * @param {Object} content - The carousel content to publish
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing result
   */
  async publishCarousel(content, options = {}) {
    try {
      if (!content.fileUrls || !Array.isArray(content.fileUrls) || content.fileUrls.length < 2) {
        return {
          success: false,
          error: 'Invalid carousel content',
          message: 'Carousel requires at least 2 media items'
        };
      }
      
      // Step 1: Create media containers for each item
      const mediaIds = [];
      
      for (const fileUrl of content.fileUrls) {
        const isVideo = fileUrl.includes('.mp4') || fileUrl.includes('.mov');
        
        const response = await this.client.post(
          `/${this.instagramBusinessAccountId}/media`,
          null,
          {
            params: {
              media_type: isVideo ? 'VIDEO' : 'IMAGE',
              [isVideo ? 'video_url' : 'image_url']: fileUrl,
              is_carousel_item: true,
              access_token: this.accessToken
            }
          }
        );
        
        if (!response.data || !response.data.id) {
          throw new Error(`Failed to create media container for ${fileUrl}`);
        }
        
        // For videos, we need to wait for processing
        if (isVideo) {
          let status = 'IN_PROGRESS';
          let statusResponse;
          let attempts = 0;
          const maxAttempts = 30;
          
          while (status === 'IN_PROGRESS' && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            statusResponse = await this.client.get(
              `/${response.data.id}`,
              {
                params: {
                  fields: 'status_code',
                  access_token: this.accessToken
                }
              }
            );
            
            status = statusResponse.data.status_code;
            attempts++;
          }
          
          if (status !== 'FINISHED') {
            throw new Error(`Video processing failed with status: ${status}`);
          }
        }
        
        mediaIds.push(response.data.id);
      }
      
      // Step 2: Create the carousel container
      const carouselResponse = await this.client.post(
        `/${this.instagramBusinessAccountId}/media`,
        null,
        {
          params: {
            media_type: 'CAROUSEL',
            children: mediaIds.join(','),
            caption: content.caption || '',
            access_token: this.accessToken
          }
        }
      );
      
      if (!carouselResponse.data || !carouselResponse.data.id) {
        throw new Error('Failed to create carousel container');
      }
      
      // Step 3: Publish the carousel
      const publishResponse = await this.client.post(
        `/${this.instagramBusinessAccountId}/media_publish`,
        null,
        {
          params: {
            creation_id: carouselResponse.data.id,
            access_token: this.accessToken
          }
        }
      );
      
      if (!publishResponse.data || !publishResponse.data.id) {
        throw new Error('Failed to publish carousel');
      }
      
      // Step 4: Get the published post details
      const postDetails = await this.client.get(
        `/${publishResponse.data.id}`,
        {
          params: {
            fields: 'id,permalink,media_type,children{media_url,media_type},timestamp,caption',
            access_token: this.accessToken
          }
        }
      );
      
      return {
        success: true,
        message: 'Carousel published successfully to Instagram',
        postId: publishResponse.data.id,
        permalink: postDetails.data.permalink,
        itemCount: mediaIds.length,
        data: postDetails.data
      };
    } catch (error) {
      console.error('Instagram carousel publish error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
        message: 'Failed to publish carousel to Instagram'
      };
    }
  }

  /**
   * Delete a published post from Instagram
   * @param {string} postId - The ID of the post to delete
   * @param {Object} options - Deletion options
   * @returns {Promise<Object>} - Deletion result
   */
  async delete(postId, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!postId) {
        return {
          success: false,
          error: 'Missing post ID',
          message: 'Post ID is required for deletion'
        };
      }

      // Delete the post
      const response = await this.client.delete(
        `/${postId}`,
        {
          params: {
            access_token: this.accessToken
          }
        }
      );

      if (!response.data || !response.data.success) {
        throw new Error('Failed to delete post');
      }

      return {
        success: true,
        message: 'Post deleted successfully from Instagram',
        postId
      };
    } catch (error) {
      console.error('Instagram delete error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
        message: 'Failed to delete post from Instagram'
      };
    }
  }

  /**
   * Get a published post from Instagram
   * @param {string} postId - The ID of the post to retrieve
   * @param {Object} options - Retrieval options
   * @returns {Promise<Object>} - Retrieval result
   */
  async get(postId, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!postId) {
        return {
          success: false,
          error: 'Missing post ID',
          message: 'Post ID is required for retrieval'
        };
      }

      // Get the post
      const response = await this.client.get(
        `/${postId}`,
        {
          params: {
            fields: 'id,permalink,media_type,media_url,thumbnail_url,children{media_url,media_type},timestamp,caption,like_count,comments_count',
            access_token: this.accessToken
          }
        }
      );

      if (!response.data || !response.data.id) {
        throw new Error('Failed to retrieve post');
      }

      const post = response.data;

      return {
        success: true,
        message: 'Post retrieved successfully from Instagram',
        post: {
          id: post.id,
          permalink: post.permalink,
          mediaType: post.media_type,
          mediaUrl: post.media_url,
          thumbnailUrl: post.thumbnail_url,
          children: post.children?.data,
          timestamp: post.timestamp,
          caption: post.caption,
          likeCount: post.like_count,
          commentsCount: post.comments_count
        },
        data: post
      };
    } catch (error) {
      console.error('Instagram get error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
        message: 'Failed to retrieve post from Instagram'
      };
    }
  }

  /**
   * List published posts from Instagram
   * @param {Object} options - Listing options
   * @returns {Promise<Object>} - Listing result
   */
  async list(options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      const limit = options.limit || 25;

      // Get the posts
      const response = await this.client.get(
        `/${this.instagramBusinessAccountId}/media`,
        {
          params: {
            fields: 'id,permalink,media_type,media_url,thumbnail_url,children{media_url,media_type},timestamp,caption,like_count,comments_count',
            limit,
            after: options.after || '',
            access_token: this.accessToken
          }
        }
      );

      if (!response.data || !response.data.data) {
        throw new Error('Failed to list posts');
      }

      const posts = response.data.data.map(post => ({
        id: post.id,
        permalink: post.permalink,
        mediaType: post.media_type,
        mediaUrl: post.media_url,
        thumbnailUrl: post.thumbnail_url,
        children: post.children?.data,
        timestamp: post.timestamp,
        caption: post.caption,
        likeCount: post.like_count,
        commentsCount: post.comments_count
      }));

      return {
        success: true,
        message: 'Posts retrieved successfully from Instagram',
        posts,
        paging: response.data.paging
      };
    } catch (error) {
      console.error('Instagram list error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
        message: 'Failed to list posts from Instagram'
      };
    }
  }

  /**
   * Get analytics for a published post from Instagram
   * @param {string} postId - The ID of the post to get analytics for
   * @param {Object} options - Analytics options
   * @returns {Promise<Object>} - Analytics result
   */
  async getAnalytics(postId, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!postId) {
        return {
          success: false,
          error: 'Missing post ID',
          message: 'Post ID is required for analytics'
        };
      }

      // Get the post insights
      const response = await this.client.get(
        `/${postId}/insights`,
        {
          params: {
            metric: options.metrics || 'engagement,impressions,reach,saved',
            access_token: this.accessToken
          }
        }
      );

      if (!response.data || !response.data.data) {
        throw new Error('Failed to retrieve post insights');
      }

      // Format the insights data
      const insights = {};
      response.data.data.forEach(metric => {
        insights[metric.name] = metric.values[0].value;
      });

      return {
        success: true,
        message: 'Post analytics retrieved successfully from Instagram',
        analytics: insights,
        data: response.data
      };
    } catch (error) {
      console.error('Instagram analytics error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
        message: 'Failed to retrieve post analytics from Instagram'
      };
    }
  }
}

module.exports = InstagramService;
