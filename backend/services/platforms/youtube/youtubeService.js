const BasePlatformService = require('../basePlatformService');
const { google } = require('googleapis');
const fs = require('fs').promises;
const path = require('path');

/**
 * YouTube Platform Service for publishing content to YouTube
 */
class YouTubeService extends BasePlatformService {
  /**
   * Constructor for the YouTubeService
   * @param {Object} options - Configuration options for the service
   */
  constructor(options = {}) {
    super(options);
    this.platformName = 'youtube';
    this.supportedContentTypes = ['video/mp4', 'video/webm', 'video/quicktime'];
    this.supportedActions = ['publish', 'update', 'delete', 'get', 'list', 'analytics', 'schedule'];
    
    // Set default options
    this.options = {
      uploadChunkSize: 1024 * 1024 * 5, // 5MB chunks
      ...options
    };
    
    // Initialize YouTube API client
    this.youtube = null;
    this.oauth2Client = null;
  }

  /**
   * Initialize the YouTube service with credentials
   * @param {Object} credentials - The credentials for YouTube
   * @returns {Promise<Object>} - Initialization result
   */
  async initialize(credentials) {
    try {
      if (!this.validateCredentials(credentials)) {
        return {
          success: false,
          error: 'Invalid credentials',
          message: 'Please provide valid YouTube API credentials'
        };
      }
      
      this.credentials = credentials;
      
      // Create OAuth2 client
      this.oauth2Client = new google.auth.OAuth2(
        credentials.client_id,
        credentials.client_secret,
        credentials.redirect_uri
      );
      
      // Set credentials
      this.oauth2Client.setCredentials({
        access_token: credentials.access_token,
        refresh_token: credentials.refresh_token,
        expiry_date: credentials.expiry_date
      });
      
      // Initialize YouTube API client
      this.youtube = google.youtube({
        version: 'v3',
        auth: this.oauth2Client
      });
      
      // Test the connection
      const response = await this.youtube.channels.list({
        part: 'snippet',
        mine: true
      });
      
      if (response.data.items && response.data.items.length > 0) {
        this.channelInfo = response.data.items[0];
        this.isConfigured = true;
        
        return {
          success: true,
          message: 'YouTube service initialized successfully',
          channelInfo: {
            id: this.channelInfo.id,
            title: this.channelInfo.snippet.title,
            description: this.channelInfo.snippet.description,
            thumbnails: this.channelInfo.snippet.thumbnails
          }
        };
      } else {
        this.isConfigured = false;
        return {
          success: false,
          error: 'Channel not found',
          message: 'Could not find YouTube channel with the provided credentials'
        };
      }
    } catch (error) {
      this.isConfigured = false;
      console.error('YouTube initialization error:', error);
      
      return {
        success: false,
        error: error.message,
        message: 'Failed to initialize YouTube service'
      };
    }
  }

  /**
   * Validate the provided credentials
   * @param {Object} credentials - The credentials to validate
   * @returns {boolean} - Whether the credentials are valid
   */
  validateCredentials(credentials) {
    return (
      credentials &&
      credentials.client_id &&
      credentials.client_secret &&
      credentials.redirect_uri &&
      (credentials.access_token || credentials.refresh_token)
    );
  }

  /**
   * Publish a video to YouTube
   * @param {Object} content - The video content to publish
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing result
   */
  async publish(content, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!this.supportsContentType(content.type)) {
        return {
          success: false,
          error: 'Unsupported content type',
          message: `Content type '${content.type}' is not supported by YouTube`
        };
      }

      if (!content.filePath) {
        return {
          success: false,
          error: 'Missing file path',
          message: 'File path is required for video upload'
        };
      }

      // Check if file exists
      try {
        await fs.access(content.filePath);
      } catch (error) {
        return {
          success: false,
          error: 'File not found',
          message: `Video file not found at path: ${content.filePath}`
        };
      }

      // Prepare video metadata
      const videoMetadata = {
        snippet: {
          title: content.title || 'Untitled Video',
          description: content.description || '',
          tags: content.tags || [],
          categoryId: options.categoryId || '22', // 22 is "People & Blogs"
          defaultLanguage: options.language || 'en'
        },
        status: {
          privacyStatus: options.privacy || 'private', // 'private', 'public', or 'unlisted'
          publishAt: options.publishAt ? new Date(options.publishAt).toISOString() : undefined,
          selfDeclaredMadeForKids: options.madeForKids || false
        }
      };

      // Upload the video
      const fileSize = (await fs.stat(content.filePath)).size;
      const fileStream = fs.createReadStream(content.filePath);

      const res = await this.youtube.videos.insert({
        part: Object.keys(videoMetadata).join(','),
        requestBody: videoMetadata,
        media: {
          body: fileStream
        }
      }, {
        // Use chunked upload
        onUploadProgress: evt => {
          const progress = (evt.bytesRead / fileSize) * 100;
          console.log(`Upload progress: ${Math.round(progress)}%`);
        }
      });

      // Upload thumbnail if provided
      let thumbnailUrl = null;
      if (content.thumbnailPath) {
        try {
          const thumbnailRes = await this.youtube.thumbnails.set({
            videoId: res.data.id,
            media: {
              body: fs.createReadStream(content.thumbnailPath)
            }
          });
          thumbnailUrl = thumbnailRes.data.items[0].default.url;
        } catch (thumbnailError) {
          console.error('Thumbnail upload error:', thumbnailError);
          // Continue even if thumbnail upload fails
        }
      }

      return {
        success: true,
        message: 'Video uploaded successfully to YouTube',
        videoId: res.data.id,
        videoUrl: `https://www.youtube.com/watch?v=${res.data.id}`,
        thumbnailUrl,
        data: res.data
      };
    } catch (error) {
      console.error('YouTube publish error:', error);
      
      return {
        success: false,
        error: error.message,
        message: 'Failed to publish video to YouTube'
      };
    }
  }

  /**
   * Update a published video on YouTube
   * @param {string} videoId - The ID of the video to update
   * @param {Object} content - The updated video content
   * @param {Object} options - Update options
   * @returns {Promise<Object>} - Update result
   */
  async update(videoId, content, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!videoId) {
        return {
          success: false,
          error: 'Missing video ID',
          message: 'Video ID is required for update'
        };
      }

      // Prepare video metadata
      const videoMetadata = {
        id: videoId,
        snippet: {
          title: content.title,
          description: content.description,
          categoryId: options.categoryId || '22'
        },
        status: {}
      };

      // Add optional fields if provided
      if (content.tags) {
        videoMetadata.snippet.tags = content.tags;
      }

      if (options.privacy) {
        videoMetadata.status.privacyStatus = options.privacy;
      }

      if (options.madeForKids !== undefined) {
        videoMetadata.status.selfDeclaredMadeForKids = options.madeForKids;
      }

      // Update the video metadata
      const res = await this.youtube.videos.update({
        part: 'snippet,status',
        requestBody: videoMetadata
      });

      // Update thumbnail if provided
      let thumbnailUrl = null;
      if (content.thumbnailPath) {
        try {
          const thumbnailRes = await this.youtube.thumbnails.set({
            videoId: videoId,
            media: {
              body: fs.createReadStream(content.thumbnailPath)
            }
          });
          thumbnailUrl = thumbnailRes.data.items[0].default.url;
        } catch (thumbnailError) {
          console.error('Thumbnail update error:', thumbnailError);
          // Continue even if thumbnail update fails
        }
      }

      return {
        success: true,
        message: 'Video updated successfully on YouTube',
        videoId: res.data.id,
        videoUrl: `https://www.youtube.com/watch?v=${res.data.id}`,
        thumbnailUrl,
        data: res.data
      };
    } catch (error) {
      console.error('YouTube update error:', error);
      
      return {
        success: false,
        error: error.message,
        message: 'Failed to update video on YouTube'
      };
    }
  }

  /**
   * Delete a published video from YouTube
   * @param {string} videoId - The ID of the video to delete
   * @param {Object} options - Deletion options
   * @returns {Promise<Object>} - Deletion result
   */
  async delete(videoId, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!videoId) {
        return {
          success: false,
          error: 'Missing video ID',
          message: 'Video ID is required for deletion'
        };
      }

      // Delete the video
      await this.youtube.videos.delete({
        id: videoId
      });

      return {
        success: true,
        message: 'Video deleted successfully from YouTube',
        videoId
      };
    } catch (error) {
      console.error('YouTube delete error:', error);
      
      return {
        success: false,
        error: error.message,
        message: 'Failed to delete video from YouTube'
      };
    }
  }

  /**
   * Get a published video from YouTube
   * @param {string} videoId - The ID of the video to retrieve
   * @param {Object} options - Retrieval options
   * @returns {Promise<Object>} - Retrieval result
   */
  async get(videoId, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!videoId) {
        return {
          success: false,
          error: 'Missing video ID',
          message: 'Video ID is required for retrieval'
        };
      }

      // Get the video
      const res = await this.youtube.videos.list({
        part: 'snippet,contentDetails,statistics,status',
        id: videoId
      });

      if (!res.data.items || res.data.items.length === 0) {
        return {
          success: false,
          error: 'Video not found',
          message: `No video found with ID: ${videoId}`
        };
      }

      const video = res.data.items[0];

      return {
        success: true,
        message: 'Video retrieved successfully from YouTube',
        video: {
          id: video.id,
          title: video.snippet.title,
          description: video.snippet.description,
          thumbnails: video.snippet.thumbnails,
          publishedAt: video.snippet.publishedAt,
          tags: video.snippet.tags || [],
          categoryId: video.snippet.categoryId,
          duration: video.contentDetails.duration,
          viewCount: video.statistics.viewCount,
          likeCount: video.statistics.likeCount,
          commentCount: video.statistics.commentCount,
          privacyStatus: video.status.privacyStatus,
          embeddable: video.status.embeddable,
          madeForKids: video.status.madeForKids
        },
        data: video
      };
    } catch (error) {
      console.error('YouTube get error:', error);
      
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve video from YouTube'
      };
    }
  }

  /**
   * List published videos from YouTube
   * @param {Object} options - Listing options
   * @returns {Promise<Object>} - Listing result
   */
  async list(options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      const params = {
        part: 'snippet,contentDetails,statistics,status',
        maxResults: options.maxResults || 50,
        mine: true
      };

      // Add optional parameters
      if (options.pageToken) {
        params.pageToken = options.pageToken;
      }

      if (options.order) {
        params.order = options.order; // date, rating, relevance, title, videoCount, viewCount
      }

      // Get the videos
      const res = await this.youtube.videos.list(params);

      const videos = res.data.items.map(video => ({
        id: video.id,
        title: video.snippet.title,
        description: video.snippet.description,
        thumbnails: video.snippet.thumbnails,
        publishedAt: video.snippet.publishedAt,
        tags: video.snippet.tags || [],
        categoryId: video.snippet.categoryId,
        duration: video.contentDetails.duration,
        viewCount: video.statistics.viewCount,
        likeCount: video.statistics.likeCount,
        commentCount: video.statistics.commentCount,
        privacyStatus: video.status.privacyStatus,
        embeddable: video.status.embeddable,
        madeForKids: video.status.madeForKids
      }));

      return {
        success: true,
        message: 'Videos retrieved successfully from YouTube',
        videos,
        pageInfo: {
          totalResults: res.data.pageInfo.totalResults,
          resultsPerPage: res.data.pageInfo.resultsPerPage,
          nextPageToken: res.data.nextPageToken,
          prevPageToken: res.data.prevPageToken
        }
      };
    } catch (error) {
      console.error('YouTube list error:', error);
      
      return {
        success: false,
        error: error.message,
        message: 'Failed to list videos from YouTube'
      };
    }
  }

  /**
   * Get analytics for a published video from YouTube
   * @param {string} videoId - The ID of the video to get analytics for
   * @param {Object} options - Analytics options
   * @returns {Promise<Object>} - Analytics result
   */
  async getAnalytics(videoId, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!videoId) {
        return {
          success: false,
          error: 'Missing video ID',
          message: 'Video ID is required for analytics'
        };
      }

      // Initialize YouTube Analytics API
      const youtubeAnalytics = google.youtubeAnalytics({
        version: 'v2',
        auth: this.oauth2Client
      });

      // Set default date range if not provided
      const endDate = options.endDate || new Date().toISOString().split('T')[0];
      const startDate = options.startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      // Get video analytics
      const res = await youtubeAnalytics.reports.query({
        ids: 'channel==MINE',
        startDate,
        endDate,
        metrics: options.metrics || 'views,likes,dislikes,comments,shares,estimatedMinutesWatched,averageViewDuration',
        dimensions: options.dimensions || 'day',
        filters: `video==${videoId}`
      });

      return {
        success: true,
        message: 'Video analytics retrieved successfully from YouTube',
        analytics: {
          columnHeaders: res.data.columnHeaders,
          rows: res.data.rows,
          startDate,
          endDate
        },
        data: res.data
      };
    } catch (error) {
      console.error('YouTube analytics error:', error);
      
      return {
        success: false,
        error: error.message,
        message: 'Failed to retrieve video analytics from YouTube'
      };
    }
  }

  /**
   * Schedule a video for publishing on YouTube
   * @param {Object} content - The video content to schedule
   * @param {Date} publishDate - The date to publish the video
   * @param {Object} options - Scheduling options
   * @returns {Promise<Object>} - Scheduling result
   */
  async schedule(content, publishDate, options = {}) {
    try {
      if (!this.isConfigured) {
        return {
          success: false,
          error: 'Service not configured',
          message: 'Please initialize the service with valid credentials'
        };
      }

      if (!this.supportsContentType(content.type)) {
        return {
          success: false,
          error: 'Unsupported content type',
          message: `Content type '${content.type}' is not supported by YouTube`
        };
      }

      if (!content.filePath) {
        return {
          success: false,
          error: 'Missing file path',
          message: 'File path is required for video upload'
        };
      }

      if (!publishDate || !(publishDate instanceof Date)) {
        return {
          success: false,
          error: 'Invalid publish date',
          message: 'A valid publish date is required for scheduling'
        };
      }

      // Ensure publish date is in the future
      const now = new Date();
      if (publishDate <= now) {
        return {
          success: false,
          error: 'Invalid publish date',
          message: 'Publish date must be in the future'
        };
      }

      // Prepare video metadata with scheduled publish time
      const videoMetadata = {
        snippet: {
          title: content.title || 'Untitled Video',
          description: content.description || '',
          tags: content.tags || [],
          categoryId: options.categoryId || '22', // 22 is "People & Blogs"
          defaultLanguage: options.language || 'en'
        },
        status: {
          privacyStatus: 'private', // Initially private
          publishAt: publishDate.toISOString(),
          selfDeclaredMadeForKids: options.madeForKids || false
        }
      };

      // Upload the video
      const fileSize = (await fs.stat(content.filePath)).size;
      const fileStream = fs.createReadStream(content.filePath);

      const res = await this.youtube.videos.insert({
        part: Object.keys(videoMetadata).join(','),
        requestBody: videoMetadata,
        media: {
          body: fileStream
        }
      }, {
        // Use chunked upload
        onUploadProgress: evt => {
          const progress = (evt.bytesRead / fileSize) * 100;
          console.log(`Upload progress: ${Math.round(progress)}%`);
        }
      });

      // Upload thumbnail if provided
      let thumbnailUrl = null;
      if (content.thumbnailPath) {
        try {
          const thumbnailRes = await this.youtube.thumbnails.set({
            videoId: res.data.id,
            media: {
              body: fs.createReadStream(content.thumbnailPath)
            }
          });
          thumbnailUrl = thumbnailRes.data.items[0].default.url;
        } catch (thumbnailError) {
          console.error('Thumbnail upload error:', thumbnailError);
          // Continue even if thumbnail upload fails
        }
      }

      return {
        success: true,
        message: 'Video scheduled successfully on YouTube',
        videoId: res.data.id,
        videoUrl: `https://www.youtube.com/watch?v=${res.data.id}`,
        thumbnailUrl,
        scheduledPublishTime: publishDate.toISOString(),
        data: res.data
      };
    } catch (error) {
      console.error('YouTube schedule error:', error);
      
      return {
        success: false,
        error: error.message,
        message: 'Failed to schedule video on YouTube'
      };
    }
  }
}

module.exports = YouTubeService;
