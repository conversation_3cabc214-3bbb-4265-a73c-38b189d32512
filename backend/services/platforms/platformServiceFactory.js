/**
 * Factory for creating appropriate platform services
 */
class PlatformServiceFactory {
  constructor() {
    this.services = {};
  }

  /**
   * Register a platform service class with the factory
   * @param {string} platform - The platform name
   * @param {Class} ServiceClass - The service class to register
   */
  registerService(platform, ServiceClass) {
    this.services[platform] = ServiceClass;
  }

  /**
   * Get a platform service instance
   * @param {string} platform - The platform name
   * @param {Object} options - Configuration options for the service
   * @returns {Object|null} - An instance of the appropriate service or null if not found
   */
  getService(platform, options = {}) {
    const ServiceClass = this.services[platform];
    
    if (!ServiceClass) {
      return null;
    }
    
    return new ServiceClass(options);
  }

  /**
   * Get all registered platform services
   * @param {Object} options - Configuration options for the services
   * @returns {Object} - Object with platform names as keys and service instances as values
   */
  getAllServices(options = {}) {
    const services = {};
    
    for (const [platform, ServiceClass] of Object.entries(this.services)) {
      services[platform] = new ServiceClass(options);
    }
    
    return services;
  }

  /**
   * Get information about all registered platform services
   * @returns {Array} - Array of platform service information
   */
  getServicesInfo() {
    const info = [];
    
    for (const [platform, ServiceClass] of Object.entries(this.services)) {
      const service = new ServiceClass();
      info.push(service.getInfo());
    }
    
    return info;
  }
}

// Create and export a singleton instance
const factory = new PlatformServiceFactory();
module.exports = factory;
