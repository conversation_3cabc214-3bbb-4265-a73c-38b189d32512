const supabase = require('../config/supabase');
const platformServiceFactory = require('./platforms/platformServiceFactory');
const YouTubeService = require('./platforms/youtube/youtubeService');
const InstagramService = require('./platforms/instagram/instagramService');
const LinkedInService = require('./platforms/linkedin/linkedinService');

// Register platform services with the factory
platformServiceFactory.registerService('youtube', YouTubeService);
platformServiceFactory.registerService('instagram', InstagramService);
platformServiceFactory.registerService('linkedin', LinkedInService);

/**
 * Platform Manager Service for unified publishing across platforms
 */
class PlatformManager {
  constructor() {
    this.services = {};
    this.initialized = false;
  }

  /**
   * Initialize platform services for a user
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - Initialization result
   */
  async initializeForUser(userId) {
    try {
      // Get user's OAuth tokens
      const { data: tokens, error } = await supabase
        .from('oauth_tokens')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) {
        throw new Error(`Failed to fetch OAuth tokens: ${error.message}`);
      }

      this.services = {};

      // Initialize services for each connected platform
      for (const token of tokens) {
        const service = platformServiceFactory.getService(token.platform);
        
        if (service) {
          // Check if token is expired and refresh if needed
          if (token.expires_at && new Date(token.expires_at) <= new Date()) {
            await this.refreshToken(userId, token.platform);
            // Refetch the updated token
            const { data: updatedToken } = await supabase
              .from('oauth_tokens')
              .select('*')
              .eq('user_id', userId)
              .eq('platform', token.platform)
              .eq('is_active', true)
              .single();
            
            if (updatedToken) {
              token.access_token = updatedToken.access_token;
              token.expires_at = updatedToken.expires_at;
            }
          }

          // Initialize the service with credentials
          const credentials = this.formatCredentialsForPlatform(token);
          const initResult = await service.initialize(credentials);
          
          if (initResult.success) {
            this.services[token.platform] = service;
          } else {
            console.error(`Failed to initialize ${token.platform} service:`, initResult.error);
          }
        }
      }

      this.initialized = true;
      return {
        success: true,
        initializedPlatforms: Object.keys(this.services),
        message: `Initialized ${Object.keys(this.services).length} platform services`
      };
    } catch (error) {
      console.error('Platform manager initialization error:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to initialize platform manager'
      };
    }
  }

  /**
   * Publish content to multiple platforms
   * @param {string} userId - The user ID
   * @param {Object} content - The content to publish
   * @param {Array} platforms - Array of platform names
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Publishing results
   */
  async publishToMultiplePlatforms(userId, content, platforms, options = {}) {
    try {
      if (!this.initialized) {
        await this.initializeForUser(userId);
      }

      const results = {};
      const errors = {};

      // Publish to each platform
      for (const platform of platforms) {
        try {
          const service = this.services[platform];
          
          if (!service) {
            errors[platform] = {
              success: false,
              error: 'Service not available',
              message: `${platform} service is not initialized or connected`
            };
            continue;
          }

          // Format content for the specific platform
          const formattedContent = await this.formatContentForPlatform(content, platform);
          
          // Publish to the platform
          const result = await service.publish(formattedContent, options[platform] || {});
          
          if (result.success) {
            // Store publication record
            await this.storePublicationRecord(userId, content.id, platform, result);
            results[platform] = result;
          } else {
            errors[platform] = result;
          }
        } catch (error) {
          console.error(`Error publishing to ${platform}:`, error);
          errors[platform] = {
            success: false,
            error: error.message,
            message: `Failed to publish to ${platform}`
          };
        }
      }

      return {
        success: Object.keys(results).length > 0,
        results,
        errors,
        published_platforms: Object.keys(results),
        failed_platforms: Object.keys(errors)
      };
    } catch (error) {
      console.error('Multi-platform publishing error:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to publish to multiple platforms'
      };
    }
  }

  /**
   * Schedule content for publishing
   * @param {string} userId - The user ID
   * @param {Object} content - The content to schedule
   * @param {Array} platforms - Array of platform names
   * @param {Date} scheduledTime - When to publish
   * @param {Object} options - Publishing options
   * @returns {Promise<Object>} - Scheduling result
   */
  async schedulePublication(userId, content, platforms, scheduledTime, options = {}) {
    try {
      const scheduledPublications = [];

      for (const platform of platforms) {
        const publicationRecord = {
          user_id: userId,
          content_id: content.id,
          platform: platform,
          scheduled_time: scheduledTime,
          status: 'scheduled'
        };

        const { data: scheduled, error } = await supabase
          .from('scheduled_publications')
          .insert(publicationRecord)
          .select()
          .single();

        if (error) {
          console.error(`Error scheduling publication for ${platform}:`, error);
        } else {
          scheduledPublications.push(scheduled);
        }
      }

      return {
        success: scheduledPublications.length > 0,
        scheduled_publications: scheduledPublications,
        message: `Scheduled ${scheduledPublications.length} publications`
      };
    } catch (error) {
      console.error('Schedule publication error:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to schedule publication'
      };
    }
  }

  /**
   * Get analytics for published content across platforms
   * @param {string} userId - The user ID
   * @param {string} contentId - The content ID
   * @returns {Promise<Object>} - Analytics data
   */
  async getContentAnalytics(userId, contentId) {
    try {
      if (!this.initialized) {
        await this.initializeForUser(userId);
      }

      // Get published content records
      const { data: publications, error } = await supabase
        .from('published_content')
        .select('*')
        .eq('user_id', userId)
        .eq('content_id', contentId);

      if (error) {
        throw new Error(`Failed to fetch publications: ${error.message}`);
      }

      const analytics = {};

      for (const publication of publications) {
        try {
          const service = this.services[publication.platform];
          
          if (service && service.supportsAction('analytics')) {
            const result = await service.getAnalytics(publication.platform_post_id);
            
            if (result.success) {
              analytics[publication.platform] = result.analytics;
            }
          }
        } catch (error) {
          console.error(`Error fetching analytics for ${publication.platform}:`, error);
        }
      }

      return {
        success: true,
        analytics,
        platforms: Object.keys(analytics)
      };
    } catch (error) {
      console.error('Get content analytics error:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to fetch content analytics'
      };
    }
  }

  /**
   * Get available platforms for a user
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - Available platforms
   */
  async getAvailablePlatforms(userId) {
    try {
      const { data: tokens, error } = await supabase
        .from('oauth_tokens')
        .select('platform, platform_username, is_active, expires_at')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) {
        throw new Error(`Failed to fetch platforms: ${error.message}`);
      }

      const platforms = tokens.map(token => ({
        platform: token.platform,
        username: token.platform_username,
        is_expired: token.expires_at ? new Date(token.expires_at) <= new Date() : false,
        capabilities: this.getPlatformCapabilities(token.platform)
      }));

      return {
        success: true,
        platforms,
        count: platforms.length
      };
    } catch (error) {
      console.error('Get available platforms error:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to fetch available platforms'
      };
    }
  }

  /**
   * Format credentials for a specific platform
   * @param {Object} token - OAuth token record
   * @returns {Object} - Formatted credentials
   */
  formatCredentialsForPlatform(token) {
    switch (token.platform) {
      case 'youtube':
        return {
          client_id: process.env.YOUTUBE_CLIENT_ID,
          client_secret: process.env.YOUTUBE_CLIENT_SECRET,
          redirect_uri: process.env.YOUTUBE_REDIRECT_URI,
          access_token: token.access_token,
          refresh_token: token.refresh_token,
          expiry_date: token.expires_at ? new Date(token.expires_at).getTime() : null
        };
      case 'instagram':
        return {
          access_token: token.access_token,
          instagram_business_account_id: token.platform_user_id
        };
      case 'linkedin':
        return {
          access_token: token.access_token,
          organization_id: token.platform_data?.organization_id
        };
      default:
        return {
          access_token: token.access_token,
          refresh_token: token.refresh_token
        };
    }
  }

  /**
   * Format content for a specific platform
   * @param {Object} content - The original content
   * @param {string} platform - The target platform
   * @returns {Promise<Object>} - Formatted content
   */
  async formatContentForPlatform(content, platform) {
    const baseContent = {
      title: content.title,
      description: content.description,
      type: content.content_type,
      filePath: content.file_path,
      fileUrl: content.file_url
    };

    switch (platform) {
      case 'youtube':
        return {
          ...baseContent,
          tags: content.metadata?.tags || [],
          thumbnailPath: content.metadata?.thumbnail_path,
          privacy: content.metadata?.privacy || 'private'
        };
      case 'instagram':
        return {
          ...baseContent,
          caption: content.description || content.title,
          fileUrls: content.metadata?.file_urls || [content.file_url]
        };
      case 'linkedin':
        return {
          ...baseContent,
          text: content.description || content.title,
          visibility: content.metadata?.visibility || 'PUBLIC'
        };
      default:
        return baseContent;
    }
  }

  /**
   * Store publication record in database
   * @param {string} userId - The user ID
   * @param {string} contentId - The content ID
   * @param {string} platform - The platform name
   * @param {Object} result - The publishing result
   * @returns {Promise<void>}
   */
  async storePublicationRecord(userId, contentId, platform, result) {
    try {
      const publicationRecord = {
        user_id: userId,
        content_id: contentId,
        platform: platform,
        platform_post_id: result.videoId || result.postId || result.articleId,
        published_url: result.videoUrl || result.permalink || result.url,
        status: 'published',
        platform_data: result.data || {}
      };

      const { error } = await supabase
        .from('published_content')
        .insert(publicationRecord);

      if (error) {
        console.error('Error storing publication record:', error);
      }
    } catch (error) {
      console.error('Store publication record error:', error);
    }
  }

  /**
   * Refresh OAuth token for a platform
   * @param {string} userId - The user ID
   * @param {string} platform - The platform name
   * @returns {Promise<void>}
   */
  async refreshToken(userId, platform) {
    try {
      // This would typically call the OAuth refresh endpoint
      // For now, we'll just log that a refresh is needed
      console.log(`Token refresh needed for ${platform} for user ${userId}`);
      
      // In a real implementation, you would:
      // 1. Get the refresh token from the database
      // 2. Call the platform's token refresh endpoint
      // 3. Update the database with the new tokens
    } catch (error) {
      console.error('Token refresh error:', error);
    }
  }

  /**
   * Get platform capabilities
   * @param {string} platform - The platform name
   * @returns {Object} - Platform capabilities
   */
  getPlatformCapabilities(platform) {
    const service = platformServiceFactory.getService(platform);
    
    if (service) {
      return {
        supportedContentTypes: service.prototype.supportedContentTypes,
        supportedActions: service.prototype.supportedActions
      };
    }

    return {
      supportedContentTypes: [],
      supportedActions: []
    };
  }
}

module.exports = new PlatformManager();
