const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const supabase = require('../config/supabase');

class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // Map of userId -> Set of WebSocket connections
    this.isRunning = false;
  }

  /**
   * Initialize WebSocket server
   * @param {Object} server - HTTP server instance
   */
  initialize(server) {
    if (this.isRunning) {
      console.log('WebSocket service is already running');
      return;
    }

    this.wss = new WebSocket.Server({
      server,
      path: '/ws/analytics',
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.isRunning = true;

    console.log('WebSocket service initialized for analytics updates');
  }

  /**
   * Verify client authentication
   * @param {Object} info - Connection info
   * @returns {boolean} Whether client is authenticated
   */
  async verifyClient(info) {
    try {
      const url = new URL(info.req.url, `http://${info.req.headers.host}`);
      const token = url.searchParams.get('token');

      if (!token) {
        console.log('WebSocket connection rejected: No token provided');
        return false;
      }

      // Verify JWT token (you might need to adjust this based on your auth setup)
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
      
      // Store user info for later use
      info.req.userId = decoded.sub || decoded.id;
      
      return true;
    } catch (error) {
      console.log('WebSocket connection rejected: Invalid token', error.message);
      return false;
    }
  }

  /**
   * Handle new WebSocket connection
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} req - Request object
   */
  handleConnection(ws, req) {
    const userId = req.userId;
    
    if (!userId) {
      ws.close(1008, 'Authentication failed');
      return;
    }

    console.log(`Analytics WebSocket connected for user ${userId}`);

    // Add client to tracking
    if (!this.clients.has(userId)) {
      this.clients.set(userId, new Set());
    }
    this.clients.get(userId).add(ws);

    // Send welcome message
    this.sendToClient(ws, {
      type: 'connection_established',
      message: 'Real-time analytics updates enabled',
      timestamp: new Date().toISOString()
    });

    // Handle client messages
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        this.handleClientMessage(ws, userId, data);
      } catch (error) {
        console.error('Invalid WebSocket message:', error);
        this.sendToClient(ws, {
          type: 'error',
          message: 'Invalid message format'
        });
      }
    });

    // Handle client disconnect
    ws.on('close', () => {
      console.log(`Analytics WebSocket disconnected for user ${userId}`);
      this.removeClient(userId, ws);
    });

    // Handle errors
    ws.on('error', (error) => {
      console.error(`WebSocket error for user ${userId}:`, error);
      this.removeClient(userId, ws);
    });

    // Send initial analytics summary
    this.sendInitialData(ws, userId);
  }

  /**
   * Handle messages from clients
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   * @param {Object} data - Message data
   */
  handleClientMessage(ws, userId, data) {
    switch (data.type) {
      case 'subscribe_platform':
        // Subscribe to specific platform updates
        this.subscribeToPlatform(ws, userId, data.platform);
        break;
      
      case 'unsubscribe_platform':
        // Unsubscribe from platform updates
        this.unsubscribeFromPlatform(ws, userId, data.platform);
        break;
      
      case 'request_update':
        // Request immediate analytics update
        this.sendAnalyticsUpdate(ws, userId);
        break;
      
      case 'ping':
        // Respond to ping with pong
        this.sendToClient(ws, { type: 'pong', timestamp: new Date().toISOString() });
        break;
      
      default:
        this.sendToClient(ws, {
          type: 'error',
          message: `Unknown message type: ${data.type}`
        });
    }
  }

  /**
   * Send initial analytics data to newly connected client
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   */
  async sendInitialData(ws, userId) {
    try {
      // Get recent analytics summary
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      const { data: recentAnalytics, error } = await supabase
        .from('analytics')
        .select(`
          metrics,
          platform,
          date,
          published_content!inner(
            user_id
          )
        `)
        .eq('published_content.user_id', userId)
        .gte('date', yesterday.toISOString().split('T')[0])
        .order('date', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Failed to fetch initial analytics data:', error);
        return;
      }

      // Aggregate metrics
      const summary = this.aggregateMetrics(recentAnalytics || []);

      this.sendToClient(ws, {
        type: 'initial_data',
        data: {
          summary,
          recentUpdates: recentAnalytics?.slice(0, 5) || [],
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Error sending initial data:', error);
    }
  }

  /**
   * Send analytics update to specific user
   * @param {string} userId - User ID
   * @param {Object} analyticsData - Analytics data
   */
  broadcastToUser(userId, analyticsData) {
    const userClients = this.clients.get(userId);
    
    if (!userClients || userClients.size === 0) {
      return;
    }

    const message = {
      type: 'analytics_update',
      data: analyticsData,
      timestamp: new Date().toISOString()
    };

    userClients.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        this.sendToClient(ws, message);
      }
    });
  }

  /**
   * Send analytics update to all connected clients
   * @param {Object} analyticsData - Analytics data
   */
  broadcastToAll(analyticsData) {
    const message = {
      type: 'global_analytics_update',
      data: analyticsData,
      timestamp: new Date().toISOString()
    };

    this.clients.forEach((userClients, userId) => {
      userClients.forEach(ws => {
        if (ws.readyState === WebSocket.OPEN) {
          this.sendToClient(ws, message);
        }
      });
    });
  }

  /**
   * Send message to specific client
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - Message to send
   */
  sendToClient(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
      }
    }
  }

  /**
   * Remove client from tracking
   * @param {string} userId - User ID
   * @param {WebSocket} ws - WebSocket connection
   */
  removeClient(userId, ws) {
    const userClients = this.clients.get(userId);
    
    if (userClients) {
      userClients.delete(ws);
      
      if (userClients.size === 0) {
        this.clients.delete(userId);
      }
    }
  }

  /**
   * Subscribe client to platform-specific updates
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   * @param {string} platform - Platform name
   */
  subscribeToPlatform(ws, userId, platform) {
    // Store subscription info (you might want to persist this)
    ws.subscribedPlatforms = ws.subscribedPlatforms || new Set();
    ws.subscribedPlatforms.add(platform);

    this.sendToClient(ws, {
      type: 'subscription_confirmed',
      platform,
      message: `Subscribed to ${platform} updates`
    });
  }

  /**
   * Unsubscribe client from platform-specific updates
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   * @param {string} platform - Platform name
   */
  unsubscribeFromPlatform(ws, userId, platform) {
    if (ws.subscribedPlatforms) {
      ws.subscribedPlatforms.delete(platform);
    }

    this.sendToClient(ws, {
      type: 'unsubscription_confirmed',
      platform,
      message: `Unsubscribed from ${platform} updates`
    });
  }

  /**
   * Send current analytics update to specific client
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} userId - User ID
   */
  async sendAnalyticsUpdate(ws, userId) {
    try {
      // Get latest analytics for user
      const { data: latestAnalytics, error } = await supabase
        .from('analytics')
        .select(`
          metrics,
          platform,
          date,
          published_content!inner(
            user_id,
            content_id,
            url
          )
        `)
        .eq('published_content.user_id', userId)
        .order('date', { ascending: false })
        .limit(5);

      if (error) {
        console.error('Failed to fetch analytics update:', error);
        return;
      }

      const summary = this.aggregateMetrics(latestAnalytics || []);

      this.sendToClient(ws, {
        type: 'analytics_update',
        data: {
          summary,
          recent: latestAnalytics || [],
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Error sending analytics update:', error);
    }
  }

  /**
   * Aggregate analytics metrics
   * @param {Array} analyticsData - Array of analytics records
   * @returns {Object} Aggregated metrics
   */
  aggregateMetrics(analyticsData) {
    const summary = {
      totalViews: 0,
      totalLikes: 0,
      totalComments: 0,
      totalShares: 0,
      totalImpressions: 0,
      platforms: {}
    };

    analyticsData.forEach(item => {
      const metrics = item.metrics || {};
      const platform = item.platform;

      // Initialize platform if not exists
      if (!summary.platforms[platform]) {
        summary.platforms[platform] = {
          views: 0,
          likes: 0,
          comments: 0,
          shares: 0,
          impressions: 0
        };
      }

      // Add to totals
      summary.totalViews += metrics.views || metrics.impressions || 0;
      summary.totalLikes += metrics.likes || 0;
      summary.totalComments += metrics.comments || 0;
      summary.totalShares += metrics.shares || 0;
      summary.totalImpressions += metrics.impressions || metrics.views || 0;

      // Add to platform totals
      summary.platforms[platform].views += metrics.views || metrics.impressions || 0;
      summary.platforms[platform].likes += metrics.likes || 0;
      summary.platforms[platform].comments += metrics.comments || 0;
      summary.platforms[platform].shares += metrics.shares || 0;
      summary.platforms[platform].impressions += metrics.impressions || metrics.views || 0;
    });

    return summary;
  }

  /**
   * Get service status
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      connectedUsers: this.clients.size,
      totalConnections: Array.from(this.clients.values()).reduce((sum, userClients) => sum + userClients.size, 0),
      uptime: process.uptime()
    };
  }

  /**
   * Shutdown WebSocket service
   */
  shutdown() {
    if (!this.isRunning) {
      return;
    }

    console.log('Shutting down WebSocket service...');

    // Close all connections
    this.clients.forEach((userClients, userId) => {
      userClients.forEach(ws => {
        ws.close(1001, 'Server shutting down');
      });
    });

    // Close server
    if (this.wss) {
      this.wss.close();
    }

    this.clients.clear();
    this.isRunning = false;

    console.log('WebSocket service shut down');
  }
}

module.exports = new WebSocketService();
