const FileProcessingQueue = require('./fileProcessingQueue');

/**
 * Queue Manager for handling multiple processing queues
 */
class QueueManager {
  constructor() {
    this.queues = new Map();
    this.isRunning = false;
  }

  /**
   * Initialize and start all queues
   */
  async initialize() {
    try {
      // Initialize file processing queue
      const fileQueue = new FileProcessingQueue({
        maxRetries: 3,
        retryDelay: 5000,
        batchSize: 5,
        processingTimeout: 300000
      });

      this.queues.set('file-processing', fileQueue);

      console.log('Queue Manager initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize Queue Manager:', error);
      return false;
    }
  }

  /**
   * Start all queues
   */
  startAll() {
    if (this.isRunning) {
      console.log('Queue Manager is already running');
      return;
    }

    this.isRunning = true;
    console.log('Starting Queue Manager...');

    // Start file processing queue
    const fileQueue = this.queues.get('file-processing');
    if (fileQueue) {
      fileQueue.startProcessing(10000); // Process every 10 seconds
      console.log('File processing queue started');
    }

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('Received SIGINT, shutting down queues gracefully...');
      this.stopAll();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('Received SIGTERM, shutting down queues gracefully...');
      this.stopAll();
      process.exit(0);
    });
  }

  /**
   * Stop all queues
   */
  stopAll() {
    if (!this.isRunning) {
      console.log('Queue Manager is not running');
      return;
    }

    console.log('Stopping Queue Manager...');

    // Stop all queues
    for (const [name, queue] of this.queues) {
      if (queue && typeof queue.stopProcessing === 'function') {
        queue.stopProcessing();
        console.log(`${name} queue stopped`);
      }
    }

    this.isRunning = false;
    console.log('Queue Manager stopped');
  }

  /**
   * Get queue by name
   * @param {string} name - Queue name
   * @returns {Object|null} - Queue instance or null
   */
  getQueue(name) {
    return this.queues.get(name) || null;
  }

  /**
   * Get status of all queues
   * @returns {Promise<Object>} - Status of all queues
   */
  async getStatus() {
    const status = {
      isRunning: this.isRunning,
      queues: {}
    };

    for (const [name, queue] of this.queues) {
      if (queue && typeof queue.getQueueStats === 'function') {
        try {
          const stats = await queue.getQueueStats();
          status.queues[name] = {
            isProcessing: queue.isProcessing || false,
            stats: stats.success ? stats.stats : null,
            error: stats.success ? null : stats.error
          };
        } catch (error) {
          status.queues[name] = {
            isProcessing: false,
            stats: null,
            error: error.message
          };
        }
      } else {
        status.queues[name] = {
          isProcessing: false,
          stats: null,
          error: 'Queue not available'
        };
      }
    }

    return status;
  }
}

// Create singleton instance
const queueManager = new QueueManager();

module.exports = queueManager;
