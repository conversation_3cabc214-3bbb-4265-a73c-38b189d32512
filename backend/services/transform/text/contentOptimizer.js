/**
 * Content Optimizer for platform-specific text optimization
 */
class ContentOptimizer {
  constructor() {
    this.platformLimits = {
      twitter: {
        maxLength: 280,
        hashtagLimit: 2,
        mentionLimit: 10
      },
      instagram: {
        maxLength: 2200,
        hashtagLimit: 30,
        mentionLimit: 20,
        captionOptimalLength: 125
      },
      linkedin: {
        maxLength: 3000,
        hashtagLimit: 5,
        mentionLimit: 50,
        articleMaxLength: 125000
      },
      youtube: {
        titleMaxLength: 100,
        descriptionMaxLength: 5000,
        hashtagLimit: 15
      },
      facebook: {
        maxLength: 63206,
        hashtagLimit: 10,
        mentionLimit: 50,
        optimalLength: 40
      },
      tiktok: {
        maxLength: 2200,
        hashtagLimit: 20,
        mentionLimit: 20
      }
    };
  }

  /**
   * Optimize content for a specific platform
   * @param {string} content - Original content
   * @param {string} platform - Target platform
   * @param {Object} options - Optimization options
   * @returns {Object} - Optimized content result
   */
  optimizeForPlatform(content, platform, options = {}) {
    const platformConfig = this.platformLimits[platform.toLowerCase()];
    if (!platformConfig) {
      return {
        success: false,
        error: `Unsupported platform: ${platform}`
      };
    }

    try {
      let optimizedContent = content;
      const analysis = this.analyzeContent(content);
      const suggestions = [];

      // Platform-specific optimizations
      switch (platform.toLowerCase()) {
        case 'twitter':
          optimizedContent = this.optimizeForTwitter(content, platformConfig, options);
          break;
        case 'instagram':
          optimizedContent = this.optimizeForInstagram(content, platformConfig, options);
          break;
        case 'linkedin':
          optimizedContent = this.optimizeForLinkedIn(content, platformConfig, options);
          break;
        case 'youtube':
          optimizedContent = this.optimizeForYouTube(content, platformConfig, options);
          break;
        case 'facebook':
          optimizedContent = this.optimizeForFacebook(content, platformConfig, options);
          break;
        case 'tiktok':
          optimizedContent = this.optimizeForTikTok(content, platformConfig, options);
          break;
      }

      // Generate suggestions
      if (analysis.length > platformConfig.maxLength) {
        suggestions.push(`Content is ${analysis.length - platformConfig.maxLength} characters over the limit`);
      }

      if (analysis.hashtags.length > platformConfig.hashtagLimit) {
        suggestions.push(`Consider reducing hashtags from ${analysis.hashtags.length} to ${platformConfig.hashtagLimit}`);
      }

      return {
        success: true,
        originalContent: content,
        optimizedContent,
        analysis,
        suggestions,
        platform,
        characterCount: optimizedContent.length,
        characterLimit: platformConfig.maxLength,
        withinLimit: optimizedContent.length <= platformConfig.maxLength
      };

    } catch (error) {
      return {
        success: false,
        error: `Optimization failed: ${error.message}`
      };
    }
  }

  /**
   * Analyze content structure and extract metadata
   * @param {string} content - Content to analyze
   * @returns {Object} - Content analysis
   */
  analyzeContent(content) {
    const hashtags = this.extractHashtags(content);
    const mentions = this.extractMentions(content);
    const urls = this.extractUrls(content);
    const sentences = this.extractSentences(content);
    const words = content.split(/\s+/).filter(word => word.length > 0);

    return {
      length: content.length,
      wordCount: words.length,
      sentenceCount: sentences.length,
      hashtags,
      mentions,
      urls,
      readabilityScore: this.calculateReadabilityScore(content),
      sentiment: this.analyzeSentiment(content),
      keywords: this.extractKeywords(content)
    };
  }

  /**
   * Extract hashtags from content
   * @param {string} content - Content to analyze
   * @returns {Array} - Array of hashtags
   */
  extractHashtags(content) {
    const hashtagRegex = /#[\w\u0590-\u05ff]+/g;
    return content.match(hashtagRegex) || [];
  }

  /**
   * Extract mentions from content
   * @param {string} content - Content to analyze
   * @returns {Array} - Array of mentions
   */
  extractMentions(content) {
    const mentionRegex = /@[\w\u0590-\u05ff]+/g;
    return content.match(mentionRegex) || [];
  }

  /**
   * Extract URLs from content
   * @param {string} content - Content to analyze
   * @returns {Array} - Array of URLs
   */
  extractUrls(content) {
    const urlRegex = /https?:\/\/[^\s]+/g;
    return content.match(urlRegex) || [];
  }

  /**
   * Extract sentences from content
   * @param {string} content - Content to analyze
   * @returns {Array} - Array of sentences
   */
  extractSentences(content) {
    return content.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
  }

  /**
   * Calculate basic readability score
   * @param {string} content - Content to analyze
   * @returns {number} - Readability score (0-100, higher is more readable)
   */
  calculateReadabilityScore(content) {
    const sentences = this.extractSentences(content);
    const words = content.split(/\s+/).filter(word => word.length > 0);
    const syllables = words.reduce((count, word) => count + this.countSyllables(word), 0);

    if (sentences.length === 0 || words.length === 0) return 0;

    // Simplified Flesch Reading Ease formula
    const avgSentenceLength = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;
    
    const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Count syllables in a word (simplified)
   * @param {string} word - Word to count syllables
   * @returns {number} - Number of syllables
   */
  countSyllables(word) {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    
    const vowels = 'aeiouy';
    let count = 0;
    let previousWasVowel = false;
    
    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i]);
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }
    
    // Handle silent e
    if (word.endsWith('e')) {
      count--;
    }
    
    return Math.max(1, count);
  }

  /**
   * Analyze sentiment (basic implementation)
   * @param {string} content - Content to analyze
   * @returns {Object} - Sentiment analysis
   */
  analyzeSentiment(content) {
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'happy', 'excited'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'sad', 'angry', 'disappointed', 'frustrated'];
    
    const words = content.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;
    
    words.forEach(word => {
      if (positiveWords.includes(word)) positiveCount++;
      if (negativeWords.includes(word)) negativeCount++;
    });
    
    const total = positiveCount + negativeCount;
    if (total === 0) {
      return { sentiment: 'neutral', confidence: 0.5, positive: 0, negative: 0 };
    }
    
    const positiveRatio = positiveCount / total;
    let sentiment = 'neutral';
    let confidence = 0.5;
    
    if (positiveRatio > 0.6) {
      sentiment = 'positive';
      confidence = positiveRatio;
    } else if (positiveRatio < 0.4) {
      sentiment = 'negative';
      confidence = 1 - positiveRatio;
    }
    
    return {
      sentiment,
      confidence: Math.round(confidence * 100) / 100,
      positive: positiveCount,
      negative: negativeCount
    };
  }

  /**
   * Extract keywords from content
   * @param {string} content - Content to analyze
   * @returns {Array} - Array of keywords with frequency
   */
  extractKeywords(content) {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
    ]);
    
    const words = content.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word));
    
    const frequency = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });
    
    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word, count]) => ({ word, count }));
  }

  /**
   * Optimize content for Twitter
   * @param {string} content - Original content
   * @param {Object} config - Platform configuration
   * @param {Object} options - Optimization options
   * @returns {string} - Optimized content
   */
  optimizeForTwitter(content, config, options) {
    if (content.length <= config.maxLength) {
      return content;
    }

    // Try to truncate at sentence boundaries
    const sentences = this.extractSentences(content);
    let optimized = '';
    
    for (const sentence of sentences) {
      if ((optimized + sentence + '.').length <= config.maxLength - 3) {
        optimized += sentence + '.';
      } else {
        break;
      }
    }
    
    if (optimized.length === 0) {
      // Truncate at word boundaries
      const words = content.split(' ');
      optimized = '';
      
      for (const word of words) {
        if ((optimized + ' ' + word).length <= config.maxLength - 3) {
          optimized += (optimized ? ' ' : '') + word;
        } else {
          break;
        }
      }
    }
    
    return optimized + '...';
  }

  /**
   * Optimize content for Instagram
   * @param {string} content - Original content
   * @param {Object} config - Platform configuration
   * @param {Object} options - Optimization options
   * @returns {string} - Optimized content
   */
  optimizeForInstagram(content, config, options) {
    if (content.length <= config.maxLength) {
      return content;
    }

    // For Instagram, we can be more generous with length
    // Focus on keeping the first part engaging
    const sentences = this.extractSentences(content);
    let optimized = '';
    
    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i];
      if ((optimized + sentence + '.').length <= config.maxLength) {
        optimized += sentence + '.';
      } else {
        break;
      }
    }
    
    return optimized || content.substring(0, config.maxLength - 3) + '...';
  }

  /**
   * Optimize content for LinkedIn
   * @param {string} content - Original content
   * @param {Object} config - Platform configuration
   * @param {Object} options - Optimization options
   * @returns {string} - Optimized content
   */
  optimizeForLinkedIn(content, config, options) {
    if (content.length <= config.maxLength) {
      return content;
    }

    // LinkedIn allows longer content, so we can be more generous
    // Focus on professional tone and complete thoughts
    const paragraphs = content.split('\n\n');
    let optimized = '';
    
    for (const paragraph of paragraphs) {
      if ((optimized + '\n\n' + paragraph).length <= config.maxLength) {
        optimized += (optimized ? '\n\n' : '') + paragraph;
      } else {
        break;
      }
    }
    
    return optimized || content.substring(0, config.maxLength - 3) + '...';
  }

  /**
   * Optimize content for YouTube
   * @param {string} content - Original content
   * @param {Object} config - Platform configuration
   * @param {Object} options - Optimization options
   * @returns {string} - Optimized content
   */
  optimizeForYouTube(content, config, options) {
    if (options.type === 'title') {
      return content.length <= config.titleMaxLength 
        ? content 
        : content.substring(0, config.titleMaxLength - 3) + '...';
    }
    
    if (content.length <= config.descriptionMaxLength) {
      return content;
    }
    
    return content.substring(0, config.descriptionMaxLength - 3) + '...';
  }

  /**
   * Optimize content for Facebook
   * @param {string} content - Original content
   * @param {Object} config - Platform configuration
   * @param {Object} options - Optimization options
   * @returns {string} - Optimized content
   */
  optimizeForFacebook(content, config, options) {
    // Facebook has very high limits, so usually no truncation needed
    if (content.length <= config.maxLength) {
      return content;
    }
    
    return content.substring(0, config.maxLength - 3) + '...';
  }

  /**
   * Optimize content for TikTok
   * @param {string} content - Original content
   * @param {Object} config - Platform configuration
   * @param {Object} options - Optimization options
   * @returns {string} - Optimized content
   */
  optimizeForTikTok(content, config, options) {
    if (content.length <= config.maxLength) {
      return content;
    }

    // TikTok content should be punchy and engaging
    const sentences = this.extractSentences(content);
    let optimized = '';
    
    for (const sentence of sentences) {
      if ((optimized + sentence + '.').length <= config.maxLength) {
        optimized += sentence + '.';
      } else {
        break;
      }
    }
    
    return optimized || content.substring(0, config.maxLength - 3) + '...';
  }

  /**
   * Generate hashtag suggestions based on content
   * @param {string} content - Content to analyze
   * @param {string} platform - Target platform
   * @param {number} maxHashtags - Maximum number of hashtags
   * @returns {Array} - Array of suggested hashtags
   */
  generateHashtagSuggestions(content, platform, maxHashtags = 5) {
    const keywords = this.extractKeywords(content);
    const suggestions = [];
    
    // Convert top keywords to hashtags
    for (const keyword of keywords.slice(0, maxHashtags)) {
      const hashtag = '#' + keyword.word.replace(/[^a-zA-Z0-9]/g, '');
      if (hashtag.length > 1) {
        suggestions.push(hashtag);
      }
    }
    
    // Add platform-specific popular hashtags
    const platformHashtags = this.getPlatformHashtags(platform);
    const remainingSlots = maxHashtags - suggestions.length;
    
    if (remainingSlots > 0) {
      suggestions.push(...platformHashtags.slice(0, remainingSlots));
    }
    
    return suggestions;
  }

  /**
   * Get popular hashtags for a platform
   * @param {string} platform - Platform name
   * @returns {Array} - Array of popular hashtags
   */
  getPlatformHashtags(platform) {
    const hashtags = {
      instagram: ['#instagood', '#photooftheday', '#love', '#beautiful', '#happy'],
      twitter: ['#trending', '#news', '#update', '#follow', '#retweet'],
      linkedin: ['#professional', '#business', '#career', '#networking', '#industry'],
      tiktok: ['#fyp', '#viral', '#trending', '#foryou', '#tiktok'],
      youtube: ['#youtube', '#video', '#subscribe', '#content', '#creator'],
      facebook: ['#facebook', '#social', '#community', '#share', '#connect']
    };
    
    return hashtags[platform.toLowerCase()] || [];
  }

  /**
   * Calculate content quality score
   * @param {string} content - Content to analyze
   * @param {string} platform - Target platform
   * @returns {Object} - Quality score and recommendations
   */
  calculateQualityScore(content, platform) {
    const analysis = this.analyzeContent(content);
    const platformConfig = this.platformLimits[platform.toLowerCase()];
    
    let score = 0;
    const recommendations = [];
    
    // Length score (30 points)
    if (platformConfig) {
      const lengthRatio = content.length / platformConfig.maxLength;
      if (lengthRatio <= 0.8) {
        score += 30;
      } else if (lengthRatio <= 1.0) {
        score += 20;
      } else {
        score += 10;
        recommendations.push('Content exceeds platform character limit');
      }
    }
    
    // Readability score (25 points)
    const readabilityPoints = Math.round(analysis.readabilityScore * 0.25);
    score += readabilityPoints;
    if (analysis.readabilityScore < 60) {
      recommendations.push('Consider simplifying language for better readability');
    }
    
    // Engagement elements (25 points)
    let engagementScore = 0;
    if (analysis.hashtags.length > 0) engagementScore += 8;
    if (analysis.mentions.length > 0) engagementScore += 7;
    if (content.includes('?')) engagementScore += 5; // Questions engage
    if (content.match(/[!]{1,3}/)) engagementScore += 5; // Excitement
    score += engagementScore;
    
    // Structure score (20 points)
    let structureScore = 0;
    if (analysis.sentenceCount > 1) structureScore += 10;
    if (content.includes('\n')) structureScore += 5; // Paragraphs
    if (analysis.wordCount >= 10) structureScore += 5;
    score += structureScore;
    
    // Determine grade
    let grade = 'F';
    if (score >= 90) grade = 'A';
    else if (score >= 80) grade = 'B';
    else if (score >= 70) grade = 'C';
    else if (score >= 60) grade = 'D';
    
    return {
      score: Math.min(100, score),
      grade,
      recommendations,
      analysis
    };
  }
}

module.exports = ContentOptimizer;
