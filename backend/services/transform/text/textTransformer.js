const BaseTransformer = require('../baseTransformer');
const ContentOptimizer = require('./contentOptimizer');

/**
 * Text Transformer for handling text content transformations
 */
class TextTransformer extends BaseTransformer {
  /**
   * Constructor for the TextTransformer
   * @param {Object} options - Configuration options for the transformer
   */
  constructor(options = {}) {
    super(options);
    this.transformerType = 'text';
    this.supportedInputTypes = [
      'text/plain',
      'text/markdown',
      'text/html',
      'text/csv',
      'application/json'
    ];
    this.supportedOutputTypes = [
      'text/plain',
      'text/markdown',
      'text/html',
      'text/csv',
      'application/json'
    ];

    // Initialize content optimizer
    this.contentOptimizer = new ContentOptimizer();
  }

  /**
   * Transform text content from one format to another
   * @param {string} inputData - The text data to be transformed
   * @param {string} inputType - The type of the input text
   * @param {string} outputType - The desired output type
   * @param {Object} options - Additional options for the transformation
   * @returns {Promise<Object>} - Transformation result with success flag, data, and optional error message
   */
  async transform(inputData, inputType, outputType, options = {}) {
    // Validate input
    const validation = this.validateInput(inputData, inputType, outputType);
    if (!validation.success) {
      return validation;
    }

    try {
      // If input and output types are the same, apply text operations without conversion
      if (inputType === outputType) {
        return await this.applyTextOperations(inputData, options);
      }

      // Convert between different text formats
      let convertedText;

      // Convert input to plain text as an intermediate step
      const plainText = await this.convertToPlainText(inputData, inputType);

      // Convert plain text to the desired output format
      switch (outputType) {
        case 'text/plain':
          convertedText = plainText;
          break;
        case 'text/markdown':
          convertedText = await this.convertToMarkdown(plainText, options);
          break;
        case 'text/html':
          convertedText = await this.convertToHtml(plainText, options);
          break;
        case 'text/csv':
          convertedText = await this.convertToCsv(plainText, options);
          break;
        case 'application/json':
          convertedText = await this.convertToJson(plainText, options);
          break;
        default:
          return {
            success: false,
            error: `Unsupported output type: ${outputType}`
          };
      }

      // Apply any additional text operations
      return await this.applyTextOperations(convertedText, options);
    } catch (error) {
      return {
        success: false,
        error: `Text transformation failed: ${error.message}`
      };
    }
  }

  /**
   * Convert input text to plain text
   * @param {string} inputData - The input text data
   * @param {string} inputType - The type of the input text
   * @returns {Promise<string>} - Plain text version of the input
   */
  async convertToPlainText(inputData, inputType) {
    switch (inputType) {
      case 'text/plain':
        return inputData;
      case 'text/markdown':
        // Simple markdown to plain text conversion (remove markdown syntax)
        return inputData
          .replace(/#+\s+/g, '') // Remove headers
          .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
          .replace(/\*(.*?)\*/g, '$1') // Remove italic
          .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
          .replace(/`(.*?)`/g, '$1') // Remove inline code
          .replace(/```[\s\S]*?```/g, '') // Remove code blocks
          .replace(/>\s+(.*)/g, '$1') // Remove blockquotes
          .replace(/- (.*)/g, '$1') // Remove list items
          .replace(/\d+\. (.*)/g, '$1'); // Remove numbered list items
      case 'text/html':
        // Simple HTML to plain text conversion (remove HTML tags)
        return inputData
          .replace(/<[^>]*>/g, '') // Remove HTML tags
          .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
          .replace(/&lt;/g, '<') // Replace &lt;
          .replace(/&gt;/g, '>') // Replace &gt;
          .replace(/&amp;/g, '&') // Replace &amp;
          .replace(/&quot;/g, '"') // Replace &quot;
          .replace(/&apos;/g, "'"); // Replace &apos;
      case 'text/csv':
        // Convert CSV to plain text (join with newlines)
        return inputData.split('\n').map(line => line.split(',').join(' ')).join('\n');
      case 'application/json':
        // Convert JSON to plain text
        try {
          const jsonObj = JSON.parse(inputData);
          return JSON.stringify(jsonObj, null, 2);
        } catch (error) {
          throw new Error(`Invalid JSON: ${error.message}`);
        }
      default:
        throw new Error(`Unsupported input type for plain text conversion: ${inputType}`);
    }
  }

  /**
   * Convert plain text to markdown
   * @param {string} plainText - The plain text to convert
   * @param {Object} options - Conversion options
   * @returns {Promise<string>} - Markdown text
   */
  async convertToMarkdown(plainText, options = {}) {
    // Simple plain text to markdown conversion
    const { headingLevel = 2, addFrontMatter = false } = options;

    // Split text into paragraphs
    const paragraphs = plainText.split(/\n\s*\n/);

    // Convert first paragraph to heading if requested
    let markdown = '';

    if (paragraphs.length > 0) {
      if (options.firstParagraphAsHeading) {
        markdown += `${'#'.repeat(headingLevel)} ${paragraphs[0]}\n\n`;
        paragraphs.shift();
      }

      // Add front matter if requested
      if (addFrontMatter) {
        const title = options.title || 'Untitled';
        const date = options.date || new Date().toISOString().split('T')[0];

        markdown = `---
title: ${title}
date: ${date}
${options.tags ? `tags: [${options.tags.join(', ')}]` : ''}
---

${markdown}`;
      }

      // Add remaining paragraphs
      markdown += paragraphs.join('\n\n');
    }

    return markdown;
  }

  /**
   * Convert plain text to HTML
   * @param {string} plainText - The plain text to convert
   * @param {Object} options - Conversion options
   * @returns {Promise<string>} - HTML text
   */
  async convertToHtml(plainText, options = {}) {
    // Simple plain text to HTML conversion
    const { addDocType = false, addHtmlStructure = false, cssClass = '' } = options;

    // Split text into paragraphs
    const paragraphs = plainText.split(/\n\s*\n/);

    // Convert paragraphs to HTML
    let html = paragraphs.map(p => `<p>${p.replace(/\n/g, '<br>')}</p>`).join('\n');

    // Add HTML structure if requested
    if (addHtmlStructure) {
      const title = options.title || 'Untitled';

      html = `<div${cssClass ? ` class="${cssClass}"` : ''}>
  ${options.firstParagraphAsHeading && paragraphs.length > 0 ?
    `<h1>${paragraphs[0]}</h1>\n` : ''}
  ${html}
</div>`;

      if (options.fullPage) {
        html = `<html>
<head>
  <title>${title}</title>
  ${options.css ? `<style>${options.css}</style>` : ''}
</head>
<body>
  ${html}
</body>
</html>`;
      }
    }

    // Add DOCTYPE if requested
    if (addDocType) {
      html = `<!DOCTYPE html>\n${html}`;
    }

    return html;
  }

  /**
   * Convert plain text to CSV
   * @param {string} plainText - The plain text to convert
   * @param {Object} options - Conversion options
   * @returns {Promise<string>} - CSV text
   */
  async convertToCsv(plainText, options = {}) {
    // Simple plain text to CSV conversion
    const { delimiter = ',', includeHeader = false } = options;

    // Split text into lines
    const lines = plainText.split('\n').filter(line => line.trim() !== '');

    // Convert lines to CSV
    const csvLines = lines.map(line => {
      // Split line into words and escape any that contain the delimiter
      const words = line.split(/\s+/);
      return words.map(word => {
        if (word.includes(delimiter) || word.includes('"')) {
          return `"${word.replace(/"/g, '""')}"`;
        }
        return word;
      }).join(delimiter);
    });

    // Add header if requested
    if (includeHeader && options.header) {
      csvLines.unshift(options.header);
    }

    return csvLines.join('\n');
  }

  /**
   * Convert plain text to JSON
   * @param {string} plainText - The plain text to convert
   * @param {Object} options - Conversion options
   * @returns {Promise<string>} - JSON text
   */
  async convertToJson(plainText, options = {}) {
    // Simple plain text to JSON conversion
    const { structure = 'object' } = options;

    // Split text into paragraphs
    const paragraphs = plainText.split(/\n\s*\n/).filter(p => p.trim() !== '');

    let jsonObj;

    if (structure === 'array') {
      // Convert to array of paragraphs
      jsonObj = paragraphs;
    } else if (structure === 'lines') {
      // Convert to array of lines
      jsonObj = plainText.split('\n').filter(line => line.trim() !== '');
    } else if (structure === 'keyValue') {
      // Convert to key-value pairs (assuming "key: value" format)
      jsonObj = {};
      plainText.split('\n').forEach(line => {
        const match = line.match(/^([^:]+):\s*(.*)$/);
        if (match) {
          const [, key, value] = match;
          jsonObj[key.trim()] = value.trim();
        }
      });
    } else {
      // Default: convert to object with text property
      jsonObj = {
        text: plainText,
        paragraphs: paragraphs.length,
        characters: plainText.length,
        words: plainText.split(/\s+/).filter(w => w.trim() !== '').length
      };
    }

    return JSON.stringify(jsonObj, null, options.pretty ? 2 : 0);
  }

  /**
   * Apply text operations to the content
   * @param {string} text - The text to process
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Processing result with success flag and processed text
   */
  async applyTextOperations(text, options = {}) {
    let processedText = text;

    // Apply text operations based on options
    if (options.trim) {
      processedText = processedText.trim();
    }

    if (options.uppercase) {
      processedText = processedText.toUpperCase();
    }

    if (options.lowercase) {
      processedText = processedText.toLowerCase();
    }

    if (options.capitalize) {
      processedText = processedText.replace(/\b\w/g, c => c.toUpperCase());
    }

    if (options.removeExtraSpaces) {
      processedText = processedText.replace(/\s+/g, ' ');
    }

    if (options.truncate && typeof options.truncate === 'number') {
      processedText = processedText.substring(0, options.truncate);
      if (processedText.length < text.length) {
        processedText += options.ellipsis || '...';
      }
    }

    if (options.replace && Array.isArray(options.replace)) {
      options.replace.forEach(({ search, replacement }) => {
        if (search && replacement !== undefined) {
          const regex = new RegExp(search, options.replaceGlobal ? 'g' : '');
          processedText = processedText.replace(regex, replacement);
        }
      });
    }

    if (options.prefix) {
      processedText = options.prefix + processedText;
    }

    if (options.suffix) {
      processedText = processedText + options.suffix;
    }

    // Calculate text statistics
    const stats = {
      characters: processedText.length,
      words: processedText.split(/\s+/).filter(w => w.trim() !== '').length,
      lines: processedText.split('\n').length,
      paragraphs: processedText.split(/\n\s*\n/).filter(p => p.trim() !== '').length
    };

    return {
      success: true,
      data: processedText,
      stats
    };
  }

  /**
   * Optimize text content for specific platform
   * @param {string} inputData - The text data to be optimized
   * @param {string} platform - Target platform
   * @param {Object} options - Optimization options
   * @returns {Promise<Object>} - Optimization result
   */
  async optimizeForPlatform(inputData, platform, options = {}) {
    try {
      const result = this.contentOptimizer.optimizeForPlatform(inputData, platform, options);

      if (!result.success) {
        return result;
      }

      // Add quality score
      const qualityScore = this.contentOptimizer.calculateQualityScore(result.optimizedContent, platform);

      // Generate hashtag suggestions if requested
      let hashtagSuggestions = [];
      if (options.generateHashtags) {
        const platformConfig = this.contentOptimizer.platformLimits[platform.toLowerCase()];
        const maxHashtags = platformConfig ? platformConfig.hashtagLimit : 5;
        hashtagSuggestions = this.contentOptimizer.generateHashtagSuggestions(
          result.optimizedContent,
          platform,
          Math.min(maxHashtags, options.maxHashtags || 5)
        );
      }

      return {
        success: true,
        data: result.optimizedContent,
        optimization: {
          platform,
          originalLength: result.analysis.length,
          optimizedLength: result.optimizedContent.length,
          characterLimit: result.characterLimit,
          withinLimit: result.withinLimit,
          suggestions: result.suggestions,
          qualityScore,
          hashtagSuggestions,
          analysis: result.analysis
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `Platform optimization failed: ${error.message}`
      };
    }
  }

  /**
   * Generate content summary
   * @param {string} inputData - The text data to summarize
   * @param {Object} options - Summary options
   * @returns {Promise<Object>} - Summary result
   */
  async generateSummary(inputData, options = {}) {
    try {
      const maxLength = options.maxLength || 200;
      const sentences = this.contentOptimizer.extractSentences(inputData);

      if (sentences.length <= 1) {
        return {
          success: true,
          data: inputData,
          summary: {
            originalLength: inputData.length,
            summaryLength: inputData.length,
            compressionRatio: 1.0,
            sentenceCount: sentences.length
          }
        };
      }

      // Simple extractive summarization
      // Score sentences based on word frequency and position
      const wordFreq = this.calculateWordFrequency(inputData);
      const sentenceScores = sentences.map((sentence, index) => {
        const words = sentence.toLowerCase().split(/\s+/);
        const score = words.reduce((sum, word) => sum + (wordFreq[word] || 0), 0) / words.length;
        const positionScore = 1 - (index / sentences.length); // Earlier sentences get higher scores
        return {
          sentence: sentence.trim(),
          score: score + (positionScore * 0.3),
          index
        };
      });

      // Sort by score and select top sentences
      sentenceScores.sort((a, b) => b.score - a.score);

      let summary = '';
      const selectedSentences = [];

      for (const sentenceData of sentenceScores) {
        const testSummary = summary + (summary ? ' ' : '') + sentenceData.sentence + '.';
        if (testSummary.length <= maxLength) {
          summary = testSummary;
          selectedSentences.push(sentenceData);
        } else {
          break;
        }
      }

      // Sort selected sentences by original order
      selectedSentences.sort((a, b) => a.index - b.index);
      summary = selectedSentences.map(s => s.sentence).join('. ') + '.';

      return {
        success: true,
        data: summary,
        summary: {
          originalLength: inputData.length,
          summaryLength: summary.length,
          compressionRatio: Math.round((summary.length / inputData.length) * 100) / 100,
          sentenceCount: selectedSentences.length,
          originalSentenceCount: sentences.length
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `Summary generation failed: ${error.message}`
      };
    }
  }

  /**
   * Calculate word frequency for summarization
   * @param {string} text - Text to analyze
   * @returns {Object} - Word frequency map
   */
  calculateWordFrequency(text) {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
    ]);

    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word));

    const frequency = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });

    // Normalize frequencies
    const maxFreq = Math.max(...Object.values(frequency));
    Object.keys(frequency).forEach(word => {
      frequency[word] = frequency[word] / maxFreq;
    });

    return frequency;
  }

  /**
   * Extract and suggest hashtags from content
   * @param {string} inputData - The text data to analyze
   * @param {Object} options - Hashtag options
   * @returns {Promise<Object>} - Hashtag suggestions
   */
  async extractHashtags(inputData, options = {}) {
    try {
      const platform = options.platform || 'general';
      const maxHashtags = options.maxHashtags || 10;

      const existingHashtags = this.contentOptimizer.extractHashtags(inputData);
      const suggestions = this.contentOptimizer.generateHashtagSuggestions(inputData, platform, maxHashtags);

      return {
        success: true,
        data: {
          existing: existingHashtags,
          suggestions: suggestions.filter(tag => !existingHashtags.includes(tag)),
          all: [...new Set([...existingHashtags, ...suggestions])]
        },
        analysis: this.contentOptimizer.analyzeContent(inputData)
      };

    } catch (error) {
      return {
        success: false,
        error: `Hashtag extraction failed: ${error.message}`
      };
    }
  }

  /**
   * Analyze content quality and provide recommendations
   * @param {string} inputData - The text data to analyze
   * @param {Object} options - Analysis options
   * @returns {Promise<Object>} - Quality analysis result
   */
  async analyzeQuality(inputData, options = {}) {
    try {
      const platform = options.platform || 'general';
      const analysis = this.contentOptimizer.analyzeContent(inputData);
      const qualityScore = this.contentOptimizer.calculateQualityScore(inputData, platform);

      return {
        success: true,
        data: {
          analysis,
          qualityScore,
          platform
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `Quality analysis failed: ${error.message}`
      };
    }
  }
}

module.exports = TextTransformer;
