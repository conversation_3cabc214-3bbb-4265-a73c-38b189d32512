const transformerFactory = require('./transformerFactory');
const TextTransformer = require('./text/textTransformer');
const ImageTransformer = require('./image/imageTransformer');
const VideoTransformer = require('./video/videoTransformer');
const AudioTransformer = require('./audio/audioTransformer');

// Register all transformers with the factory
transformerFactory.registerTransformer('text', TextTransformer);
transformerFactory.registerTransformer('image', ImageTransformer);
transformerFactory.registerTransformer('video', VideoTransformer);
transformerFactory.registerTransformer('audio', AudioTransformer);

// Export the factory and individual transformers
module.exports = {
  transformerFactory,
  TextTransformer,
  ImageTransformer,
  VideoTransformer,
  AudioTransformer
};
