/**
 * Factory for creating appropriate transformers based on input and output types
 */
class TransformerFactory {
  constructor() {
    this.transformers = {};
  }

  /**
   * Register a transformer class with the factory
   * @param {string} type - The type of transformer (e.g., 'text', 'image', 'video', 'audio')
   * @param {Class} TransformerClass - The transformer class to register
   */
  registerTransformer(type, TransformerClass) {
    this.transformers[type] = TransformerClass;
  }

  /**
   * Get a transformer instance based on the input and output types
   * @param {string} inputType - The type of the input content
   * @param {string} outputType - The desired output type
   * @param {Object} options - Configuration options for the transformer
   * @returns {Object} - An instance of the appropriate transformer or null if no suitable transformer is found
   */
  getTransformer(inputType, outputType, options = {}) {
    // Determine the transformer type based on input and output types
    const transformerType = this.determineTransformerType(inputType, outputType);
    
    if (!transformerType || !this.transformers[transformerType]) {
      return null;
    }
    
    // Create and return a new instance of the transformer
    const TransformerClass = this.transformers[transformerType];
    const transformer = new TransformerClass(options);
    
    // Verify that the transformer supports the input and output types
    if (!transformer.supportsInputType(inputType) || !transformer.supportsOutputType(outputType)) {
      return null;
    }
    
    return transformer;
  }

  /**
   * Determine the appropriate transformer type based on input and output types
   * @param {string} inputType - The type of the input content
   * @param {string} outputType - The desired output type
   * @returns {string|null} - The transformer type or null if no suitable transformer is found
   */
  determineTransformerType(inputType, outputType) {
    // Extract the main content type (e.g., 'text', 'image', 'video', 'audio')
    const inputMainType = inputType.split('/')[0];
    const outputMainType = outputType.split('/')[0];
    
    // If input and output main types are the same, use that type
    if (inputMainType === outputMainType && this.transformers[inputMainType]) {
      return inputMainType;
    }
    
    // For cross-type transformations, check if we have a specific transformer
    const crossType = `${inputMainType}-to-${outputMainType}`;
    if (this.transformers[crossType]) {
      return crossType;
    }
    
    // If no specific transformer is found, try to use the input type's transformer
    if (this.transformers[inputMainType]) {
      return inputMainType;
    }
    
    // If still no transformer is found, return null
    return null;
  }

  /**
   * Transform content using the appropriate transformer
   * @param {any} inputData - The data to be transformed
   * @param {string} inputType - The type of the input data
   * @param {string} outputType - The desired output type
   * @param {Object} options - Additional options for the transformation
   * @returns {Promise<Object>} - Transformation result with success flag, data, and optional error message
   */
  async transform(inputData, inputType, outputType, options = {}) {
    const transformer = this.getTransformer(inputType, outputType, options);
    
    if (!transformer) {
      return {
        success: false,
        error: `No suitable transformer found for converting ${inputType} to ${outputType}`
      };
    }
    
    return await transformer.transform(inputData, inputType, outputType, options);
  }

  /**
   * Get information about all registered transformers
   * @returns {Object} - Information about all registered transformers
   */
  getTransformerInfo() {
    const info = {};
    
    for (const [type, TransformerClass] of Object.entries(this.transformers)) {
      const transformer = new TransformerClass();
      info[type] = transformer.getInfo();
    }
    
    return info;
  }
}

// Create and export a singleton instance
const factory = new TransformerFactory();
module.exports = factory;
