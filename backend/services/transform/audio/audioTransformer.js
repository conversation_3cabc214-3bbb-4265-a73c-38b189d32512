const BaseTransformer = require('../baseTransformer');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

/**
 * Audio Transformer for handling audio content transformations
 */
class AudioTransformer extends BaseTransformer {
  /**
   * Constructor for the AudioTransformer
   * @param {Object} options - Configuration options for the transformer
   */
  constructor(options = {}) {
    super(options);
    this.transformerType = 'audio';
    this.supportedInputTypes = [
      'audio/mpeg',
      'audio/mp4',
      'audio/ogg',
      'audio/wav',
      'audio/webm',
      'audio/flac',
      'audio/aac'
    ];
    this.supportedOutputTypes = [
      'audio/mpeg',
      'audio/mp4',
      'audio/ogg',
      'audio/wav',
      'audio/webm',
      'audio/flac',
      'audio/aac'
    ];
    
    // Set default options
    this.options = {
      tempDir: options.tempDir || path.join(process.cwd(), 'temp'),
      ffmpegPath: options.ffmpegPath || null,
      ffprobePath: options.ffprobePath || null,
      ...options
    };
    
    // Configure ffmpeg paths if provided
    if (this.options.ffmpegPath) {
      ffmpeg.setFfmpegPath(this.options.ffmpegPath);
    }
    
    if (this.options.ffprobePath) {
      ffmpeg.setFfprobePath(this.options.ffprobePath);
    }
    
    // Ensure temp directory exists
    this.ensureTempDir();
  }

  /**
   * Ensure the temporary directory exists
   */
  async ensureTempDir() {
    try {
      await fs.mkdir(this.options.tempDir, { recursive: true });
    } catch (error) {
      console.error(`Error creating temp directory: ${error.message}`);
    }
  }

  /**
   * Transform audio content from one format to another
   * @param {string} inputData - The audio file path to be transformed
   * @param {string} inputType - The type of the input audio
   * @param {string} outputType - The desired output type
   * @param {Object} options - Additional options for the transformation
   * @returns {Promise<Object>} - Transformation result with success flag, data, and optional error message
   */
  async transform(inputData, inputType, outputType, options = {}) {
    // Validate input
    const validation = this.validateInput(inputData, inputType, outputType);
    if (!validation.success) {
      return validation;
    }

    try {
      // Input must be a file path for ffmpeg
      if (typeof inputData !== 'string') {
        return {
          success: false,
          error: 'Input data must be a file path for audio transformations'
        };
      }
      
      // Check if the input file exists
      try {
        await fs.access(inputData);
      } catch (error) {
        return {
          success: false,
          error: `Input file does not exist or is not accessible: ${inputData}`
        };
      }
      
      // Get audio metadata
      const metadata = await this.getAudioMetadata(inputData);
      
      // Determine output format and file extension
      const { format, extension } = this.getFormatAndExtension(outputType);
      if (!format) {
        return {
          success: false,
          error: `Unsupported output type: ${outputType}`
        };
      }
      
      // Generate output file path
      const outputPath = options.outputPath || path.join(
        this.options.tempDir, 
        `${uuidv4()}.${extension}`
      );
      
      // Perform the transformation
      await this.processAudio(inputData, outputPath, format, options, metadata);
      
      // Get output file stats
      const stats = await fs.stat(outputPath);
      
      return {
        success: true,
        filePath: outputPath,
        metadata: {
          format,
          size: stats.size,
          ...await this.getAudioMetadata(outputPath)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `Audio transformation failed: ${error.message}`
      };
    }
  }

  /**
   * Get audio metadata using ffprobe
   * @param {string} filePath - Path to the audio file
   * @returns {Promise<Object>} - Audio metadata
   */
  getAudioMetadata(filePath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }
        
        // Extract relevant metadata
        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');
        
        resolve({
          duration: metadata.format.duration,
          size: metadata.format.size,
          bitrate: metadata.format.bit_rate,
          format: metadata.format.format_name,
          audio: audioStream ? {
            codec: audioStream.codec_name,
            channels: audioStream.channels,
            sampleRate: audioStream.sample_rate,
            bitrate: audioStream.bit_rate
          } : null
        });
      });
    });
  }

  /**
   * Get format and file extension from MIME type
   * @param {string} mimeType - The MIME type
   * @returns {Object} - Object containing format and extension
   */
  getFormatAndExtension(mimeType) {
    const formatMap = {
      'audio/mpeg': { format: 'mp3', extension: 'mp3' },
      'audio/mp4': { format: 'm4a', extension: 'm4a' },
      'audio/ogg': { format: 'ogg', extension: 'ogg' },
      'audio/wav': { format: 'wav', extension: 'wav' },
      'audio/webm': { format: 'webm', extension: 'webm' },
      'audio/flac': { format: 'flac', extension: 'flac' },
      'audio/aac': { format: 'aac', extension: 'aac' }
    };
    
    return formatMap[mimeType] || { format: null, extension: null };
  }

  /**
   * Process audio using ffmpeg
   * @param {string} inputPath - Path to the input audio file
   * @param {string} outputPath - Path for the output file
   * @param {string} format - Output format
   * @param {Object} options - Processing options
   * @param {Object} metadata - Input audio metadata
   * @returns {Promise<void>} - Promise that resolves when processing is complete
   */
  processAudio(inputPath, outputPath, format, options, metadata) {
    return new Promise((resolve, reject) => {
      // Initialize ffmpeg command
      let command = ffmpeg(inputPath);
      
      // Apply audio transformations based on options
      command = this.applyAudioTransformations(command, options, metadata, format);
      
      // Set output format
      command.format(format);
      
      // Set output path
      command.output(outputPath);
      
      // Handle events
      command
        .on('start', (commandLine) => {
          console.log('FFmpeg process started:', commandLine);
        })
        .on('progress', (progress) => {
          console.log(`Processing: ${Math.floor(progress.percent)}% done`);
        })
        .on('end', () => {
          console.log('FFmpeg process completed');
          resolve();
        })
        .on('error', (err) => {
          console.error('FFmpeg error:', err);
          reject(err);
        });
      
      // Run the command
      command.run();
    });
  }

  /**
   * Apply audio transformations based on options
   * @param {Object} command - FFmpeg command
   * @param {Object} options - Transformation options
   * @param {Object} metadata - Input audio metadata
   * @param {string} format - Output format
   * @returns {Object} - Modified FFmpeg command
   */
  applyAudioTransformations(command, options, metadata, format) {
    // Set audio codec
    if (options.audioCodec) {
      command.audioCodec(options.audioCodec);
    } else {
      // Set default codecs based on format
      switch (format) {
        case 'mp3':
          command.audioCodec('libmp3lame');
          break;
        case 'm4a':
          command.audioCodec('aac');
          break;
        case 'ogg':
          command.audioCodec('libvorbis');
          break;
        case 'flac':
          command.audioCodec('flac');
          break;
        case 'aac':
          command.audioCodec('aac');
          break;
        // wav and webm can use default codecs
      }
    }
    
    // Set audio bitrate
    if (options.audioBitrate) {
      command.audioBitrate(options.audioBitrate);
    }
    
    // Set audio channels
    if (options.channels) {
      command.audioChannels(options.channels);
    }
    
    // Set audio sample rate
    if (options.sampleRate) {
      command.audioFrequency(options.sampleRate);
    }
    
    // Set duration/trim audio
    if (options.duration) {
      command.duration(options.duration);
    }
    
    // Set start time
    if (options.startTime) {
      command.seekInput(options.startTime);
    }
    
    // Set end time (alternative to duration)
    if (options.endTime && !options.duration) {
      const duration = options.endTime - (options.startTime || 0);
      if (duration > 0) {
        command.duration(duration);
      }
    }
    
    // Set quality
    if (options.quality) {
      switch (format) {
        case 'mp3':
          // For MP3, quality is 0-9 (0 = best, 9 = worst)
          const mp3Quality = Math.max(0, Math.min(9, Math.floor(9 - (options.quality / 11.1))));
          command.addOption('-q:a', mp3Quality.toString());
          break;
        case 'ogg':
          // For Ogg Vorbis, quality is -1 to 10 (-1 = lowest, 10 = highest)
          const oggQuality = Math.max(-1, Math.min(10, Math.floor((options.quality / 10) * 11) - 1));
          command.addOption('-q:a', oggQuality.toString());
          break;
        // For other formats, we'll use bitrate-based quality
      }
    }
    
    // Apply volume adjustment
    if (options.volume !== undefined) {
      command.audioFilters(`volume=${options.volume}`);
    }
    
    // Apply fade in
    if (options.fadeIn) {
      const fadeInDuration = typeof options.fadeIn === 'number' ? options.fadeIn : 3;
      command.audioFilters(`afade=t=in:st=0:d=${fadeInDuration}`);
    }
    
    // Apply fade out
    if (options.fadeOut && metadata.duration) {
      const fadeOutDuration = typeof options.fadeOut === 'number' ? options.fadeOut : 3;
      const startTime = metadata.duration - fadeOutDuration;
      if (startTime > 0) {
        command.audioFilters(`afade=t=out:st=${startTime}:d=${fadeOutDuration}`);
      }
    }
    
    // Apply normalization
    if (options.normalize) {
      command.audioFilters('loudnorm');
    }
    
    // Apply compression
    if (options.compress) {
      // Default compression settings
      const threshold = options.compressThreshold || -20;
      const ratio = options.compressRatio || 4;
      const attack = options.compressAttack || 20;
      const release = options.compressRelease || 250;
      
      command.audioFilters(`compand=attacks=${attack/1000}:decays=${release/1000}:points=-80/-80|-${threshold}/-${threshold}|0/-${threshold/ratio}|20/20:soft-knee=6:volume=2`);
    }
    
    // Apply equalization
    if (options.equalizer && Array.isArray(options.equalizer)) {
      options.equalizer.forEach(eq => {
        if (eq.frequency && eq.gain) {
          const width = eq.width || 1;
          command.audioFilters(`equalizer=f=${eq.frequency}:width_type=o:width=${width}:g=${eq.gain}`);
        }
      });
    }
    
    // Apply audio filters
    if (options.audioFilters && Array.isArray(options.audioFilters)) {
      options.audioFilters.forEach(filter => {
        command.audioFilters(filter);
      });
    }
    
    // Set output options
    if (options.outputOptions && Array.isArray(options.outputOptions)) {
      options.outputOptions.forEach(option => {
        command.addOption(option);
      });
    }
    
    // Remove video stream if present
    command.noVideo();
    
    return command;
  }
}

module.exports = AudioTransformer;
