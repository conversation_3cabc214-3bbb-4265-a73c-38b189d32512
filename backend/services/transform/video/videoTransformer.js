const BaseTransformer = require('../baseTransformer');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

/**
 * Video Transformer for handling video content transformations
 */
class VideoTransformer extends BaseTransformer {
  /**
   * Constructor for the VideoTransformer
   * @param {Object} options - Configuration options for the transformer
   */
  constructor(options = {}) {
    super(options);
    this.transformerType = 'video';
    this.supportedInputTypes = [
      'video/mp4',
      'video/webm',
      'video/ogg',
      'video/quicktime',
      'video/x-msvideo',
      'video/x-matroska'
    ];
    this.supportedOutputTypes = [
      'video/mp4',
      'video/webm',
      'video/ogg',
      'video/gif',
      'image/jpeg',
      'image/png'
    ];
    
    // Set default options
    this.options = {
      tempDir: options.tempDir || path.join(process.cwd(), 'temp'),
      ffmpegPath: options.ffmpegPath || null,
      ffprobePath: options.ffprobePath || null,
      ...options
    };
    
    // Configure ffmpeg paths if provided
    if (this.options.ffmpegPath) {
      ffmpeg.setFfmpegPath(this.options.ffmpegPath);
    }
    
    if (this.options.ffprobePath) {
      ffmpeg.setFfprobePath(this.options.ffprobePath);
    }
    
    // Ensure temp directory exists
    this.ensureTempDir();
  }

  /**
   * Ensure the temporary directory exists
   */
  async ensureTempDir() {
    try {
      await fs.mkdir(this.options.tempDir, { recursive: true });
    } catch (error) {
      console.error(`Error creating temp directory: ${error.message}`);
    }
  }

  /**
   * Transform video content from one format to another
   * @param {string} inputData - The video file path to be transformed
   * @param {string} inputType - The type of the input video
   * @param {string} outputType - The desired output type
   * @param {Object} options - Additional options for the transformation
   * @returns {Promise<Object>} - Transformation result with success flag, data, and optional error message
   */
  async transform(inputData, inputType, outputType, options = {}) {
    // Validate input
    const validation = this.validateInput(inputData, inputType, outputType);
    if (!validation.success) {
      return validation;
    }

    try {
      // Input must be a file path for ffmpeg
      if (typeof inputData !== 'string') {
        return {
          success: false,
          error: 'Input data must be a file path for video transformations'
        };
      }
      
      // Check if the input file exists
      try {
        await fs.access(inputData);
      } catch (error) {
        return {
          success: false,
          error: `Input file does not exist or is not accessible: ${inputData}`
        };
      }
      
      // Get video metadata
      const metadata = await this.getVideoMetadata(inputData);
      
      // Determine output format and file extension
      const { format, extension } = this.getFormatAndExtension(outputType);
      if (!format) {
        return {
          success: false,
          error: `Unsupported output type: ${outputType}`
        };
      }
      
      // Generate output file path
      const outputPath = options.outputPath || path.join(
        this.options.tempDir, 
        `${uuidv4()}.${extension}`
      );
      
      // Perform the transformation
      await this.processVideo(inputData, outputPath, format, options, metadata);
      
      // Get output file stats
      const stats = await fs.stat(outputPath);
      
      return {
        success: true,
        filePath: outputPath,
        metadata: {
          format,
          size: stats.size,
          ...await this.getVideoMetadata(outputPath)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `Video transformation failed: ${error.message}`
      };
    }
  }

  /**
   * Get video metadata using ffprobe
   * @param {string} filePath - Path to the video file
   * @returns {Promise<Object>} - Video metadata
   */
  getVideoMetadata(filePath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }
        
        // Extract relevant metadata
        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');
        
        resolve({
          duration: metadata.format.duration,
          size: metadata.format.size,
          bitrate: metadata.format.bit_rate,
          format: metadata.format.format_name,
          video: videoStream ? {
            codec: videoStream.codec_name,
            width: videoStream.width,
            height: videoStream.height,
            fps: eval(videoStream.r_frame_rate),
            bitrate: videoStream.bit_rate
          } : null,
          audio: audioStream ? {
            codec: audioStream.codec_name,
            channels: audioStream.channels,
            sampleRate: audioStream.sample_rate,
            bitrate: audioStream.bit_rate
          } : null
        });
      });
    });
  }

  /**
   * Get format and file extension from MIME type
   * @param {string} mimeType - The MIME type
   * @returns {Object} - Object containing format and extension
   */
  getFormatAndExtension(mimeType) {
    const formatMap = {
      'video/mp4': { format: 'mp4', extension: 'mp4' },
      'video/webm': { format: 'webm', extension: 'webm' },
      'video/ogg': { format: 'ogg', extension: 'ogv' },
      'video/gif': { format: 'gif', extension: 'gif' },
      'image/jpeg': { format: 'image2', extension: 'jpg' },
      'image/png': { format: 'image2', extension: 'png' }
    };
    
    return formatMap[mimeType] || { format: null, extension: null };
  }

  /**
   * Process video using ffmpeg
   * @param {string} inputPath - Path to the input video file
   * @param {string} outputPath - Path for the output file
   * @param {string} format - Output format
   * @param {Object} options - Processing options
   * @param {Object} metadata - Input video metadata
   * @returns {Promise<void>} - Promise that resolves when processing is complete
   */
  processVideo(inputPath, outputPath, format, options, metadata) {
    return new Promise((resolve, reject) => {
      // Initialize ffmpeg command
      let command = ffmpeg(inputPath);
      
      // Apply video transformations based on options
      command = this.applyVideoTransformations(command, options, metadata, format);
      
      // Set output format
      command.format(format);
      
      // Set output path
      command.output(outputPath);
      
      // Handle events
      command
        .on('start', (commandLine) => {
          console.log('FFmpeg process started:', commandLine);
        })
        .on('progress', (progress) => {
          console.log(`Processing: ${Math.floor(progress.percent)}% done`);
        })
        .on('end', () => {
          console.log('FFmpeg process completed');
          resolve();
        })
        .on('error', (err) => {
          console.error('FFmpeg error:', err);
          reject(err);
        });
      
      // Run the command
      command.run();
    });
  }

  /**
   * Apply video transformations based on options
   * @param {Object} command - FFmpeg command
   * @param {Object} options - Transformation options
   * @param {Object} metadata - Input video metadata
   * @param {string} format - Output format
   * @returns {Object} - Modified FFmpeg command
   */
  applyVideoTransformations(command, options, metadata, format) {
    // Set video codec
    if (options.videoCodec) {
      command.videoCodec(options.videoCodec);
    } else {
      // Set default codecs based on format
      switch (format) {
        case 'mp4':
          command.videoCodec('libx264');
          break;
        case 'webm':
          command.videoCodec('libvpx-vp9');
          break;
        case 'ogg':
          command.videoCodec('libtheora');
          break;
        case 'gif':
          // For GIF, we need special handling
          command
            .videoCodec('gif')
            .noAudio();
          break;
        case 'image2':
          // For image extraction, we don't need to set a codec
          command.noAudio();
          break;
      }
    }
    
    // Set audio codec if not outputting to an image format
    if (format !== 'image2' && format !== 'gif') {
      if (options.audioCodec) {
        command.audioCodec(options.audioCodec);
      } else {
        // Set default audio codecs based on format
        switch (format) {
          case 'mp4':
            command.audioCodec('aac');
            break;
          case 'webm':
            command.audioCodec('libopus');
            break;
          case 'ogg':
            command.audioCodec('libvorbis');
            break;
        }
      }
    } else {
      // No audio for image or GIF output
      command.noAudio();
    }
    
    // Set video bitrate
    if (options.videoBitrate) {
      command.videoBitrate(options.videoBitrate);
    }
    
    // Set audio bitrate
    if (options.audioBitrate && format !== 'image2' && format !== 'gif') {
      command.audioBitrate(options.audioBitrate);
    }
    
    // Resize video
    if (options.width || options.height) {
      const width = options.width || '?';
      const height = options.height || '?';
      command.size(`${width}x${height}`);
    }
    
    // Set frame rate
    if (options.fps) {
      command.fps(options.fps);
    }
    
    // Set duration/trim video
    if (options.duration) {
      command.duration(options.duration);
    }
    
    // Set start time
    if (options.startTime) {
      command.seekInput(options.startTime);
    }
    
    // Set end time (alternative to duration)
    if (options.endTime && !options.duration) {
      const duration = options.endTime - (options.startTime || 0);
      if (duration > 0) {
        command.duration(duration);
      }
    }
    
    // Extract specific frame for image output
    if (format === 'image2' && options.frameTime) {
      command
        .seekInput(options.frameTime)
        .frames(1);
    }
    
    // Set quality
    if (options.quality) {
      if (format === 'mp4' || format === 'webm') {
        // For video, lower CRF means higher quality (0-51 for x264, 0-63 for VP9)
        const crf = format === 'mp4' 
          ? Math.max(0, Math.min(51, 51 - Math.floor(options.quality / 2))) 
          : Math.max(0, Math.min(63, 63 - Math.floor(options.quality * 0.63)));
        command.addOption('-crf', crf.toString());
      } else if (format === 'image2') {
        // For images, higher quality is better (1-31 for JPEG)
        command.addOption('-qscale:v', Math.max(1, Math.min(31, Math.floor(31 - (options.quality * 0.3)))).toString());
      }
    }
    
    // Set pixel format
    if (options.pixelFormat) {
      command.addOption('-pix_fmt', options.pixelFormat);
    }
    
    // Add watermark
    if (options.watermark && options.watermarkPath) {
      command.complexFilter([
        `[0:v][1:v]overlay=main_w-overlay_w-10:main_h-overlay_h-10[out]`
      ], ['out']);
      
      // Add watermark input
      command.input(options.watermarkPath);
    }
    
    // Add text overlay
    if (options.text) {
      const { text, x = 10, y = 10, fontsize = 24, color = 'white', fontfile } = options.text;
      
      let filterString = `drawtext=text='${text.replace(/'/g, "\\'")}':x=${x}:y=${y}:fontsize=${fontsize}:fontcolor=${color}`;
      
      if (fontfile) {
        filterString += `:fontfile='${fontfile.replace(/'/g, "\\'")}'`;
      }
      
      command.videoFilters(filterString);
    }
    
    // Apply video filters
    if (options.videoFilters && Array.isArray(options.videoFilters)) {
      options.videoFilters.forEach(filter => {
        command.videoFilters(filter);
      });
    }
    
    // Apply audio filters
    if (options.audioFilters && Array.isArray(options.audioFilters) && format !== 'image2' && format !== 'gif') {
      options.audioFilters.forEach(filter => {
        command.audioFilters(filter);
      });
    }
    
    // Set output options
    if (options.outputOptions && Array.isArray(options.outputOptions)) {
      options.outputOptions.forEach(option => {
        command.addOption(option);
      });
    }
    
    return command;
  }
}

module.exports = VideoTransformer;
