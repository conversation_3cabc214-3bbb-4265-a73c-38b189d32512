const BaseTransformer = require('../baseTransformer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

/**
 * Image Transformer for handling image content transformations
 */
class ImageTransformer extends BaseTransformer {
  /**
   * Constructor for the ImageTransformer
   * @param {Object} options - Configuration options for the transformer
   */
  constructor(options = {}) {
    super(options);
    this.transformerType = 'image';
    this.supportedInputTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      'image/tiff',
      'image/bmp'
    ];
    this.supportedOutputTypes = [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/avif',
      'image/tiff'
    ];

    // Set default options
    this.options = {
      tempDir: options.tempDir || path.join(process.cwd(), 'temp'),
      quality: options.quality || 80,
      ...options
    };

    // Ensure temp directory exists
    this.ensureTempDir();
  }

  /**
   * Ensure the temporary directory exists
   */
  async ensureTempDir() {
    try {
      await fs.mkdir(this.options.tempDir, { recursive: true });
    } catch (error) {
      console.error(`Error creating temp directory: ${error.message}`);
    }
  }

  /**
   * Transform image content from one format to another
   * @param {Buffer|string} inputData - The image data to be transformed (Buffer or file path)
   * @param {string} inputType - The type of the input image
   * @param {string} outputType - The desired output type
   * @param {Object} options - Additional options for the transformation
   * @returns {Promise<Object>} - Transformation result with success flag, data, and optional error message
   */
  async transform(inputData, inputType, outputType, options = {}) {
    // Validate input
    const validation = this.validateInput(inputData, inputType, outputType);
    if (!validation.success) {
      return validation;
    }

    try {
      // Initialize Sharp with the input data
      let image;

      if (Buffer.isBuffer(inputData)) {
        // Input is a Buffer
        image = sharp(inputData);
      } else if (typeof inputData === 'string') {
        // Input is a file path
        image = sharp(inputData);
      } else {
        return {
          success: false,
          error: 'Input data must be a Buffer or a file path'
        };
      }

      // Get image metadata
      const metadata = await image.metadata();

      // Apply transformations
      image = await this.applyImageTransformations(image, options, metadata);

      // Convert to the desired output format
      const format = this.getFormatFromMimeType(outputType);
      if (!format) {
        return {
          success: false,
          error: `Unsupported output type: ${outputType}`
        };
      }

      // Set format-specific options
      const formatOptions = this.getFormatOptions(format, options);
      image = image[format](formatOptions);

      // Process the image and get the output buffer
      const outputBuffer = await image.toBuffer();

      // If saveToFile option is provided, save the output to a file
      let outputPath = null;
      if (options.saveToFile) {
        outputPath = typeof options.saveToFile === 'string'
          ? options.saveToFile
          : path.join(this.options.tempDir, `${uuidv4()}.${format}`);

        await fs.writeFile(outputPath, outputBuffer);
      }

      // Get the new metadata
      const newMetadata = await sharp(outputBuffer).metadata();

      return {
        success: true,
        data: outputBuffer,
        metadata: {
          format: newMetadata.format,
          width: newMetadata.width,
          height: newMetadata.height,
          channels: newMetadata.channels,
          size: outputBuffer.length
        },
        filePath: outputPath
      };
    } catch (error) {
      return {
        success: false,
        error: `Image transformation failed: ${error.message}`
      };
    }
  }

  /**
   * Apply image transformations based on options
   * @param {Object} image - Sharp image instance
   * @param {Object} options - Transformation options
   * @param {Object} metadata - Original image metadata
   * @returns {Object} - Transformed Sharp image instance
   */
  async applyImageTransformations(image, options, metadata) {
    // Resize image if width or height is specified
    if (options.width || options.height) {
      const resizeOptions = {
        width: options.width,
        height: options.height,
        fit: options.fit || 'cover',
        position: options.position || 'center',
        background: options.background || { r: 255, g: 255, b: 255, alpha: 1 }
      };

      image = image.resize(resizeOptions);
    }

    // Rotate image if angle is specified
    if (options.rotate) {
      const angle = parseInt(options.rotate);
      if (!isNaN(angle)) {
        const rotateOptions = {
          background: options.background || { r: 255, g: 255, b: 255, alpha: 1 }
        };
        image = image.rotate(angle, rotateOptions);
      }
    }

    // Flip image horizontally or vertically
    if (options.flip) {
      image = image.flip();
    }

    if (options.flop) {
      image = image.flop();
    }

    // Apply blur
    if (options.blur && !isNaN(parseFloat(options.blur))) {
      image = image.blur(parseFloat(options.blur));
    }

    // Apply sharpen
    if (options.sharpen) {
      const sharpenOptions = typeof options.sharpen === 'object'
        ? options.sharpen
        : {};
      image = image.sharpen(sharpenOptions);
    }

    // Adjust brightness, contrast, saturation
    if (options.brightness || options.contrast || options.saturation) {
      const modulate = {
        brightness: options.brightness !== undefined ? parseFloat(options.brightness) : undefined,
        saturation: options.saturation !== undefined ? parseFloat(options.saturation) : undefined
      };
      image = image.modulate(modulate);
    }

    // Apply grayscale
    if (options.grayscale) {
      image = image.grayscale();
    }

    // Apply tint
    if (options.tint) {
      image = image.tint(options.tint);
    }

    // Apply gamma correction
    if (options.gamma && !isNaN(parseFloat(options.gamma))) {
      image = image.gamma(parseFloat(options.gamma));
    }

    // Apply normalization
    if (options.normalize) {
      image = image.normalize();
    }

    // Apply threshold
    if (options.threshold !== undefined) {
      const threshold = parseInt(options.threshold);
      if (!isNaN(threshold)) {
        image = image.threshold(threshold);
      }
    }

    // Apply median filter
    if (options.median && !isNaN(parseInt(options.median))) {
      image = image.median(parseInt(options.median));
    }

    // Crop image
    if (options.crop && typeof options.crop === 'object') {
      const { left, top, width, height } = options.crop;
      if (left !== undefined && top !== undefined && width !== undefined && height !== undefined) {
        image = image.extract({ left, top, width, height });
      }
    }

    // Apply watermark
    if (options.watermark) {
      image = await this.applyWatermark(image, options.watermark);
    }

    // Apply border
    if (options.border) {
      const { width: borderWidth = 5, color = { r: 0, g: 0, b: 0, alpha: 1 } } =
        typeof options.border === 'object' ? options.border : {};

      // Create a larger canvas with the border color
      const newWidth = metadata.width + (borderWidth * 2);
      const newHeight = metadata.height + (borderWidth * 2);

      // We need to use the composite operation to apply a border
      const background = sharp({
        create: {
          width: newWidth,
          height: newHeight,
          channels: 4,
          background: color
        }
      });

      // This is a simplified approach - a full implementation would be more complex
      image = await background.composite([{
        input: await image.toBuffer(),
        left: borderWidth,
        top: borderWidth
      }]).toBuffer();

      // Reinitialize sharp with the new image buffer
      image = sharp(image);
    }

    return image;
  }

  /**
   * Get the format string from a MIME type
   * @param {string} mimeType - The MIME type
   * @returns {string|null} - The format string or null if not supported
   */
  getFormatFromMimeType(mimeType) {
    const formatMap = {
      'image/jpeg': 'jpeg',
      'image/png': 'png',
      'image/webp': 'webp',
      'image/avif': 'avif',
      'image/tiff': 'tiff'
    };

    return formatMap[mimeType] || null;
  }

  /**
   * Get format-specific options
   * @param {string} format - The output format
   * @param {Object} options - User-provided options
   * @returns {Object} - Format-specific options
   */
  getFormatOptions(format, options) {
    const quality = options.quality || this.options.quality;

    switch (format) {
      case 'jpeg':
        return {
          quality,
          progressive: options.progressive !== false,
          chromaSubsampling: options.chromaSubsampling || '4:2:0',
          trellisQuantisation: options.trellisQuantisation !== false
        };
      case 'png':
        return {
          progressive: options.progressive !== false,
          compressionLevel: options.compressionLevel || 6,
          adaptiveFiltering: options.adaptiveFiltering !== false
        };
      case 'webp':
        return {
          quality,
          alphaQuality: options.alphaQuality || 100,
          lossless: options.lossless === true,
          nearLossless: options.nearLossless === true
        };
      case 'avif':
        return {
          quality,
          lossless: options.lossless === true,
          speed: options.speed || 5
        };
      case 'tiff':
        return {
          quality,
          compression: options.compression || 'jpeg',
          predictor: options.predictor || 'horizontal',
          pyramid: options.pyramid === true,
          tile: options.tile === true,
          tileWidth: options.tileWidth || 256,
          tileHeight: options.tileHeight || 256,
          xres: options.xres || 1.0,
          yres: options.yres || 1.0
        };
      default:
        return {};
    }
  }

  /**
   * Apply watermark to image
   * @param {Object} image - Sharp image instance
   * @param {Object} options - Watermark options
   * @returns {Promise<Object>} - Watermarked Sharp image instance
   */
  async applyWatermark(image, options) {
    if (!options.text && !options.image) {
      return image;
    }

    try {
      if (options.text) {
        // Text watermark
        const textOptions = {
          text: options.text,
          font: options.font || 'Arial',
          fontSize: options.fontSize || 24,
          fontColor: options.fontColor || '#FFFFFF',
          opacity: options.opacity || 0.7,
          position: options.position || 'bottom-right'
        };

        // Create text overlay using SVG
        const textSvg = `
          <svg width="${textOptions.text.length * textOptions.fontSize * 0.6}" height="${textOptions.fontSize + 10}">
            <text x="5" y="${textOptions.fontSize}"
                  font-family="${textOptions.font}"
                  font-size="${textOptions.fontSize}"
                  fill="${textOptions.fontColor}"
                  opacity="${textOptions.opacity}">
              ${textOptions.text}
            </text>
          </svg>
        `;

        const textBuffer = Buffer.from(textSvg);
        const imageMetadata = await image.metadata();
        const position = this.calculateWatermarkPosition(imageMetadata, textOptions.position, {
          width: textOptions.text.length * textOptions.fontSize * 0.6,
          height: textOptions.fontSize + 10
        });

        image = image.composite([{
          input: textBuffer,
          left: position.left,
          top: position.top
        }]);
      }

      if (options.image && typeof options.image === 'string') {
        // Image watermark
        const watermarkImage = sharp(options.image);
        const watermarkMetadata = await watermarkImage.metadata();
        const imageMetadata = await image.metadata();

        // Resize watermark if needed
        const maxWatermarkSize = Math.min(imageMetadata.width * 0.3, imageMetadata.height * 0.3);
        if (watermarkMetadata.width > maxWatermarkSize || watermarkMetadata.height > maxWatermarkSize) {
          watermarkImage.resize({
            width: maxWatermarkSize,
            height: maxWatermarkSize,
            fit: 'inside'
          });
        }

        // Apply opacity
        if (options.opacity && options.opacity < 1) {
          watermarkImage.composite([{
            input: Buffer.from([255, 255, 255, Math.round(255 * options.opacity)]),
            raw: { width: 1, height: 1, channels: 4 },
            tile: true,
            blend: 'dest-in'
          }]);
        }

        const finalWatermarkMetadata = await watermarkImage.metadata();
        const position = this.calculateWatermarkPosition(imageMetadata, options.position || 'bottom-right', finalWatermarkMetadata);

        image = image.composite([{
          input: await watermarkImage.toBuffer(),
          left: position.left,
          top: position.top
        }]);
      }

      return image;
    } catch (error) {
      console.error('Error applying watermark:', error);
      return image; // Return original image if watermark fails
    }
  }

  /**
   * Calculate watermark position
   * @param {Object} imageMetadata - Image metadata
   * @param {string} position - Position string
   * @param {Object} watermarkSize - Watermark dimensions
   * @returns {Object} - Position coordinates
   */
  calculateWatermarkPosition(imageMetadata, position, watermarkSize) {
    const margin = 20;
    const imageWidth = imageMetadata.width;
    const imageHeight = imageMetadata.height;
    const watermarkWidth = watermarkSize.width;
    const watermarkHeight = watermarkSize.height;

    switch (position) {
      case 'top-left':
        return { left: margin, top: margin };
      case 'top-right':
        return { left: imageWidth - watermarkWidth - margin, top: margin };
      case 'bottom-left':
        return { left: margin, top: imageHeight - watermarkHeight - margin };
      case 'bottom-right':
        return { left: imageWidth - watermarkWidth - margin, top: imageHeight - watermarkHeight - margin };
      case 'center':
        return {
          left: Math.round((imageWidth - watermarkWidth) / 2),
          top: Math.round((imageHeight - watermarkHeight) / 2)
        };
      default:
        return { left: imageWidth - watermarkWidth - margin, top: imageHeight - watermarkHeight - margin };
    }
  }

  /**
   * Optimize image for specific platform
   * @param {Buffer|string} inputData - The image data to be optimized
   * @param {string} inputType - The type of the input image
   * @param {string} platform - Target platform (youtube, instagram, linkedin, etc.)
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Optimization result
   */
  async optimizeForPlatform(inputData, inputType, platform, options = {}) {
    const platformSpecs = this.getPlatformSpecs(platform);
    if (!platformSpecs) {
      return {
        success: false,
        error: `Unsupported platform: ${platform}`
      };
    }

    const optimizationOptions = {
      ...platformSpecs,
      ...options
    };

    return await this.transform(inputData, inputType, platformSpecs.format, optimizationOptions);
  }

  /**
   * Get platform-specific image specifications
   * @param {string} platform - Platform name
   * @returns {Object|null} - Platform specifications
   */
  getPlatformSpecs(platform) {
    const specs = {
      youtube: {
        thumbnail: {
          width: 1280,
          height: 720,
          format: 'image/jpeg',
          quality: 90,
          fit: 'cover'
        },
        banner: {
          width: 2560,
          height: 1440,
          format: 'image/jpeg',
          quality: 90,
          fit: 'cover'
        }
      },
      instagram: {
        post: {
          width: 1080,
          height: 1080,
          format: 'image/jpeg',
          quality: 85,
          fit: 'cover'
        },
        story: {
          width: 1080,
          height: 1920,
          format: 'image/jpeg',
          quality: 85,
          fit: 'cover'
        },
        reel: {
          width: 1080,
          height: 1920,
          format: 'image/jpeg',
          quality: 85,
          fit: 'cover'
        }
      },
      linkedin: {
        post: {
          width: 1200,
          height: 627,
          format: 'image/jpeg',
          quality: 85,
          fit: 'cover'
        },
        article: {
          width: 1200,
          height: 627,
          format: 'image/jpeg',
          quality: 85,
          fit: 'cover'
        },
        banner: {
          width: 1584,
          height: 396,
          format: 'image/jpeg',
          quality: 90,
          fit: 'cover'
        }
      },
      twitter: {
        post: {
          width: 1200,
          height: 675,
          format: 'image/jpeg',
          quality: 85,
          fit: 'cover'
        },
        header: {
          width: 1500,
          height: 500,
          format: 'image/jpeg',
          quality: 90,
          fit: 'cover'
        }
      },
      facebook: {
        post: {
          width: 1200,
          height: 630,
          format: 'image/jpeg',
          quality: 85,
          fit: 'cover'
        },
        cover: {
          width: 820,
          height: 312,
          format: 'image/jpeg',
          quality: 90,
          fit: 'cover'
        }
      }
    };

    return specs[platform.toLowerCase()] || null;
  }

  /**
   * Generate multiple sizes for responsive images
   * @param {Buffer|string} inputData - The image data
   * @param {string} inputType - The type of the input image
   * @param {Array} sizes - Array of size objects {width, height, suffix}
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Generation result with multiple sizes
   */
  async generateResponsiveSizes(inputData, inputType, sizes, options = {}) {
    try {
      const results = [];

      for (const size of sizes) {
        const sizeOptions = {
          width: size.width,
          height: size.height,
          fit: options.fit || 'cover',
          quality: options.quality || 85,
          saveToFile: options.saveToFile
        };

        const result = await this.transform(inputData, inputType, options.outputFormat || 'image/webp', sizeOptions);

        if (result.success) {
          results.push({
            size: size.suffix || `${size.width}x${size.height}`,
            width: size.width,
            height: size.height,
            data: result.data,
            filePath: result.filePath,
            metadata: result.metadata
          });
        }
      }

      return {
        success: true,
        sizes: results
      };

    } catch (error) {
      return {
        success: false,
        error: `Failed to generate responsive sizes: ${error.message}`
      };
    }
  }
}

module.exports = ImageTransformer;
