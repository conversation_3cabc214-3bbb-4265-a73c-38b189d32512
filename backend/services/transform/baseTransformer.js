/**
 * Base Transformer class that all specific transformers will extend
 */
class BaseTransformer {
  /**
   * Constructor for the BaseTransformer
   * @param {Object} options - Configuration options for the transformer
   */
  constructor(options = {}) {
    this.options = options;
    this.transformerType = 'base';
    this.supportedInputTypes = [];
    this.supportedOutputTypes = [];
  }

  /**
   * Validate that the input type is supported by this transformer
   * @param {string} inputType - The type of the input content
   * @returns {boolean} - Whether the input type is supported
   */
  supportsInputType(inputType) {
    return this.supportedInputTypes.includes(inputType);
  }

  /**
   * Validate that the output type is supported by this transformer
   * @param {string} outputType - The desired output type
   * @returns {boolean} - Whether the output type is supported
   */
  supportsOutputType(outputType) {
    return this.supportedOutputTypes.includes(outputType);
  }

  /**
   * Validate input data before transformation
   * @param {any} inputData - The data to be transformed
   * @param {string} inputType - The type of the input data
   * @param {string} outputType - The desired output type
   * @returns {Object} - Validation result with success flag and optional error message
   */
  validateInput(inputData, inputType, outputType) {
    // Check if input type is supported
    if (!this.supportsInputType(inputType)) {
      return {
        success: false,
        error: `Unsupported input type: ${inputType}. Supported types: ${this.supportedInputTypes.join(', ')}`
      };
    }

    // Check if output type is supported
    if (!this.supportsOutputType(outputType)) {
      return {
        success: false,
        error: `Unsupported output type: ${outputType}. Supported types: ${this.supportedOutputTypes.join(', ')}`
      };
    }

    // Check if input data is provided
    if (inputData === undefined || inputData === null) {
      return {
        success: false,
        error: 'Input data is required'
      };
    }

    return { success: true };
  }

  /**
   * Transform the input data to the desired output type
   * This method should be overridden by subclasses
   * @param {any} inputData - The data to be transformed
   * @param {string} inputType - The type of the input data
   * @param {string} outputType - The desired output type
   * @param {Object} options - Additional options for the transformation
   * @returns {Promise<Object>} - Transformation result with success flag, data, and optional error message
   */
  async transform(inputData, inputType, outputType, options = {}) {
    // Validate input
    const validation = this.validateInput(inputData, inputType, outputType);
    if (!validation.success) {
      return validation;
    }

    // This method should be overridden by subclasses
    return {
      success: false,
      error: 'Transform method not implemented'
    };
  }

  /**
   * Get information about the transformer
   * @returns {Object} - Information about the transformer
   */
  getInfo() {
    return {
      type: this.transformerType,
      supportedInputTypes: this.supportedInputTypes,
      supportedOutputTypes: this.supportedOutputTypes,
      options: this.options
    };
  }
}

module.exports = BaseTransformer;
