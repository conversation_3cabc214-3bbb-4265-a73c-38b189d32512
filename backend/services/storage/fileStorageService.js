const supabase = require('../../config/supabase');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');

/**
 * File Storage Service for handling file uploads, storage, and management
 * Integrates with Supabase Storage for cloud storage
 */
class FileStorageService {
  constructor(options = {}) {
    this.options = {
      bucketName: options.bucketName || 'content-files',
      maxFileSize: options.maxFileSize || 100 * 1024 * 1024, // 100MB
      allowedMimeTypes: options.allowedMimeTypes || [
        // Text types
        'text/plain', 'text/markdown', 'text/html', 'text/csv', 'application/json',
        'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        // Image types
        'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/tiff', 'image/bmp',
        // Video types
        'video/mp4', 'video/webm', 'video/ogg', 'video/quicktime', 'video/x-msvideo', 'video/x-matroska',
        // Audio types
        'audio/mpeg', 'audio/mp4', 'audio/ogg', 'audio/wav', 'audio/webm', 'audio/flac', 'audio/aac'
      ],
      tempDir: options.tempDir || path.join(process.cwd(), 'temp'),
      ...options
    };
    
    this.ensureTempDir();
  }

  /**
   * Ensure the temporary directory exists
   */
  async ensureTempDir() {
    try {
      await fs.mkdir(this.options.tempDir, { recursive: true });
    } catch (error) {
      console.error(`Error creating temp directory: ${error.message}`);
    }
  }

  /**
   * Validate file before processing
   * @param {Object} file - File object with name, size, mimetype
   * @returns {Object} - Validation result
   */
  validateFile(file) {
    const errors = [];

    // Check file size
    if (file.size > this.options.maxFileSize) {
      errors.push(`File size ${this.formatFileSize(file.size)} exceeds maximum allowed size of ${this.formatFileSize(this.options.maxFileSize)}`);
    }

    // Check MIME type
    if (!this.options.allowedMimeTypes.includes(file.mimetype)) {
      errors.push(`File type ${file.mimetype} is not allowed`);
    }

    // Check for malicious file extensions
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.vbs', '.js', '.jar'];
    const fileExtension = path.extname(file.name).toLowerCase();
    if (dangerousExtensions.includes(fileExtension)) {
      errors.push(`File extension ${fileExtension} is not allowed for security reasons`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Sanitize filename to prevent path traversal and other security issues
   * @param {string} filename - Original filename
   * @returns {string} - Sanitized filename
   */
  sanitizeFilename(filename) {
    // Remove path separators and other dangerous characters
    let sanitized = filename.replace(/[\/\\:*?"<>|]/g, '_');
    
    // Remove leading dots and spaces
    sanitized = sanitized.replace(/^[.\s]+/, '');
    
    // Limit length
    if (sanitized.length > 255) {
      const ext = path.extname(sanitized);
      const name = path.basename(sanitized, ext);
      sanitized = name.substring(0, 255 - ext.length) + ext;
    }
    
    // Ensure it's not empty
    if (!sanitized || sanitized === '') {
      sanitized = 'unnamed_file';
    }
    
    return sanitized;
  }

  /**
   * Generate a unique file path for storage
   * @param {string} originalFilename - Original filename
   * @param {string} userId - User ID for organizing files
   * @returns {string} - Unique file path
   */
  generateFilePath(originalFilename, userId) {
    const sanitizedFilename = this.sanitizeFilename(originalFilename);
    const fileExtension = path.extname(sanitizedFilename);
    const baseName = path.basename(sanitizedFilename, fileExtension);
    const uniqueId = uuidv4();
    const timestamp = Date.now();
    
    // Create a hierarchical path: userId/year/month/uniqueId-filename
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    
    return `${userId}/${year}/${month}/${uniqueId}-${timestamp}-${baseName}${fileExtension}`;
  }

  /**
   * Calculate file hash for deduplication
   * @param {Buffer} fileBuffer - File buffer
   * @returns {string} - SHA-256 hash
   */
  calculateFileHash(fileBuffer) {
    return crypto.createHash('sha256').update(fileBuffer).digest('hex');
  }

  /**
   * Format file size for display
   * @param {number} bytes - File size in bytes
   * @returns {string} - Formatted file size
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

module.exports = FileStorageService;
