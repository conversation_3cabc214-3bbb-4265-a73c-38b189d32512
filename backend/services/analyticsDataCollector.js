const supabase = require('../config/supabase');
const youtubeAnalytics = require('./platformAPIs/youtubeAnalytics');
const instagramInsights = require('./platformAPIs/instagramInsights');
const linkedinAnalytics = require('./platformAPIs/linkedinAnalytics');

class AnalyticsDataCollector {
  constructor() {
    this.platforms = {
      youtube: youtubeAnalytics,
      instagram: instagramInsights,
      linkedin: linkedinAnalytics
    };
  }

  /**
   * Collect analytics data for a specific published content
   * @param {string} publishedContentId - Published content ID
   * @returns {Object} Collected analytics data
   */
  async collectAnalyticsForContent(publishedContentId) {
    try {
      // Get published content details
      const { data: publishedContent, error: contentError } = await supabase
        .from('published_content')
        .select('*')
        .eq('id', publishedContentId)
        .single();

      if (contentError) {
        throw new Error(`Failed to fetch published content: ${contentError.message}`);
      }

      if (!publishedContent) {
        throw new Error('Published content not found');
      }

      // Get user's platform credentials
      const { data: credentials, error: credError } = await supabase
        .from('platform_credentials')
        .select('*')
        .eq('user_id', publishedContent.user_id)
        .eq('platform', publishedContent.platform)
        .eq('status', 'active')
        .single();

      if (credError || !credentials) {
        console.warn(`No active credentials found for ${publishedContent.platform} for user ${publishedContent.user_id}`);
        return this.getMockAnalyticsData(publishedContent.platform);
      }

      // Collect analytics based on platform
      const analyticsData = await this.collectPlatformAnalytics(
        publishedContent.platform,
        credentials.access_token,
        publishedContent.platform_post_id,
        publishedContent.publication_date
      );

      // Store analytics data
      const { data: storedAnalytics, error: storeError } = await supabase
        .from('analytics')
        .upsert({
          published_content_id: publishedContentId,
          platform: publishedContent.platform,
          metrics: analyticsData,
          date: new Date().toISOString().split('T')[0],
          collected_at: new Date().toISOString()
        }, {
          onConflict: 'published_content_id,date'
        })
        .select()
        .single();

      if (storeError) {
        console.error('Failed to store analytics data:', storeError);
        throw new Error(`Failed to store analytics data: ${storeError.message}`);
      }

      return {
        success: true,
        publishedContentId,
        platform: publishedContent.platform,
        analytics: analyticsData,
        storedAt: storedAnalytics.collected_at
      };

    } catch (error) {
      console.error('Analytics collection error:', error);
      return {
        success: false,
        publishedContentId,
        error: error.message
      };
    }
  }

  /**
   * Collect analytics for a specific platform
   * @param {string} platform - Platform name
   * @param {string} accessToken - Platform access token
   * @param {string} postId - Platform-specific post ID
   * @param {string} publicationDate - Publication date
   * @returns {Object} Analytics data
   */
  async collectPlatformAnalytics(platform, accessToken, postId, publicationDate) {
    const platformService = this.platforms[platform];
    
    if (!platformService) {
      throw new Error(`Unsupported platform: ${platform}`);
    }

    try {
      switch (platform) {
        case 'youtube':
          return await this.collectYouTubeAnalytics(platformService, accessToken, postId, publicationDate);
        
        case 'instagram':
          return await this.collectInstagramAnalytics(platformService, accessToken, postId);
        
        case 'linkedin':
          return await this.collectLinkedInAnalytics(platformService, accessToken, postId);
        
        default:
          throw new Error(`Analytics collection not implemented for platform: ${platform}`);
      }
    } catch (error) {
      console.error(`${platform} analytics collection error:`, error);
      // Return mock data if real collection fails
      return this.getMockAnalyticsData(platform);
    }
  }

  /**
   * Collect YouTube analytics
   */
  async collectYouTubeAnalytics(service, accessToken, videoId, publicationDate) {
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = publicationDate.split('T')[0];

    const analytics = await service.getVideoAnalytics(accessToken, videoId, startDate, endDate);
    const videoDetails = await service.getVideoDetails(accessToken, videoId);

    return {
      views: analytics.views,
      likes: analytics.likes,
      dislikes: analytics.dislikes,
      comments: analytics.comments,
      shares: analytics.shares,
      watchTime: analytics.watchTime,
      averageViewDuration: analytics.averageViewDuration,
      title: videoDetails.title,
      thumbnail: videoDetails.thumbnail,
      engagementRate: analytics.views > 0 ? 
        ((analytics.likes + analytics.comments + analytics.shares) / analytics.views * 100).toFixed(2) : 0
    };
  }

  /**
   * Collect Instagram analytics
   */
  async collectInstagramAnalytics(service, accessToken, mediaId) {
    const insights = await service.getMediaInsights(accessToken, mediaId);

    return {
      impressions: insights.impressions,
      reach: insights.reach,
      likes: insights.likes,
      comments: insights.comments,
      shares: insights.shares,
      saves: insights.saves,
      profileVisits: insights.profileVisits,
      follows: insights.follows,
      mediaType: insights.mediaType,
      engagementRate: insights.reach > 0 ? 
        ((insights.likes + insights.comments + insights.shares + insights.saves) / insights.reach * 100).toFixed(2) : 0
    };
  }

  /**
   * Collect LinkedIn analytics
   */
  async collectLinkedInAnalytics(service, accessToken, postId) {
    const analytics = await service.getPostAnalytics(accessToken, postId);

    return {
      impressions: analytics.impressions,
      uniqueImpressions: analytics.uniqueImpressions,
      likes: analytics.likes,
      comments: analytics.comments,
      shares: analytics.shares,
      clicks: analytics.clicks,
      engagement: analytics.engagement,
      clickThroughRate: analytics.impressions > 0 ? 
        (analytics.clicks / analytics.impressions * 100).toFixed(2) : 0,
      engagementRate: analytics.impressions > 0 ? 
        (analytics.engagement / analytics.impressions * 100).toFixed(2) : 0
    };
  }

  /**
   * Collect analytics for all published content of a user
   * @param {string} userId - User ID
   * @param {number} daysBack - Number of days to look back
   * @returns {Array} Collection results
   */
  async collectAllUserAnalytics(userId, daysBack = 7) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysBack);

      // Get published content from the last N days
      const { data: publishedContent, error: contentError } = await supabase
        .from('published_content')
        .select('id, platform, platform_post_id, publication_date')
        .eq('user_id', userId)
        .eq('status', 'published')
        .gte('publication_date', cutoffDate.toISOString())
        .order('publication_date', { ascending: false });

      if (contentError) {
        throw new Error(`Failed to fetch published content: ${contentError.message}`);
      }

      if (!publishedContent || publishedContent.length === 0) {
        return {
          success: true,
          message: 'No published content found for analytics collection',
          results: []
        };
      }

      // Collect analytics for each piece of content
      const collectionPromises = publishedContent.map(content => 
        this.collectAnalyticsForContent(content.id)
      );

      const results = await Promise.all(collectionPromises);

      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      return {
        success: true,
        message: `Analytics collection completed. ${successful} successful, ${failed} failed.`,
        results,
        summary: {
          total: results.length,
          successful,
          failed
        }
      };

    } catch (error) {
      console.error('Bulk analytics collection error:', error);
      return {
        success: false,
        error: error.message,
        results: []
      };
    }
  }

  /**
   * Collect analytics for all users (for scheduled jobs)
   * @param {number} daysBack - Number of days to look back
   * @returns {Object} Collection summary
   */
  async collectAllAnalytics(daysBack = 1) {
    try {
      // Get all users with published content
      const { data: users, error: usersError } = await supabase
        .from('published_content')
        .select('user_id')
        .eq('status', 'published')
        .gte('publication_date', new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000).toISOString());

      if (usersError) {
        throw new Error(`Failed to fetch users: ${usersError.message}`);
      }

      // Get unique user IDs
      const uniqueUserIds = [...new Set(users.map(u => u.user_id))];

      console.log(`Starting analytics collection for ${uniqueUserIds.length} users`);

      // Collect analytics for each user
      const userPromises = uniqueUserIds.map(userId => 
        this.collectAllUserAnalytics(userId, daysBack)
      );

      const userResults = await Promise.all(userPromises);

      const totalSuccessful = userResults.reduce((sum, result) => 
        sum + (result.summary?.successful || 0), 0);
      const totalFailed = userResults.reduce((sum, result) => 
        sum + (result.summary?.failed || 0), 0);

      return {
        success: true,
        message: `Analytics collection completed for ${uniqueUserIds.length} users`,
        usersProcessed: uniqueUserIds.length,
        totalAnalyticsCollected: totalSuccessful,
        totalFailed,
        userResults
      };

    } catch (error) {
      console.error('Global analytics collection error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get mock analytics data for development
   * @param {string} platform - Platform name
   * @returns {Object} Mock analytics data
   */
  getMockAnalyticsData(platform) {
    const baseMetrics = {
      views: Math.floor(Math.random() * 10000) + 1000,
      likes: Math.floor(Math.random() * 500) + 50,
      comments: Math.floor(Math.random() * 100) + 10,
      shares: Math.floor(Math.random() * 50) + 5
    };

    switch (platform) {
      case 'youtube':
        return {
          ...baseMetrics,
          dislikes: Math.floor(Math.random() * 20) + 2,
          watchTime: Math.floor(Math.random() * 50000) + 5000,
          averageViewDuration: Math.floor(Math.random() * 300) + 60,
          engagementRate: (Math.random() * 5 + 1).toFixed(2)
        };

      case 'instagram':
        return {
          impressions: Math.floor(Math.random() * 5000) + 500,
          reach: Math.floor(Math.random() * 3000) + 300,
          likes: baseMetrics.likes,
          comments: baseMetrics.comments,
          shares: baseMetrics.shares,
          saves: Math.floor(Math.random() * 100) + 10,
          profileVisits: Math.floor(Math.random() * 50) + 5,
          follows: Math.floor(Math.random() * 10) + 1,
          engagementRate: (Math.random() * 8 + 2).toFixed(2)
        };

      case 'linkedin':
        return {
          impressions: Math.floor(Math.random() * 2000) + 200,
          uniqueImpressions: Math.floor(Math.random() * 1500) + 150,
          likes: baseMetrics.likes,
          comments: baseMetrics.comments,
          shares: baseMetrics.shares,
          clicks: Math.floor(Math.random() * 100) + 10,
          clickThroughRate: (Math.random() * 3 + 1).toFixed(2),
          engagementRate: (Math.random() * 4 + 1).toFixed(2)
        };

      default:
        return baseMetrics;
    }
  }

  /**
   * Validate platform credentials
   * @param {string} platform - Platform name
   * @param {string} accessToken - Access token
   * @returns {boolean} Validation result
   */
  async validatePlatformCredentials(platform, accessToken) {
    const platformService = this.platforms[platform];
    
    if (!platformService || !platformService.validateToken) {
      return false;
    }

    try {
      return await platformService.validateToken(accessToken);
    } catch (error) {
      console.error(`Token validation error for ${platform}:`, error);
      return false;
    }
  }
}

module.exports = new AnalyticsDataCollector();
