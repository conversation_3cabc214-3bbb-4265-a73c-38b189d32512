# ContentForge Services

This directory contains the core services for ContentForge's content processing and transformation capabilities.

## 📁 Directory Structure

```
services/
├── storage/              # File storage and management
│   └── fileStorageService.js
├── queue/               # Background processing queues
│   ├── fileProcessingQueue.js
│   └── queueManager.js
├── transform/           # Content transformation services
│   ├── audio/
│   ├── image/
│   ├── text/
│   ├── video/
│   └── index.js
├── platforms/           # Platform-specific services
│   ├── instagram/
│   ├── linkedin/
│   ├── youtube/
│   └── platformServiceFactory.js
└── n8n/                # Workflow orchestration
    └── n8nService.js
```

## 🚀 Key Features

### File Storage Service
- **Secure file uploads** with validation and sanitization
- **Supabase Storage integration** for cloud storage
- **Duplicate detection** using file hashing
- **Metadata extraction** for images and videos
- **Storage usage tracking** and analytics

### File Processing Queue
- **Asynchronous processing** of uploaded files
- **Automatic thumbnail generation** for images and videos
- **Metadata extraction** and enhancement
- **File optimization** and compression
- **Retry mechanism** with exponential backoff

### Content Transformation

#### Text Processing
- **Platform optimization** for Twitter, Instagram, LinkedIn, etc.
- **Content summarization** using extractive algorithms
- **Hashtag extraction and suggestion** based on content analysis
- **Quality scoring** with recommendations for improvement
- **Sentiment analysis** and readability scoring
- **SEO optimization** features

#### Image Processing
- **Platform-specific sizing** (YouTube thumbnails, Instagram posts, etc.)
- **Watermark application** (text and image watermarks)
- **Format conversion** (JPEG, PNG, WebP, AVIF)
- **Responsive image generation** for multiple screen sizes
- **Advanced image transformations** (resize, crop, filters)

#### Video Processing
- **Thumbnail extraction** from video files
- **Format conversion** and compression
- **Video metadata extraction**
- **Basic video editing** (trimming, resizing)

## 📋 API Usage Examples

### File Upload
```javascript
// Upload a single file
POST /api/files/upload
Content-Type: multipart/form-data

{
  "file": <file>,
  "checkDuplicates": true,
  "autoProcess": true
}
```

### Content Optimization
```javascript
// Optimize content for a specific platform
POST /api/transform/optimize
{
  "content": "Your content here...",
  "platform": "twitter",
  "contentType": "text",
  "options": {
    "generateHashtags": true,
    "maxHashtags": 5
  }
}
```

### Content Summarization
```javascript
// Generate a summary of long content
POST /api/transform/summary
{
  "content": "Long article content...",
  "maxLength": 200
}
```

### Hashtag Extraction
```javascript
// Extract and suggest hashtags
POST /api/transform/hashtags
{
  "content": "Your content here...",
  "platform": "instagram",
  "maxHashtags": 10
}
```

### Quality Analysis
```javascript
// Analyze content quality
POST /api/transform/quality
{
  "content": "Your content here...",
  "platform": "linkedin"
}
```

## 🎯 Platform Specifications

### Supported Platforms
- **YouTube**: Thumbnails (1280x720), Banners (2560x1440)
- **Instagram**: Posts (1080x1080), Stories (1080x1920), Reels (1080x1920)
- **LinkedIn**: Posts (1200x627), Articles (1200x627), Banners (1584x396)
- **Twitter**: Posts (1200x675), Headers (1500x500)
- **Facebook**: Posts (1200x630), Covers (820x312)
- **TikTok**: Videos (1080x1920)

### Character Limits
- **Twitter**: 280 characters
- **Instagram**: 2,200 characters (125 optimal for captions)
- **LinkedIn**: 3,000 characters (125,000 for articles)
- **YouTube**: 100 characters (titles), 5,000 characters (descriptions)
- **Facebook**: 63,206 characters (40 optimal)
- **TikTok**: 2,200 characters

## 🔧 Configuration

### Environment Variables
```bash
# File Storage
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Processing Queue
QUEUE_PROCESSING_INTERVAL=10000  # 10 seconds
QUEUE_MAX_RETRIES=3
QUEUE_BATCH_SIZE=10

# File Limits
MAX_FILE_SIZE=104857600  # 100MB
ALLOWED_MIME_TYPES=image/*,video/*,audio/*,text/*
```

### Queue Configuration
```javascript
const queueManager = require('./services/queue/queueManager');

// Initialize and start queues
await queueManager.initialize();
queueManager.startAll();

// Get queue status
const status = await queueManager.getStatus();
console.log(status);
```

## 🧪 Testing

### Running Tests
```bash
# Run all service tests
npm test

# Run specific test suites
npm test -- --testNamePattern="File Storage"
npm test -- --testNamePattern="Content Optimizer"
npm test -- --testNamePattern="Image Transformer"
```

### Test Coverage
- File storage validation and sanitization
- Content optimization for all platforms
- Image transformation and watermarking
- Text analysis and quality scoring
- Queue processing and error handling

## 🔒 Security Features

### File Upload Security
- **File type validation** based on MIME type and extension
- **File size limits** to prevent abuse
- **Filename sanitization** to prevent path traversal
- **Virus scanning** (placeholder for integration)
- **Content validation** for malicious payloads

### Data Protection
- **User isolation** - files are scoped to user accounts
- **Secure file paths** with UUID-based naming
- **Temporary file cleanup** after processing
- **Access control** via Row Level Security (RLS)

## 📊 Monitoring and Analytics

### Queue Monitoring
```javascript
// Get queue statistics
GET /api/files/queue/stats

// Response
{
  "stats": {
    "pending": 5,
    "processing": 2,
    "completed": 150,
    "failed": 3,
    "total": 160
  }
}
```

### Storage Analytics
```javascript
// Get storage usage
GET /api/files/usage/stats

// Response
{
  "usage": {
    "totalSize": **********,
    "totalSizeFormatted": "1 GB",
    "fileCount": 250,
    "typeBreakdown": [
      { "type": "image", "size": *********, "sizeFormatted": "512 MB" },
      { "type": "video", "size": *********, "sizeFormatted": "512 MB" }
    ]
  }
}
```

## 🚀 Performance Optimization

### Image Processing
- **Progressive JPEG** encoding for faster loading
- **WebP format** for modern browsers
- **Responsive image sets** for different screen sizes
- **Lazy loading** support with placeholder generation

### Queue Processing
- **Batch processing** to reduce database load
- **Priority queues** for urgent tasks
- **Concurrent processing** with configurable limits
- **Graceful degradation** on failures

### Caching Strategy
- **File metadata caching** in database
- **Processed image caching** in storage
- **Content analysis caching** for repeated requests
- **Platform specification caching** in memory

## 🔄 Future Enhancements

### Planned Features
- **AI-powered content generation** using GPT models
- **Advanced video editing** capabilities
- **Real-time collaboration** on content
- **Content scheduling** and automation
- **A/B testing** for content variations
- **Advanced analytics** and insights

### Integration Roadmap
- **External AI services** (OpenAI, Anthropic)
- **Advanced image processing** (background removal, object detection)
- **Video transcription** and subtitle generation
- **Content moderation** and compliance checking
- **Multi-language support** for global content

## 📚 Additional Resources

- [API Documentation](../docs/api-docs.md)
- [Deployment Guide](../docs/deployment-guide.md)
- [Performance Optimization](../docs/performance-optimization.md)
- [Security Audit](../docs/security-audit.md)
- [Troubleshooting Guide](../docs/troubleshooting.md)
