"use client";
import { useState, useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "../components/ui/Card";
import { Input } from "../components/ui/Input";
import { Button } from "../components/ui/Button";
import { useAuth } from "../context/AuthContext";
import { useToast } from "../context/ToastContext";

export function AuthForm() {
  const { user, signIn, signUp, signInWithGoogle, signOut, isLoading } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [mode, setMode] = useState<"signin" | "signup">("signin");
  const [loading, setLoading] = useState(false);

  const { showToast } = useToast();
  const searchParams = useSearchParams();
  const router = useRouter();

  // Check URL parameters for signup mode and errors
  useEffect(() => {
    const signupParam = searchParams.get("signup");
    if (signupParam === "true") {
      setMode("signup");
    }

    // Check for error messages from auth callback
    const errorParam = searchParams.get("error");
    if (errorParam) {
      setError(decodeURIComponent(errorParam));
      showToast("Authentication Error", decodeURIComponent(errorParam), "error");
    }
  }, [searchParams, showToast]);

  // Redirect to dashboard if user is already logged in
  useEffect(() => {
    if (user && !isLoading) {
      const redirectTimer = setTimeout(() => {
        router.push("/dashboard");
      }, 1500);

      return () => clearTimeout(redirectTimer);
    }
  }, [user, isLoading, router]);

  async function handleSignUp(e: React.FormEvent) {
    e.preventDefault();
    setError("");
    setLoading(true);

    const { error } = await signUp(email, password);

    if (error) {
      setError(error.message);
    } else {
      showToast(
        "Account created successfully",
        "Please check your email for the confirmation link.",
        "success"
      );
      setMode("signin");
    }
    setLoading(false);
  }

  async function handleSignIn(e: React.FormEvent) {
    e.preventDefault();
    setError("");
    setLoading(true);

    const { error } = await signIn(email, password);

    if (error) {
      setError(error.message);
    } else {
      showToast(
        "Signed in successfully",
        "Welcome back to ContentForge!",
        "success"
      );
    }
    setLoading(false);
  }

  async function handleSignInWithGoogle(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Get the redirectTo parameter from URL if it exists
    const redirectTo = searchParams.get("redirectTo") || "/dashboard";

    const { error } = await signInWithGoogle(redirectTo);
    if (error) setError(error.message);
    setLoading(false);
  }

  async function handleSignOut() {
    await signOut();
    showToast("Signed out successfully", "You have been signed out of your account.", "success");
  }

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (mode === "signin") {
      handleSignIn(e);
    } else {
      handleSignUp(e);
    }
  }

  if (isLoading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="flex justify-center items-center p-6">
          <p className="text-center text-muted-foreground">Loading...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      {user ? (
        <>
          <CardHeader>
            <CardTitle>Welcome</CardTitle>
            <CardDescription>You are signed in as {user.email}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-center text-muted-foreground">
              Redirecting you to the dashboard...
            </p>
          </CardContent>
          <CardFooter>
            <Button onClick={handleSignOut} variant="outline" fullWidth>
              Sign out
            </Button>
          </CardFooter>
        </>
      ) : (
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>{mode === "signin" ? "Sign In" : "Create Account"}</CardTitle>
            <CardDescription>
              {mode === "signin"
                ? "Enter your credentials to access your account"
                : "Fill in the information to create your account"}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Input
              id="email"
              type="email"
              label="Email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              autoComplete="email"
              disabled={loading}
            />
            <Input
              id="password"
              type="password"
              label="Password"
              placeholder="••••••••"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              autoComplete={mode === "signin" ? "current-password" : "new-password"}
              error={error}
              disabled={loading}
            />
          </CardContent>
          <CardFooter className="flex-col space-y-4">
            {mode === "signin" ? (
              <>
                <Button type="button" onClick={handleSignIn} disabled={loading} fullWidth>
                  {loading ? "Processing..." : "Sign In"}
                </Button>
                <Button type="button" onClick={handleSignInWithGoogle} disabled={loading} variant="outline" fullWidth>
                  Sign In with Google
                </Button>
              </>
            ) : (
              <Button type="button" onClick={handleSignUp} disabled={loading} fullWidth>
                {loading ? "Processing..." : "Create Account"}
              </Button>
            )}
            <div className="text-center text-sm">
              {mode === "signin" ? (
                <p>
                  Don't have an account?{" "}
                  <button
                    type="button"
                    className="text-primary hover:underline focus:outline-none"
                    onClick={() => {
                      setMode("signup");
                      setError("");
                    }}
                    disabled={loading}
                  >
                    Sign up
                  </button>
                </p>
              ) : (
                <p>
                  Already have an account?{" "}
                  <button
                    type="button"
                    className="text-primary hover:underline focus:outline-none"
                    onClick={() => {
                      setMode("signin");
                      setError("");
                    }}
                    disabled={loading}
                  >
                    Sign in
                  </button>
                </p>
              )}
            </div>
          </CardFooter>
        </form>
      )}
    </Card>
  );
}
