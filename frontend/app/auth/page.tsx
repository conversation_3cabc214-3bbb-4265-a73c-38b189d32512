"use client";

import { Suspense } from "react";
import dynamic from "next/dynamic";

// Import AuthForm dynamically with ssr: false to ensure it's only rendered on the client
const AuthForm = dynamic(() => import("../AuthForm").then(mod => ({ default: mod.AuthForm })), {
  ssr: false,
  loading: () => <div className="text-center p-4">Loading authentication form...</div>
});

export default function AuthPage() {
  return (
    <div className="container mx-auto py-12 px-4">
      <div className="max-w-md mx-auto">
        <h1 className="text-2xl font-bold text-center mb-6">Authentication</h1>
        <Suspense fallback={<div className="text-center p-4">Loading...</div>}>
          <AuthForm />
        </Suspense>
      </div>
    </div>
  );
}
