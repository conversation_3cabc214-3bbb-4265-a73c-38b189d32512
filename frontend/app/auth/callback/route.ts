import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

/**
 * This route handler manages the OAuth callback from Supabase Auth.
 * It exchanges the code for a session and redirects to the appropriate page.
 *
 * Note: We're using the new @supabase/ssr package which replaces the deprecated
 * @supabase/auth-helpers-nextjs package for Next.js 15+.
 */
export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const redirectTo = requestUrl.searchParams.get('redirectTo') || '/';
  const error = requestUrl.searchParams.get('error');
  const errorDescription = requestUrl.searchParams.get('error_description');

  // Handle error cases
  if (error) {
    console.error(`Auth error: ${error}`, errorDescription);
    return NextResponse.redirect(
      new URL(`/auth?error=${encodeURIComponent(errorDescription || error)}`, request.url)
    );
  }

  if (code) {
    try {
      const cookieStore = cookies();
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            get: (name) => cookieStore.get(name)?.value,
            set: (name, value, options) => cookieStore.set(name, value, options),
            remove: (name, options) => cookieStore.set(name, '', { ...options, maxAge: 0 }),
          },
        }
      );

      const { error: sessionError } = await supabase.auth.exchangeCodeForSession(code);

      if (sessionError) {
        console.error("Error exchanging code for session:", sessionError);
        return NextResponse.redirect(
          new URL(`/auth?error=${encodeURIComponent(sessionError.message)}`, request.url)
        );
      }

      // Check if user exists in the session
      const { data: { session } } = await supabase.auth.getSession();

      if (session) {
        // Successful authentication, redirect to dashboard or specified redirect path
        return NextResponse.redirect(new URL(redirectTo.startsWith('/') ? redirectTo : '/dashboard', request.url));
      }
    } catch (error) {
      console.error("Error during auth callback:", error);
      return NextResponse.redirect(
        new URL(`/auth?error=${encodeURIComponent('Authentication failed')}`, request.url)
      );
    }
  }

  // Fallback redirect if no code or session
  return NextResponse.redirect(new URL('/auth', request.url));
}
