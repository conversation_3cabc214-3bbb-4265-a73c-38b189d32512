"use client";

import { useState } from "react";
import { Button } from "../../../components/ui/Button";
import ContentCard, { ContentCardProps } from "../../../components/content/ContentCard";
import ContentFilters, { ContentFilters as FilterType } from "../../../components/content/ContentFilters";
import Link from "next/link";

// Sample data for content library
const sampleContent: ContentCardProps[] = [
  {
    id: "1",
    title: "10 Tips for Better Content Creation",
    excerpt: "Learn how to create engaging content that resonates with your audience and drives engagement.",
    thumbnail: "https://images.unsplash.com/photo-1499750310107-5fef28a66643",
    status: "published",
    date: "2023-05-15T10:30:00Z",
    platform: "Blog",
  },
  {
    id: "2",
    title: "How to Grow Your YouTube Channel",
    excerpt: "Strategies and tactics to increase your YouTube subscribers and views.",
    thumbnail: "https://images.unsplash.com/photo-1611162616475-46b635cb6868",
    status: "published",
    date: "2023-05-10T14:20:00Z",
    platform: "YouTube",
  },
  {
    id: "3",
    title: "Instagram Marketing in 2023",
    excerpt: "The latest trends and strategies for Instagram marketing in the current year.",
    thumbnail: "https://images.unsplash.com/photo-1611162618071-b39a2ec055fb",
    status: "scheduled",
    date: "2023-05-25T09:00:00Z",
    platform: "Instagram",
  },
  {
    id: "4",
    title: "LinkedIn Content Strategy",
    excerpt: "How to create a content strategy that works for LinkedIn's professional audience.",
    thumbnail: "https://images.unsplash.com/photo-1611944212129-29977ae1398c",
    status: "draft",
    date: "2023-05-05T11:45:00Z",
    platform: "LinkedIn",
  },
  {
    id: "5",
    title: "Twitter Growth Hacks",
    excerpt: "Quick tips and hacks to grow your Twitter following and engagement.",
    status: "draft",
    date: "2023-05-02T16:30:00Z",
    platform: "Twitter",
  },
  {
    id: "6",
    title: "Content Repurposing Guide",
    excerpt: "How to efficiently repurpose your content across multiple platforms.",
    thumbnail: "https://images.unsplash.com/photo-1586339949916-3e9457bef6d3",
    status: "published",
    date: "2023-04-28T13:15:00Z",
    platform: "Blog",
  },
];

export default function ContentLibraryPage() {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [filteredContent, setFilteredContent] = useState(sampleContent);

  const handleFilterChange = (filters: FilterType) => {
    let filtered = [...sampleContent];

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.title.toLowerCase().includes(searchLower) ||
          item.excerpt.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter((item) =>
        filters.status.includes(item.status)
      );
    }

    // Apply platform filter
    if (filters.platform.length > 0) {
      filtered = filtered.filter(
        (item) =>
          item.platform &&
          filters.platform.includes(item.platform.toLowerCase())
      );
    }

    // Apply date range filter
    if (filters.dateRange.from || filters.dateRange.to) {
      filtered = filtered.filter((item) => {
        const itemDate = new Date(item.date);
        const fromDate = filters.dateRange.from
          ? new Date(filters.dateRange.from)
          : new Date(0);
        const toDate = filters.dateRange.to
          ? new Date(filters.dateRange.to)
          : new Date(8640000000000000); // Max date

        return itemDate >= fromDate && itemDate <= toDate;
      });
    }

    setFilteredContent(filtered);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Content Library</h1>
        <Link href="/dashboard/content">
          <Button>Create New</Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-card border border-border rounded-lg p-4 sticky top-6">
            <h2 className="text-lg font-semibold mb-4">Filters</h2>
            <ContentFilters onFilterChange={handleFilterChange} />
          </div>
        </div>

        {/* Content Grid/List */}
        <div className="lg:col-span-3 space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {filteredContent.length} items
            </p>
            <div className="flex space-x-2">
              <button
                onClick={() => setViewMode("grid")}
                className={`p-2 rounded-md ${
                  viewMode === "grid"
                    ? "bg-primary/10 text-primary"
                    : "hover:bg-muted/50"
                }`}
                aria-label="Grid view"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                  />
                </svg>
              </button>
              <button
                onClick={() => setViewMode("list")}
                className={`p-2 rounded-md ${
                  viewMode === "list"
                    ? "bg-primary/10 text-primary"
                    : "hover:bg-muted/50"
                }`}
                aria-label="List view"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            </div>
          </div>

          {filteredContent.length === 0 ? (
            <div className="bg-card border border-border rounded-lg p-8 text-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-12 w-12 mx-auto text-muted-foreground/50 mb-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <h3 className="text-lg font-medium mb-2">No content found</h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your filters or create new content.
              </p>
              <Link href="/dashboard/content">
                <Button>Create New Content</Button>
              </Link>
            </div>
          ) : (
            <div
              className={
                viewMode === "grid"
                  ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                  : "space-y-4"
              }
            >
              {filteredContent.map((item) => (
                <ContentCard
                  key={item.id}
                  {...item}
                  onClick={() => {
                    // In a real app, navigate to content detail page
                    console.log("Clicked content:", item.id);
                  }}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
