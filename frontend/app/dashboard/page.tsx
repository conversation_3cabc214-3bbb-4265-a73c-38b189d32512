"use client";

import { useState } from "react";
import ContentSummary from "../../components/dashboard/ContentSummary";
import PerformanceMetrics from "../../components/dashboard/PerformanceMetrics";
import RecentActivities from "../../components/dashboard/RecentActivities";
import UpcomingPublications from "../../components/dashboard/UpcomingPublications";
import QuickActions from "../../components/dashboard/QuickActions";

export default function DashboardPage() {
  const [isLoading, setIsLoading] = useState(false);

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-2">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome back! Here's an overview of your content activities.
        </p>
      </div>

      {/* Content Summary */}
      <ContentSummary isLoading={isLoading} />

      {/* Performance Metrics */}
      <PerformanceMetrics />

      {/* Main Dashboard Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-8">
          <RecentActivities isLoading={isLoading} />
          <UpcomingPublications isLoading={isLoading} />
        </div>

        {/* Right Column */}
        <div className="space-y-8">
          <QuickActions />
        </div>
      </div>
    </div>
  );
}
