"use client";

import { useState } from "react";
import { Button } from "../../../components/ui/Button";
import AnalyticsChart from "../../../components/analytics/AnalyticsChart";
import MetricsSummary from "../../../components/analytics/MetricsSummary";

// Sample data for analytics
const viewsData = [
  { name: "<PERSON>", views: 4000, engagement: 2400 },
  { name: "Feb", views: 3000, engagement: 1398 },
  { name: "Mar", views: 2000, engagement: 9800 },
  { name: "Apr", views: 2780, engagement: 3908 },
  { name: "May", views: 1890, engagement: 4800 },
  { name: "<PERSON>", views: 2390, engagement: 3800 },
  { name: "<PERSON>", views: 3490, engagement: 4300 },
];

const platformData = [
  { name: "YouTube", value: 4000 },
  { name: "Instagram", value: 3000 },
  { name: "LinkedIn", value: 2000 },
  { name: "Twitter", value: 2780 },
  { name: "Blog", value: 1890 },
];

const contentTypeData = [
  { name: "<PERSON>", video: 20, image: 15, text: 30 },
  { name: "Feb", video: 25, image: 18, text: 28 },
  { name: "<PERSON>", video: 30, image: 20, text: 25 },
  { name: "Apr", video: 28, image: 22, text: 32 },
  { name: "May", video: 35, image: 25, text: 30 },
  { name: "Jun", video: 40, image: 30, text: 35 },
  { name: "Jul", video: 45, image: 35, text: 40 },
];

const audienceData = [
  { name: "18-24", value: 15 },
  { name: "25-34", value: 35 },
  { name: "35-44", value: 25 },
  { name: "45-54", value: 15 },
  { name: "55+", value: 10 },
];

type DateRange = "7d" | "30d" | "90d" | "1y" | "all";

export default function AnalyticsPage() {
  const [dateRange, setDateRange] = useState<DateRange>("30d");

  const metrics = [
    {
      title: "Total Views",
      value: "125.2k",
      change: "+12.5%",
      changeType: "positive" as const,
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
          />
        </svg>
      ),
    },
    {
      title: "Engagement Rate",
      value: "8.3%",
      change: "+2.1%",
      changeType: "positive" as const,
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
          />
        </svg>
      ),
    },
    {
      title: "Conversion Rate",
      value: "3.2%",
      change: "-0.5%",
      changeType: "negative" as const,
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
      ),
    },
    {
      title: "Avg. Time on Page",
      value: "2:35",
      change: "+0:42",
      changeType: "positive" as const,
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Analytics</h1>
        <div className="flex items-center space-x-2">
          <DateRangeButton
            range="7d"
            active={dateRange === "7d"}
            onClick={() => setDateRange("7d")}
          />
          <DateRangeButton
            range="30d"
            active={dateRange === "30d"}
            onClick={() => setDateRange("30d")}
          />
          <DateRangeButton
            range="90d"
            active={dateRange === "90d"}
            onClick={() => setDateRange("90d")}
          />
          <DateRangeButton
            range="1y"
            active={dateRange === "1y"}
            onClick={() => setDateRange("1y")}
          />
          <DateRangeButton
            range="all"
            active={dateRange === "all"}
            onClick={() => setDateRange("all")}
          />
        </div>
      </div>

      {/* Metrics Summary */}
      <MetricsSummary metrics={metrics} />

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <AnalyticsChart
          title="Content Performance"
          description="Views and engagement over time"
          type="line"
          data={viewsData}
          dataKeys={["views", "engagement"]}
        />

        <AnalyticsChart
          title="Platform Distribution"
          description="Content views by platform"
          type="pie"
          data={platformData}
          dataKeys={["value"]}
        />

        <AnalyticsChart
          title="Content Type Performance"
          description="Performance by content type"
          type="bar"
          data={contentTypeData}
          dataKeys={["video", "image", "text"]}
        />

        <AnalyticsChart
          title="Audience Demographics"
          description="Age distribution of audience"
          type="pie"
          data={audienceData}
          dataKeys={["value"]}
        />
      </div>
    </div>
  );
}

interface DateRangeButtonProps {
  range: DateRange;
  active: boolean;
  onClick: () => void;
}

function DateRangeButton({ range, active, onClick }: DateRangeButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`px-3 py-1 text-sm rounded-md transition-colors ${
        active
          ? "bg-primary text-primary-foreground"
          : "bg-muted hover:bg-muted/80 text-foreground"
      }`}
    >
      {range}
    </button>
  );
}
