"use client";

import { useState } from "react";
import { Card, CardContent } from "../../../components/ui/Card";
import { Button } from "../../../components/ui/Button";
import { Input } from "../../../components/ui/Input";
import ContentEditor, { ContentItem } from "../../../components/content/ContentEditor";

export default function ContentCreationPage() {
  const [title, setTitle] = useState("");
  const [showPreview, setShowPreview] = useState(false);
  const [content, setContent] = useState<ContentItem[]>([]);

  const handleSaveContent = (newContent: ContentItem[]) => {
    setContent(newContent);
    // In a real app, you would save this to the backend
    console.log("Content saved:", newContent);
  };

  const handlePublish = () => {
    // In a real app, you would publish the content
    console.log("Publishing content:", { title, content });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Create Content</h1>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowPreview(!showPreview)}
          >
            {showPreview ? "Edit" : "Preview"}
          </Button>
          <Button onClick={handlePublish}>Publish</Button>
        </div>
      </div>

      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div>
              <label
                htmlFor="title"
                className="block text-sm font-medium mb-1"
              >
                Title
              </label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter content title"
                className="w-full"
              />
            </div>

            {showPreview ? (
              <div className="border rounded-md p-4 bg-muted/20">
                <h2 className="text-2xl font-bold mb-4">{title || "Untitled"}</h2>
                <div className="prose max-w-none">
                  {content.map((item) => {
                    switch (item.type) {
                      case "text":
                        return <p key={item.id}>{item.content.text}</p>;
                      case "heading":
                        if (item.content.level === "h1")
                          return <h1 key={item.id}>{item.content.text}</h1>;
                        if (item.content.level === "h2")
                          return <h2 key={item.id}>{item.content.text}</h2>;
                        if (item.content.level === "h3")
                          return <h3 key={item.id}>{item.content.text}</h3>;
                        return <h4 key={item.id}>{item.content.text}</h4>;
                      case "image":
                        return (
                          <figure key={item.id}>
                            <img
                              src={item.content.url}
                              alt={item.content.alt}
                              className="max-w-full h-auto rounded-md"
                            />
                            {item.content.alt && (
                              <figcaption className="text-sm text-muted-foreground mt-1">
                                {item.content.alt}
                              </figcaption>
                            )}
                          </figure>
                        );
                      case "video":
                        return (
                          <figure key={item.id}>
                            <div className="aspect-video bg-muted/50 rounded-md flex items-center justify-center">
                              <span className="text-muted-foreground">
                                Video: {item.content.url}
                              </span>
                            </div>
                            {item.content.caption && (
                              <figcaption className="text-sm text-muted-foreground mt-1">
                                {item.content.caption}
                              </figcaption>
                            )}
                          </figure>
                        );
                      case "quote":
                        return (
                          <blockquote
                            key={item.id}
                            className="border-l-4 border-primary pl-4 italic"
                          >
                            <p>{item.content.text}</p>
                            {item.content.source && (
                              <footer className="text-sm text-muted-foreground mt-1">
                                — {item.content.source}
                              </footer>
                            )}
                          </blockquote>
                        );
                      case "list":
                        return item.content.style === "bullet" ? (
                          <ul key={item.id} className="list-disc pl-5">
                            {item.content.items.map((text: string, i: number) => (
                              <li key={i}>{text}</li>
                            ))}
                          </ul>
                        ) : (
                          <ol key={item.id} className="list-decimal pl-5">
                            {item.content.items.map((text: string, i: number) => (
                              <li key={i}>{text}</li>
                            ))}
                          </ol>
                        );
                      case "divider":
                        return <hr key={item.id} className="my-4" />;
                      default:
                        return null;
                    }
                  })}
                </div>
              </div>
            ) : (
              <ContentEditor
                initialContent={content}
                onSave={handleSaveContent}
              />
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
