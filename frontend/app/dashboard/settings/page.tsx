"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "../../../components/ui/Card";
import { Button } from "../../../components/ui/Button";
import { Input } from "../../../components/ui/Input";

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<
    "profile" | "notifications" | "appearance" | "account"
  >("profile");

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Settings</h1>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <Card className="md:col-span-1">
          <CardContent className="p-4">
            <nav className="space-y-1">
              <SettingsNavItem
                title="Profile"
                active={activeTab === "profile"}
                onClick={() => setActiveTab("profile")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                }
              />
              <SettingsNavItem
                title="Notifications"
                active={activeTab === "notifications"}
                onClick={() => setActiveTab("notifications")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                    />
                  </svg>
                }
              />
              <SettingsNavItem
                title="Appearance"
                active={activeTab === "appearance"}
                onClick={() => setActiveTab("appearance")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
                    />
                  </svg>
                }
              />
              <SettingsNavItem
                title="Account"
                active={activeTab === "account"}
                onClick={() => setActiveTab("account")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                }
              />
            </nav>
          </CardContent>
        </Card>

        {/* Settings Content */}
        <Card className="md:col-span-3">
          <CardContent className="p-6">
            {activeTab === "profile" && <ProfileSettings />}
            {activeTab === "notifications" && <NotificationSettings />}
            {activeTab === "appearance" && <AppearanceSettings />}
            {activeTab === "account" && <AccountSettings />}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

interface SettingsNavItemProps {
  title: string;
  active: boolean;
  onClick: () => void;
  icon: React.ReactNode;
}

function SettingsNavItem({
  title,
  active,
  onClick,
  icon,
}: SettingsNavItemProps) {
  return (
    <button
      onClick={onClick}
      className={`flex items-center w-full px-3 py-2 text-sm rounded-md transition-colors ${
        active
          ? "bg-primary/10 text-primary"
          : "hover:bg-muted/50 text-foreground"
      }`}
    >
      <span className="mr-3">{icon}</span>
      <span>{title}</span>
    </button>
  );
}

function ProfileSettings() {
  return (
    <div className="space-y-6">
      <CardHeader className="px-0 pt-0">
        <CardTitle>Profile Settings</CardTitle>
      </CardHeader>

      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <div className="w-20 h-20 rounded-full bg-muted/50 flex items-center justify-center text-muted-foreground">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-10 w-10"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
          <div>
            <Button size="sm">Upload Photo</Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">First Name</label>
            <Input defaultValue="John" />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Last Name</label>
            <Input defaultValue="Doe" />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Email</label>
          <Input defaultValue="<EMAIL>" type="email" />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Bio</label>
          <textarea
            className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            rows={4}
            defaultValue="Content creator and digital marketer with 5+ years of experience."
          />
        </div>

        <div className="flex justify-end">
          <Button>Save Changes</Button>
        </div>
      </div>
    </div>
  );
}

function NotificationSettings() {
  return (
    <div className="space-y-6">
      <CardHeader className="px-0 pt-0">
        <CardTitle>Notification Settings</CardTitle>
      </CardHeader>

      <div className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Email Notifications</h3>
          <div className="space-y-2">
            <NotificationOption
              title="Content Updates"
              description="Receive updates about your content performance"
              defaultChecked={true}
            />
            <NotificationOption
              title="Workflow Notifications"
              description="Get notified about workflow status changes"
              defaultChecked={true}
            />
            <NotificationOption
              title="Comments and Feedback"
              description="Receive notifications for comments on your content"
              defaultChecked={false}
            />
            <NotificationOption
              title="Platform Updates"
              description="Get notified about new features and updates"
              defaultChecked={true}
            />
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-medium">In-App Notifications</h3>
          <div className="space-y-2">
            <NotificationOption
              title="Content Performance Alerts"
              description="Get alerts for significant changes in content performance"
              defaultChecked={true}
            />
            <NotificationOption
              title="Workflow Reminders"
              description="Receive reminders about pending workflow tasks"
              defaultChecked={true}
            />
            <NotificationOption
              title="Team Mentions"
              description="Get notified when you're mentioned by team members"
              defaultChecked={true}
            />
          </div>
        </div>

        <div className="flex justify-end">
          <Button>Save Preferences</Button>
        </div>
      </div>
    </div>
  );
}

interface NotificationOptionProps {
  title: string;
  description: string;
  defaultChecked: boolean;
}

function NotificationOption({
  title,
  description,
  defaultChecked,
}: NotificationOptionProps) {
  const [checked, setChecked] = useState(defaultChecked);

  return (
    <div className="flex items-start space-x-3 p-3 rounded-md hover:bg-muted/30 transition-colors">
      <input
        type="checkbox"
        id={title.replace(/\s+/g, "-").toLowerCase()}
        checked={checked}
        onChange={(e) => setChecked(e.target.checked)}
        className="mt-1"
      />
      <div>
        <label
          htmlFor={title.replace(/\s+/g, "-").toLowerCase()}
          className="font-medium cursor-pointer"
        >
          {title}
        </label>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
    </div>
  );
}

function AppearanceSettings() {
  return (
    <div className="space-y-6">
      <CardHeader className="px-0 pt-0">
        <CardTitle>Appearance Settings</CardTitle>
      </CardHeader>

      <div className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Theme</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <ThemeOption
              title="Light"
              selected={true}
              onClick={() => {}}
              preview={
                <div className="w-full h-20 rounded-md bg-white border border-gray-200 flex items-center justify-center">
                  <div className="w-8 h-8 rounded-full bg-blue-500"></div>
                </div>
              }
            />
            <ThemeOption
              title="Dark"
              selected={false}
              onClick={() => {}}
              preview={
                <div className="w-full h-20 rounded-md bg-gray-900 border border-gray-700 flex items-center justify-center">
                  <div className="w-8 h-8 rounded-full bg-blue-400"></div>
                </div>
              }
            />
            <ThemeOption
              title="System"
              selected={false}
              onClick={() => {}}
              preview={
                <div className="w-full h-20 rounded-md bg-gradient-to-r from-white to-gray-900 border border-gray-300 flex items-center justify-center">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-blue-400"></div>
                </div>
              }
            />
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-medium">Accent Color</h3>
          <div className="flex flex-wrap gap-3">
            <ColorOption color="bg-blue-500" selected={true} onClick={() => {}} />
            <ColorOption color="bg-green-500" selected={false} onClick={() => {}} />
            <ColorOption color="bg-purple-500" selected={false} onClick={() => {}} />
            <ColorOption color="bg-pink-500" selected={false} onClick={() => {}} />
            <ColorOption color="bg-orange-500" selected={false} onClick={() => {}} />
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-medium">Font Size</h3>
          <div className="flex items-center space-x-4">
            <button className="text-sm hover:text-primary transition-colors">
              A
            </button>
            <input
              type="range"
              min="1"
              max="3"
              defaultValue="2"
              className="w-full"
            />
            <button className="text-lg hover:text-primary transition-colors">
              A
            </button>
          </div>
        </div>

        <div className="flex justify-end">
          <Button>Save Preferences</Button>
        </div>
      </div>
    </div>
  );
}

interface ThemeOptionProps {
  title: string;
  selected: boolean;
  onClick: () => void;
  preview: React.ReactNode;
}

function ThemeOption({ title, selected, onClick, preview }: ThemeOptionProps) {
  return (
    <div
      onClick={onClick}
      className={`cursor-pointer rounded-md border ${
        selected ? "border-primary" : "border-border"
      } p-2 transition-colors hover:border-primary`}
    >
      {preview}
      <div className="flex items-center justify-between mt-2">
        <span className="text-sm font-medium">{title}</span>
        {selected && (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-primary"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        )}
      </div>
    </div>
  );
}

interface ColorOptionProps {
  color: string;
  selected: boolean;
  onClick: () => void;
}

function ColorOption({ color, selected, onClick }: ColorOptionProps) {
  return (
    <button
      onClick={onClick}
      className={`w-8 h-8 rounded-full ${color} ${
        selected ? "ring-2 ring-offset-2 ring-primary" : ""
      }`}
      aria-label={`Select ${color} as accent color`}
    ></button>
  );
}

function AccountSettings() {
  return (
    <div className="space-y-6">
      <CardHeader className="px-0 pt-0">
        <CardTitle>Account Settings</CardTitle>
      </CardHeader>

      <div className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Change Password</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                Current Password
              </label>
              <Input type="password" />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                New Password
              </label>
              <Input type="password" />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                Confirm New Password
              </label>
              <Input type="password" />
            </div>
            <div>
              <Button size="sm">Update Password</Button>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-medium">Connected Accounts</h3>
          <div className="space-y-3">
            <ConnectedAccount
              platform="Google"
              connected={true}
              email="<EMAIL>"
            />
            <ConnectedAccount
              platform="Facebook"
              connected={false}
              email=""
            />
            <ConnectedAccount
              platform="Twitter"
              connected={true}
              email="@johndoe"
            />
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-medium">Danger Zone</h3>
          <div className="p-4 border border-error/30 rounded-md bg-error/5">
            <h4 className="text-error font-medium mb-2">Delete Account</h4>
            <p className="text-sm text-muted-foreground mb-4">
              Once you delete your account, there is no going back. Please be
              certain.
            </p>
            <Button variant="destructive" size="sm">
              Delete Account
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

interface ConnectedAccountProps {
  platform: string;
  connected: boolean;
  email: string;
}

function ConnectedAccount({
  platform,
  connected,
  email,
}: ConnectedAccountProps) {
  return (
    <div className="flex items-center justify-between p-3 border border-border rounded-md">
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 rounded-full bg-muted/50 flex items-center justify-center">
          {platform.charAt(0)}
        </div>
        <div>
          <h4 className="font-medium">{platform}</h4>
          {connected && (
            <p className="text-xs text-muted-foreground">{email}</p>
          )}
        </div>
      </div>
      <Button variant={connected ? "outline" : "default"} size="sm">
        {connected ? "Disconnect" : "Connect"}
      </Button>
    </div>
  );
}
