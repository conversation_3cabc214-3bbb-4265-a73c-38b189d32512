"use client";

import { useState } from "react";
import { Card, CardContent } from "../../../components/ui/Card";
import { Button } from "../../../components/ui/Button";
import { Input } from "../../../components/ui/Input";
import WorkflowBuilder, { WorkflowStep } from "../../../components/workflow/WorkflowBuilder";

export default function WorkflowsPage() {
  const [workflowName, setWorkflowName] = useState("");
  const [workflowDescription, setWorkflowDescription] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  const handleSaveWorkflow = (steps: WorkflowStep[]) => {
    // In a real app, you would save this to the backend
    console.log("Workflow saved:", {
      name: workflowName,
      description: workflowDescription,
      steps,
    });
    
    // Reset form and close creation panel
    setWorkflowName("");
    setWorkflowDescription("");
    setIsCreating(false);
  };

  // Sample workflows for the list
  const sampleWorkflows = [
    {
      id: "1",
      name: "Blog Post Workflow",
      description: "Transform content into blog posts with review and publishing",
      steps: 5,
      lastUsed: "2023-05-10T14:30:00Z",
    },
    {
      id: "2",
      name: "YouTube Video Workflow",
      description: "Process and publish video content to YouTube",
      steps: 4,
      lastUsed: "2023-05-05T09:15:00Z",
    },
    {
      id: "3",
      name: "Social Media Workflow",
      description: "Create and schedule content for multiple social platforms",
      steps: 6,
      lastUsed: "2023-05-12T16:45:00Z",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Workflows</h1>
        <Button onClick={() => setIsCreating(true)}>Create Workflow</Button>
      </div>

      {isCreating ? (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="workflowName"
                  className="block text-sm font-medium mb-1"
                >
                  Workflow Name
                </label>
                <Input
                  id="workflowName"
                  value={workflowName}
                  onChange={(e) => setWorkflowName(e.target.value)}
                  placeholder="Enter workflow name"
                  className="w-full"
                />
              </div>

              <div>
                <label
                  htmlFor="workflowDescription"
                  className="block text-sm font-medium mb-1"
                >
                  Description
                </label>
                <textarea
                  id="workflowDescription"
                  value={workflowDescription}
                  onChange={(e) => setWorkflowDescription(e.target.value)}
                  placeholder="Enter workflow description"
                  className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  rows={3}
                />
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Workflow Steps</h3>
                <WorkflowBuilder onSave={handleSaveWorkflow} />
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {sampleWorkflows.map((workflow) => (
            <Card key={workflow.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <h3 className="text-lg font-medium mb-1">{workflow.name}</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  {workflow.description}
                </p>
                <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                  <span>{workflow.steps} steps</span>
                  <span>
                    Last used:{" "}
                    {new Date(workflow.lastUsed).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => {
                      // In a real app, navigate to workflow edit page
                      console.log("Edit workflow:", workflow.id);
                    }}
                  >
                    Edit
                  </Button>
                  <Button
                    size="sm"
                    className="flex-1"
                    onClick={() => {
                      // In a real app, navigate to workflow run page
                      console.log("Run workflow:", workflow.id);
                    }}
                  >
                    Run
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
