import { supabase } from './supabaseClient';
import { 
  PublicationItem, 
  ProductionItem,
  PublicationCreateInput, 
  PublicationUpdateInput, 
  PublicationFilterOptions,
  PublicationSearchResult,
  PlatformConnection,
  Platform
} from '../types/publishing.types';

// Generic API response type
export interface ApiResponse<T> {
  data: T | null;
  error: Error | null;
  status: number;
}

// Generic fetch function with error handling
async function fetchData<T>(
  fetcher: () => Promise<{ data: T | null; error: any }>
): Promise<ApiResponse<T>> {
  try {
    const { data, error } = await fetcher();
    
    if (error) {
      console.error('Publishing API Error:', error);
      return {
        data: null,
        error: new Error(error.message || 'An error occurred'),
        status: error.status || 500,
      };
    }
    
    return {
      data,
      error: null,
      status: 200,
    };
  } catch (err: any) {
    console.error('Publishing Fetch Error:', err);
    return {
      data: null,
      error: new Error(err.message || 'An unexpected error occurred'),
      status: 500,
    };
  }
}

// Publishing API service
export const publishingApi = {
  // Get all publications
  getAllPublications: async (options?: PublicationFilterOptions): Promise<ApiResponse<PublicationItem[]>> => {
    let query = supabase
      .from('publications')
      .select('*');
    
    // Apply filters if provided
    if (options) {
      if (options.platform) {
        query = query.eq('platform', options.platform);
      }
      
      if (options.status) {
        query = query.eq('status', options.status);
      }
      
      if (options.startDate) {
        const dateField = options.sortBy === 'published_at' ? 'published_at' : 'scheduled_at';
        query = query.gte(dateField, options.startDate);
      }
      
      if (options.endDate) {
        const dateField = options.sortBy === 'published_at' ? 'published_at' : 'scheduled_at';
        query = query.lte(dateField, options.endDate);
      }
      
      // Apply sorting
      const sortBy = options.sortBy || 'scheduled_at';
      const sortOrder = options.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });
      
      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }
      
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }
    } else {
      // Default sorting by scheduled_at desc
      query = query.order('scheduled_at', { ascending: false });
    }
    
    return fetchData(() => query);
  },
  
  // Search publications with pagination
  searchPublications: async (
    options: PublicationFilterOptions
  ): Promise<ApiResponse<PublicationSearchResult>> => {
    try {
      // First, get the total count
      const countQuery = supabase
        .from('publications')
        .select('publication_id', { count: 'exact' });
      
      // Apply the same filters as the main query
      if (options.platform) {
        countQuery.eq('platform', options.platform);
      }
      
      if (options.status) {
        countQuery.eq('status', options.status);
      }
      
      if (options.startDate) {
        const dateField = options.sortBy === 'published_at' ? 'published_at' : 'scheduled_at';
        countQuery.gte(dateField, options.startDate);
      }
      
      if (options.endDate) {
        const dateField = options.sortBy === 'published_at' ? 'published_at' : 'scheduled_at';
        countQuery.lte(dateField, options.endDate);
      }
      
      const { count, error: countError } = await countQuery;
      
      if (countError) {
        throw new Error(countError.message);
      }
      
      // Now get the actual data with pagination
      const { data, error } = await publishingApi.getAllPublications(options);
      
      if (error) {
        throw error;
      }
      
      const pageSize = options.limit || 10;
      const page = Math.floor((options.offset || 0) / pageSize) + 1;
      const totalPages = Math.ceil((count || 0) / pageSize);
      
      return {
        data: {
          items: data || [],
          total: count || 0,
          page,
          pageSize,
          totalPages
        },
        error: null,
        status: 200
      };
    } catch (err: any) {
      console.error('Publication Search Error:', err);
      return {
        data: null,
        error: new Error(err.message || 'An unexpected error occurred'),
        status: 500,
      };
    }
  },
  
  // Get publications by production ID
  getPublicationsByProduction: async (productionId: string): Promise<ApiResponse<PublicationItem[]>> => {
    return fetchData(() => 
      supabase
        .from('publications')
        .select('*')
        .eq('production_id', productionId)
        .order('scheduled_at', { ascending: false })
    );
  },
  
  // Get publication by ID
  getPublicationById: async (publicationId: string): Promise<ApiResponse<PublicationItem>> => {
    return fetchData(() => 
      supabase
        .from('publications')
        .select('*')
        .eq('publication_id', publicationId)
        .single()
    );
  },
  
  // Create new publication
  createPublication: async (input: PublicationCreateInput): Promise<ApiResponse<PublicationItem>> => {
    return fetchData(() => 
      supabase
        .from('publications')
        .insert({
          production_id: input.production_id,
          platform: input.platform,
          status: 'draft',
          scheduled_at: input.scheduled_at || null,
          metadata: input.metadata || null
        })
        .select()
        .single()
    );
  },
  
  // Update publication
  updatePublication: async (publicationId: string, updates: PublicationUpdateInput): Promise<ApiResponse<PublicationItem>> => {
    return fetchData(() => 
      supabase
        .from('publications')
        .update(updates)
        .eq('publication_id', publicationId)
        .select()
        .single()
    );
  },
  
  // Delete publication
  deletePublication: async (publicationId: string): Promise<ApiResponse<null>> => {
    return fetchData(() => 
      supabase
        .from('publications')
        .delete()
        .eq('publication_id', publicationId)
        .then(() => ({ data: null, error: null }))
    );
  },
  
  // Publish now (change status to publishing)
  publishNow: async (publicationId: string): Promise<ApiResponse<PublicationItem>> => {
    return fetchData(() => 
      supabase
        .from('publications')
        .update({
          status: 'publishing',
          scheduled_at: new Date().toISOString()
        })
        .eq('publication_id', publicationId)
        .select()
        .single()
    );
  },
  
  // Get production by ID
  getProductionById: async (productionId: string): Promise<ApiResponse<ProductionItem>> => {
    return fetchData(() => 
      supabase
        .from('productions')
        .select('*')
        .eq('production_id', productionId)
        .single()
    );
  },
  
  // Get productions by workflow ID
  getProductionsByWorkflow: async (workflowId: string): Promise<ApiResponse<ProductionItem[]>> => {
    return fetchData(() => 
      supabase
        .from('productions')
        .select('*')
        .eq('workflow_id', workflowId)
        .order('created_at', { ascending: false })
    );
  },
  
  // Get platform connections for a user
  getPlatformConnections: async (userId: string): Promise<ApiResponse<PlatformConnection[]>> => {
    return fetchData(() => 
      supabase
        .from('platform_connections')
        .select('*')
        .eq('user_id', userId)
    );
  },
  
  // Connect to a platform (this would typically redirect to OAuth flow)
  connectToPlatform: async (userId: string, platform: Platform): Promise<ApiResponse<{ redirectUrl: string }>> => {
    // In a real implementation, this would generate an OAuth URL and return it
    // For now, we'll simulate this with a mock response
    return {
      data: {
        redirectUrl: `/api/connect/${platform}?userId=${userId}`
      },
      error: null,
      status: 200
    };
  },
  
  // Disconnect from a platform
  disconnectFromPlatform: async (userId: string, platform: Platform): Promise<ApiResponse<null>> => {
    return fetchData(() => 
      supabase
        .from('platform_connections')
        .delete()
        .eq('user_id', userId)
        .eq('platform', platform)
        .then(() => ({ data: null, error: null }))
    );
  }
};
