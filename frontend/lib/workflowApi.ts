import { supabase } from './supabaseClient';
import { 
  WorkflowItem, 
  WorkflowCreateInput, 
  WorkflowUpdateInput, 
  WorkflowFilterOptions,
  WorkflowSearchResult,
  WorkflowTemplate
} from '../types/workflow.types';

// Generic API response type
export interface ApiResponse<T> {
  data: T | null;
  error: Error | null;
  status: number;
}

// Generic fetch function with error handling
async function fetchData<T>(
  fetcher: () => Promise<{ data: T | null; error: any }>
): Promise<ApiResponse<T>> {
  try {
    const { data, error } = await fetcher();
    
    if (error) {
      console.error('Workflow API Error:', error);
      return {
        data: null,
        error: new Error(error.message || 'An error occurred'),
        status: error.status || 500,
      };
    }
    
    return {
      data,
      error: null,
      status: 200,
    };
  } catch (err: any) {
    console.error('Workflow Fetch Error:', err);
    return {
      data: null,
      error: new Error(err.message || 'An unexpected error occurred'),
      status: 500,
    };
  }
}

// Workflow API service
export const workflowApi = {
  // Get all workflows for the current user
  getAllWorkflows: async (userId: string, options?: WorkflowFilterOptions): Promise<ApiResponse<WorkflowItem[]>> => {
    let query = supabase
      .from('workflows')
      .select('*')
      .eq('user_id', userId);
    
    // Apply filters if provided
    if (options) {
      if (options.workflow_type) {
        query = query.eq('workflow_type', options.workflow_type);
      }
      
      if (options.status) {
        query = query.eq('status', options.status);
      }
      
      if (options.search) {
        query = query.or(`name.ilike.%${options.search}%,description.ilike.%${options.search}%`);
      }
      
      if (options.startDate) {
        query = query.gte('created_at', options.startDate);
      }
      
      if (options.endDate) {
        query = query.lte('created_at', options.endDate);
      }
      
      // Apply sorting
      const sortBy = options.sortBy || 'created_at';
      const sortOrder = options.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });
      
      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }
      
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }
    } else {
      // Default sorting by created_at desc
      query = query.order('created_at', { ascending: false });
    }
    
    return fetchData(() => query);
  },
  
  // Search workflows with pagination
  searchWorkflows: async (
    userId: string, 
    options: WorkflowFilterOptions
  ): Promise<ApiResponse<WorkflowSearchResult>> => {
    try {
      // First, get the total count
      const countQuery = supabase
        .from('workflows')
        .select('workflow_id', { count: 'exact' })
        .eq('user_id', userId);
      
      // Apply the same filters as the main query
      if (options.workflow_type) {
        countQuery.eq('workflow_type', options.workflow_type);
      }
      
      if (options.status) {
        countQuery.eq('status', options.status);
      }
      
      if (options.search) {
        countQuery.or(`name.ilike.%${options.search}%,description.ilike.%${options.search}%`);
      }
      
      if (options.startDate) {
        countQuery.gte('created_at', options.startDate);
      }
      
      if (options.endDate) {
        countQuery.lte('created_at', options.endDate);
      }
      
      const { count, error: countError } = await countQuery;
      
      if (countError) {
        throw new Error(countError.message);
      }
      
      // Now get the actual data with pagination
      const { data, error } = await workflowApi.getAllWorkflows(userId, options);
      
      if (error) {
        throw error;
      }
      
      const pageSize = options.limit || 10;
      const page = Math.floor((options.offset || 0) / pageSize) + 1;
      const totalPages = Math.ceil((count || 0) / pageSize);
      
      return {
        data: {
          items: data || [],
          total: count || 0,
          page,
          pageSize,
          totalPages
        },
        error: null,
        status: 200
      };
    } catch (err: any) {
      console.error('Workflow Search Error:', err);
      return {
        data: null,
        error: new Error(err.message || 'An unexpected error occurred'),
        status: 500,
      };
    }
  },
  
  // Get workflow by ID
  getWorkflowById: async (workflowId: string): Promise<ApiResponse<WorkflowItem>> => {
    return fetchData(() => 
      supabase
        .from('workflows')
        .select('*')
        .eq('workflow_id', workflowId)
        .single()
    );
  },
  
  // Create new workflow
  createWorkflow: async (userId: string, input: WorkflowCreateInput): Promise<ApiResponse<WorkflowItem>> => {
    const now = new Date().toISOString();
    
    // Initialize steps with status
    const initializedSteps = input.steps.map((step, index) => ({
      ...step,
      status: 'pending',
      order: index
    }));
    
    return fetchData(() => 
      supabase
        .from('workflows')
        .insert({
          user_id: userId,
          workflow_type: input.workflow_type,
          name: input.name,
          description: input.description || null,
          steps: initializedSteps,
          status: 'pending',
          metadata: input.metadata || null,
          created_at: now
        })
        .select()
        .single()
    );
  },
  
  // Update workflow
  updateWorkflow: async (workflowId: string, updates: WorkflowUpdateInput): Promise<ApiResponse<WorkflowItem>> => {
    return fetchData(() => 
      supabase
        .from('workflows')
        .update(updates)
        .eq('workflow_id', workflowId)
        .select()
        .single()
    );
  },
  
  // Delete workflow
  deleteWorkflow: async (workflowId: string): Promise<ApiResponse<null>> => {
    return fetchData(() => 
      supabase
        .from('workflows')
        .delete()
        .eq('workflow_id', workflowId)
        .then(() => ({ data: null, error: null }))
    );
  },
  
  // Start workflow execution
  startWorkflow: async (workflowId: string): Promise<ApiResponse<WorkflowItem>> => {
    const now = new Date().toISOString();
    
    return fetchData(() => 
      supabase
        .from('workflows')
        .update({
          status: 'running',
          started_at: now
        })
        .eq('workflow_id', workflowId)
        .select()
        .single()
    );
  },
  
  // Cancel workflow execution
  cancelWorkflow: async (workflowId: string): Promise<ApiResponse<WorkflowItem>> => {
    return fetchData(() => 
      supabase
        .from('workflows')
        .update({
          status: 'cancelled'
        })
        .eq('workflow_id', workflowId)
        .select()
        .single()
    );
  },
  
  // Get workflow templates
  getWorkflowTemplates: async (): Promise<ApiResponse<WorkflowTemplate[]>> => {
    return fetchData(() => 
      supabase
        .from('workflow_templates')
        .select('*')
        .order('name', { ascending: true })
    );
  },
  
  // Get workflow template by ID
  getWorkflowTemplateById: async (templateId: string): Promise<ApiResponse<WorkflowTemplate>> => {
    return fetchData(() => 
      supabase
        .from('workflow_templates')
        .select('*')
        .eq('id', templateId)
        .single()
    );
  }
};
