import { supabase } from './supabaseClient';
import { Database } from '../types/database.types';

// Type for API response
type ApiResponse<T> = {
  data: T | null;
  error: Error | null;
  status: number;
};

// Generic fetch function with error handling
export async function fetchData<T>(
  fetcher: () => Promise<{ data: T | null; error: any }>
): Promise<ApiResponse<T>> {
  try {
    const { data, error } = await fetcher();

    if (error) {
      console.error('API Error:', error);
      return {
        data: null,
        error: new Error(error.message || 'An error occurred'),
        status: error.status || 500,
      };
    }

    return {
      data,
      error: null,
      status: 200,
    };
  } catch (err: any) {
    console.error('Fetch Error:', err);
    return {
      data: null,
      error: new Error(err.message || 'An unexpected error occurred'),
      status: 500,
    };
  }
}

// User related API calls
export const userApi = {
  // Get user profile
  getProfile: async (userId: string) => {
    return fetchData(() =>
      supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()
    );
  },

  // Update user profile
  updateProfile: async (userId: string, updates: Partial<Database['public']['Tables']['users']['Update']>) => {
    return fetchData(() =>
      supabase
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single()
    );
  },
};

// Content related API calls
export const contentApi = {
  // Get user's inputs
  getUserInputs: async (userId: string) => {
    return fetchData(() =>
      supabase
        .from('inputs')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
    );
  },

  // Create new input
  createInput: async (input: Database['public']['Tables']['inputs']['Insert']) => {
    return fetchData(() =>
      supabase
        .from('inputs')
        .insert(input)
        .select()
        .single()
    );
  },

  // Get input by ID
  getInputById: async (inputId: string) => {
    return fetchData(() =>
      supabase
        .from('inputs')
        .select('*')
        .eq('input_id', inputId)
        .single()
    );
  },

  // Delete input
  deleteInput: async (inputId: string) => {
    return fetchData(() =>
      supabase
        .from('inputs')
        .delete()
        .eq('input_id', inputId)
    );
  },
};

// Analytics related API calls
export const analyticsApi = {
  // Get analytics overview
  getOverview: async (params: {
    start_date?: string;
    end_date?: string;
    platforms?: string;
  } = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) searchParams.append(key, value);
    });

    return fetchData(() =>
      fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/analytics/overview?${searchParams}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
        },
      }).then(res => res.json())
    );
  },

  // Get platform analytics
  getPlatformAnalytics: async (platform: string, params: {
    start_date?: string;
    end_date?: string;
    limit?: number;
    page?: number;
  } = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) searchParams.append(key, value.toString());
    });

    return fetchData(() =>
      fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/analytics/platform/${platform}?${searchParams}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
        },
      }).then(res => res.json())
    );
  },

  // Get content analytics
  getContentAnalytics: async (contentId: string, params: {
    start_date?: string;
    end_date?: string;
  } = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) searchParams.append(key, value);
    });

    return fetchData(() =>
      fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/analytics/content/${contentId}?${searchParams}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
        },
      }).then(res => res.json())
    );
  },

  // Get performance metrics
  getPerformanceMetrics: async (params: {
    start_date?: string;
    end_date?: string;
    interval?: string;
  } = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) searchParams.append(key, value);
    });

    return fetchData(() =>
      fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/analytics/performance?${searchParams}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
        },
      }).then(res => res.json())
    );
  },

  // Get top performing content
  getTopContent: async (params: {
    limit?: number;
    platform?: string;
    metric?: string;
    timeframe?: string;
  } = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) searchParams.append(key, value.toString());
    });

    return fetchData(() =>
      fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/analytics/content/top?${searchParams}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
        },
      }).then(res => res.json())
    );
  },

  // Get real-time summary
  getRealTimeSummary: async () => {
    return fetchData(() =>
      fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/analytics/realtime`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
        },
      }).then(res => res.json())
    );
  },

  // Trigger analytics collection
  triggerCollection: async (daysBack: number = 7) => {
    return fetchData(() =>
      fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/analytics/collect?daysBack=${daysBack}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
        },
      }).then(res => res.json())
    );
  },

  // Get collection status
  getCollectionStatus: async () => {
    return fetchData(() =>
      fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/analytics/status`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
        },
      }).then(res => res.json())
    );
  },

  // Export analytics
  exportAnalytics: async (params: {
    format: 'csv' | 'json' | 'pdf';
    timeframe?: string;
    platforms?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) searchParams.append(key, value);
    });

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/analytics/export?${searchParams}`, {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
      },
    });

    if (!response.ok) {
      throw new Error('Export failed');
    }

    return response.blob();
  },
};

// Helper function to get auth token
function getAuthToken(): string {
  // This should be implemented based on your auth system
  // For now, return empty string
  return '';
}

// Workflow related API calls
export const workflowApi = {
  // Get user's workflows
  getUserWorkflows: async (userId: string) => {
    return fetchData(() =>
      supabase
        .from('workflows')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
    );
  },

  // Create new workflow
  createWorkflow: async (workflow: Database['public']['Tables']['workflows']['Insert']) => {
    return fetchData(() =>
      supabase
        .from('workflows')
        .insert(workflow)
        .select()
        .single()
    );
  },

  // Get workflow by ID
  getWorkflowById: async (workflowId: string) => {
    return fetchData(() =>
      supabase
        .from('workflows')
        .select('*')
        .eq('workflow_id', workflowId)
        .single()
    );
  },

  // Update workflow status
  updateWorkflowStatus: async (workflowId: string, status: string, completedAt?: string) => {
    const updates: Partial<Database['public']['Tables']['workflows']['Update']> = {
      status
    };

    if (completedAt) {
      updates.completed_at = completedAt;
    }

    return fetchData(() =>
      supabase
        .from('workflows')
        .update(updates)
        .eq('workflow_id', workflowId)
        .select()
        .single()
    );
  },
};

// Production related API calls
export const productionApi = {
  // Get productions by workflow ID
  getProductionsByWorkflow: async (workflowId: string) => {
    return fetchData(() =>
      supabase
        .from('productions')
        .select('*')
        .eq('workflow_id', workflowId)
        .order('created_at', { ascending: false })
    );
  },

  // Create new production
  createProduction: async (production: Database['public']['Tables']['productions']['Insert']) => {
    return fetchData(() =>
      supabase
        .from('productions')
        .insert(production)
        .select()
        .single()
    );
  },
};

// Publication related API calls
export const publicationApi = {
  // Get publications by production ID
  getPublicationsByProduction: async (productionId: string) => {
    return fetchData(() =>
      supabase
        .from('publications')
        .select('*')
        .eq('production_id', productionId)
        .order('published_at', { ascending: false })
    );
  },

  // Create new publication
  createPublication: async (publication: Database['public']['Tables']['publications']['Insert']) => {
    return fetchData(() =>
      supabase
        .from('publications')
        .insert(publication)
        .select()
        .single()
    );
  },

  // Update publication status
  updatePublicationStatus: async (publicationId: string, status: string, url?: string) => {
    const updates: Partial<Database['public']['Tables']['publications']['Update']> = {
      status
    };

    if (url) {
      updates.url = url;
    }

    return fetchData(() =>
      supabase
        .from('publications')
        .update(updates)
        .eq('publication_id', publicationId)
        .select()
        .single()
    );
  },
};
