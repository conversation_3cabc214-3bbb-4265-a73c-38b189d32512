import { supabase } from './supabaseClient';
import { 
  ContentItem, 
  ContentCreateInput, 
  ContentUpdateInput, 
  ContentFilterOptions,
  ContentSearchResult
} from '../types/content.types';

// Generic API response type
export interface ApiResponse<T> {
  data: T | null;
  error: Error | null;
  status: number;
}

// Generic fetch function with error handling
async function fetchData<T>(
  fetcher: () => Promise<{ data: T | null; error: any }>
): Promise<ApiResponse<T>> {
  try {
    const { data, error } = await fetcher();
    
    if (error) {
      console.error('Content API Error:', error);
      return {
        data: null,
        error: new Error(error.message || 'An error occurred'),
        status: error.status || 500,
      };
    }
    
    return {
      data,
      error: null,
      status: 200,
    };
  } catch (err: any) {
    console.error('Content Fetch Error:', err);
    return {
      data: null,
      error: new Error(err.message || 'An unexpected error occurred'),
      status: 500,
    };
  }
}

// Content API service
export const contentApi = {
  // Get all content for the current user
  getAllContent: async (userId: string, options?: ContentFilterOptions): Promise<ApiResponse<ContentItem[]>> => {
    let query = supabase
      .from('content')
      .select('*')
      .eq('user_id', userId);
    
    // Apply filters if provided
    if (options) {
      if (options.input_type) {
        query = query.eq('input_type', options.input_type);
      }
      
      if (options.status) {
        query = query.eq('status', options.status);
      }
      
      if (options.tags && options.tags.length > 0) {
        query = query.contains('tags', options.tags);
      }
      
      if (options.search) {
        query = query.or(`title.ilike.%${options.search}%,description.ilike.%${options.search}%`);
      }
      
      if (options.startDate) {
        query = query.gte('created_at', options.startDate);
      }
      
      if (options.endDate) {
        query = query.lte('created_at', options.endDate);
      }
      
      // Apply sorting
      const sortBy = options.sortBy || 'created_at';
      const sortOrder = options.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });
      
      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }
      
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }
    } else {
      // Default sorting by created_at desc
      query = query.order('created_at', { ascending: false });
    }
    
    return fetchData(() => query);
  },
  
  // Search content with pagination
  searchContent: async (
    userId: string, 
    options: ContentFilterOptions
  ): Promise<ApiResponse<ContentSearchResult>> => {
    try {
      // First, get the total count
      const countQuery = supabase
        .from('content')
        .select('content_id', { count: 'exact' })
        .eq('user_id', userId);
      
      // Apply the same filters as the main query
      if (options.input_type) {
        countQuery.eq('input_type', options.input_type);
      }
      
      if (options.status) {
        countQuery.eq('status', options.status);
      }
      
      if (options.tags && options.tags.length > 0) {
        countQuery.contains('tags', options.tags);
      }
      
      if (options.search) {
        countQuery.or(`title.ilike.%${options.search}%,description.ilike.%${options.search}%`);
      }
      
      if (options.startDate) {
        countQuery.gte('created_at', options.startDate);
      }
      
      if (options.endDate) {
        countQuery.lte('created_at', options.endDate);
      }
      
      const { count, error: countError } = await countQuery;
      
      if (countError) {
        throw new Error(countError.message);
      }
      
      // Now get the actual data with pagination
      const { data, error } = await contentApi.getAllContent(userId, options);
      
      if (error) {
        throw error;
      }
      
      const pageSize = options.limit || 10;
      const page = Math.floor((options.offset || 0) / pageSize) + 1;
      const totalPages = Math.ceil((count || 0) / pageSize);
      
      return {
        data: {
          items: data || [],
          total: count || 0,
          page,
          pageSize,
          totalPages
        },
        error: null,
        status: 200
      };
    } catch (err: any) {
      console.error('Content Search Error:', err);
      return {
        data: null,
        error: new Error(err.message || 'An unexpected error occurred'),
        status: 500,
      };
    }
  },
  
  // Get content by ID
  getContentById: async (contentId: string): Promise<ApiResponse<ContentItem>> => {
    return fetchData(() => 
      supabase
        .from('content')
        .select('*')
        .eq('content_id', contentId)
        .single()
    );
  },
  
  // Create new content
  createContent: async (userId: string, input: ContentCreateInput): Promise<ApiResponse<ContentItem>> => {
    const now = new Date().toISOString();
    
    return fetchData(() => 
      supabase
        .from('content')
        .insert({
          user_id: userId,
          title: input.title,
          input_type: input.input_type,
          content: input.content,
          description: input.description || null,
          tags: input.tags || [],
          metadata: input.metadata || null,
          status: 'draft',
          created_at: now,
          updated_at: now
        })
        .select()
        .single()
    );
  },
  
  // Update content
  updateContent: async (contentId: string, updates: ContentUpdateInput): Promise<ApiResponse<ContentItem>> => {
    const updateData = {
      ...updates,
      updated_at: new Date().toISOString()
    };
    
    return fetchData(() => 
      supabase
        .from('content')
        .update(updateData)
        .eq('content_id', contentId)
        .select()
        .single()
    );
  },
  
  // Delete content
  deleteContent: async (contentId: string): Promise<ApiResponse<null>> => {
    return fetchData(() => 
      supabase
        .from('content')
        .delete()
        .eq('content_id', contentId)
        .then(() => ({ data: null, error: null }))
    );
  },
  
  // Archive content (soft delete)
  archiveContent: async (contentId: string): Promise<ApiResponse<ContentItem>> => {
    return fetchData(() => 
      supabase
        .from('content')
        .update({
          status: 'archived',
          updated_at: new Date().toISOString()
        })
        .eq('content_id', contentId)
        .select()
        .single()
    );
  }
};
