"use client";

import { useState } from "react";
import { DragDropContext, Droppable, DropResult } from "@hello-pangea/dnd";
import WorkflowStep, { WorkflowStepType } from "./WorkflowStep";
import { Button } from "../ui/Button";
import { generateId } from "../../lib/utils";

interface WorkflowBuilderProps {
  initialSteps?: WorkflowStep[];
  onSave?: (steps: WorkflowStep[]) => void;
}

export interface WorkflowStep {
  id: string;
  type: WorkflowStepType;
  title: string;
  description: string;
  config: any;
}

const defaultStepConfigs: Record<WorkflowStepType, any> = {
  input: {
    inputType: "text",
    required: true,
  },
  transform: {
    transformType: "summarize",
    targetLanguage: "en",
  },
  review: {
    reviewer: "self",
    timeout: 24,
  },
  publish: {
    platform: "blog",
    schedule: "immediate",
    scheduledTime: "",
  },
  notify: {
    notificationType: "email",
    recipients: "",
  },
};

const stepTitles: Record<WorkflowStepType, string> = {
  input: "Content Input",
  transform: "Transform Content",
  review: "Review Content",
  publish: "Publish Content",
  notify: "Send Notification",
};

const stepDescriptions: Record<WorkflowStepType, string> = {
  input: "Add content to the workflow",
  transform: "Transform or modify content",
  review: "Review and approve content",
  publish: "Publish content to platform",
  notify: "Send notifications about content",
};

export default function WorkflowBuilder({
  initialSteps = [],
  onSave,
}: WorkflowBuilderProps) {
  const [steps, setSteps] = useState<WorkflowStep[]>(initialSteps);
  const [showStepMenu, setShowStepMenu] = useState(false);

  const handleDragEnd = (result: DropResult) => {
    const { destination, source } = result;

    // Dropped outside the list
    if (!destination) {
      return;
    }

    // Dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    const newSteps = Array.from(steps);
    const [removed] = newSteps.splice(source.index, 1);
    newSteps.splice(destination.index, 0, removed);

    setSteps(newSteps);
  };

  const addStep = (type: WorkflowStepType) => {
    const newStep: WorkflowStep = {
      id: generateId(),
      type,
      title: stepTitles[type],
      description: stepDescriptions[type],
      config: defaultStepConfigs[type],
    };

    setSteps([...steps, newStep]);
    setShowStepMenu(false);
  };

  const updateStep = (id: string, newConfig: any) => {
    setSteps(
      steps.map((step) =>
        step.id === id ? { ...step, config: newConfig } : step
      )
    );
  };

  const deleteStep = (id: string) => {
    setSteps(steps.filter((step) => step.id !== id));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(steps);
    }
  };

  return (
    <div className="space-y-4">
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="workflow-steps">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="space-y-4"
            >
              {steps.map((step, index) => (
                <WorkflowStep
                  key={step.id}
                  id={step.id}
                  type={step.type}
                  title={step.title}
                  description={step.description}
                  config={step.config}
                  index={index}
                  onUpdate={updateStep}
                  onDelete={deleteStep}
                />
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      <div className="relative">
        <Button
          onClick={() => setShowStepMenu(!showStepMenu)}
          variant="outline"
          className="w-full border-dashed"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          Add Workflow Step
        </Button>

        {showStepMenu && (
          <div className="absolute top-full left-0 right-0 mt-2 p-2 bg-card border border-border rounded-md shadow-md z-10">
            <div className="grid grid-cols-1 gap-2">
              <StepButton
                onClick={() => addStep("input")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                }
                title="Content Input"
                description="Add content to the workflow"
              />
              <StepButton
                onClick={() => addStep("transform")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                    />
                  </svg>
                }
                title="Transform Content"
                description="Transform or modify content"
              />
              <StepButton
                onClick={() => addStep("review")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                }
                title="Review Content"
                description="Review and approve content"
              />
              <StepButton
                onClick={() => addStep("publish")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    />
                  </svg>
                }
                title="Publish Content"
                description="Publish content to platform"
              />
              <StepButton
                onClick={() => addStep("notify")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                    />
                  </svg>
                }
                title="Send Notification"
                description="Send notifications about content"
              />
            </div>
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline">Cancel</Button>
        <Button onClick={handleSave}>Save Workflow</Button>
      </div>
    </div>
  );
}

interface StepButtonProps {
  onClick: () => void;
  icon: React.ReactNode;
  title: string;
  description: string;
}

function StepButton({ onClick, icon, title, description }: StepButtonProps) {
  return (
    <button
      onClick={onClick}
      className="flex items-center p-3 rounded-md hover:bg-muted/50 transition-colors w-full text-left"
    >
      <div className="mr-3 p-2 rounded-full bg-primary/10 text-primary">
        {icon}
      </div>
      <div>
        <h3 className="font-medium">{title}</h3>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
    </button>
  );
}
