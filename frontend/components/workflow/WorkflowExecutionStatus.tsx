"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "../ui/Card";
import { Button } from "../ui/Button";
import { Skeleton } from "../ui/Skeleton";
import { useToast } from "../../context/ToastContext";
import { formatDate } from "../../lib/utils";

interface WorkflowExecution {
  id: string;
  workflow_id: string;
  content_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  parameters?: any;
  results?: any;
  error?: string;
  started_at: string;
  completed_at?: string;
  n8n_execution_id?: string;
  n8n_data?: any;
}

interface WorkflowExecutionStatusProps {
  workflowId: string;
  executionId?: string;
  onExecutionComplete?: (execution: WorkflowExecution) => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export default function WorkflowExecutionStatus({
  workflowId,
  executionId,
  onExe<PERSON>ionComplete,
  autoRefresh = true,
  refreshInterval = 5000
}: WorkflowExecutionStatusProps) {
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCancelling, setIsCancelling] = useState<string | null>(null);
  const { showToast } = useToast();

  const fetchExecutions = async () => {
    try {
      const url = executionId 
        ? `/api/workflows/${workflowId}/status?execution_id=${executionId}`
        : `/api/workflows/${workflowId}/status`;
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch execution status');
      }

      const data = await response.json();
      
      if (executionId) {
        // Single execution
        setExecutions([data.execution]);
        
        // Check if execution completed and call callback
        if (data.execution.status === 'completed' && onExecutionComplete) {
          onExecutionComplete(data.execution);
        }
      } else {
        // Multiple executions
        setExecutions(data.executions || []);
      }
      
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  const cancelExecution = async (executionId: string) => {
    setIsCancelling(executionId);
    
    try {
      const response = await fetch(`/api/workflows/${workflowId}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ execution_id: executionId })
      });

      if (!response.ok) {
        throw new Error('Failed to cancel execution');
      }

      showToast("Execution cancelled", "The workflow execution has been cancelled.", "success");
      await fetchExecutions();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      showToast("Error cancelling execution", errorMessage, "error");
    } finally {
      setIsCancelling(null);
    }
  };

  useEffect(() => {
    fetchExecutions();
  }, [workflowId, executionId]);

  useEffect(() => {
    if (!autoRefresh) return;

    const hasRunningExecutions = executions.some(
      exec => exec.status === 'pending' || exec.status === 'running'
    );

    if (!hasRunningExecutions) return;

    const interval = setInterval(fetchExecutions, refreshInterval);
    return () => clearInterval(interval);
  }, [executions, autoRefresh, refreshInterval]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'running':
        return 'text-blue-600 bg-blue-100';
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      case 'cancelled':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'running':
        return (
          <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        );
      case 'completed':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'failed':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'cancelled':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636" />
          </svg>
        );
      default:
        return null;
    }
  };

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = Math.floor((end.getTime() - start.getTime()) / 1000);
    
    if (duration < 60) {
      return `${duration}s`;
    } else if (duration < 3600) {
      return `${Math.floor(duration / 60)}m ${duration % 60}s`;
    } else {
      const hours = Math.floor(duration / 3600);
      const minutes = Math.floor((duration % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p className="font-medium">Error loading execution status</p>
            <p className="text-sm mt-1">{error}</p>
            <Button 
              onClick={fetchExecutions} 
              variant="outline" 
              size="sm"
              className="mt-4"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Workflow Execution Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                <Skeleton className="w-8 h-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <Skeleton className="h-6 w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (executions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Workflow Execution Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <p>No executions found for this workflow.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          Workflow Execution Status
          {executions.some(exec => exec.status === 'running' || exec.status === 'pending') && (
            <span className="ml-2 text-sm font-normal text-muted-foreground">
              (Auto-refreshing every {refreshInterval / 1000}s)
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {executions.map((execution) => (
            <div key={execution.id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(execution.status)}`}>
                    {getStatusIcon(execution.status)}
                    <span className="capitalize">{execution.status}</span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    ID: {execution.id.slice(0, 8)}...
                  </span>
                </div>
                
                {(execution.status === 'pending' || execution.status === 'running') && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => cancelExecution(execution.id)}
                    disabled={isCancelling === execution.id}
                  >
                    {isCancelling === execution.id ? 'Cancelling...' : 'Cancel'}
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-muted-foreground">Started:</span>
                  <p>{formatDate(execution.started_at)}</p>
                </div>
                
                {execution.completed_at && (
                  <div>
                    <span className="font-medium text-muted-foreground">Completed:</span>
                    <p>{formatDate(execution.completed_at)}</p>
                  </div>
                )}
                
                <div>
                  <span className="font-medium text-muted-foreground">Duration:</span>
                  <p>{formatDuration(execution.started_at, execution.completed_at)}</p>
                </div>
              </div>

              {execution.error && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <span className="font-medium text-red-800">Error:</span>
                  <p className="text-red-700 text-sm mt-1">{execution.error}</p>
                </div>
              )}

              {execution.results && (
                <div className="mt-3">
                  <details className="group">
                    <summary className="cursor-pointer font-medium text-muted-foreground hover:text-foreground">
                      View Results
                      <span className="ml-2 group-open:rotate-90 transition-transform">▶</span>
                    </summary>
                    <div className="mt-2 p-3 bg-muted rounded-lg">
                      <pre className="text-xs overflow-x-auto">
                        {JSON.stringify(execution.results, null, 2)}
                      </pre>
                    </div>
                  </details>
                </div>
              )}

              {execution.n8n_execution_id && (
                <div className="mt-3 text-xs text-muted-foreground">
                  n8n Execution ID: {execution.n8n_execution_id}
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
