"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { DragDropContext, Droppable, Draggable, DropResult } from "@hello-pangea/dnd";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "../ui/Card";
import { Input } from "../ui/Input";
import { Button } from "../ui/Button";
import { useWorkflow, WorkflowStep } from "../../context/WorkflowContext";
import { useToast } from "../../context/ToastContext";
import WorkflowStepEditor from "./WorkflowStepEditor";

interface WorkflowEditorProps {
  id?: string; // If provided, we're editing an existing workflow
}

export default function WorkflowEditor({ id }: WorkflowEditorProps) {
  const router = useRouter();
  const {
    workflows,
    isLoading,
    error,
    fetchWorkflows,
    getWorkflow,
    createWorkflow,
    updateWorkflow,
    addStep,
    deleteStep,
    reorderSteps
  } = useWorkflow();
  const { showToast } = useToast();

  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [platforms, setPlatforms] = useState<string[]>([]);
  const [steps, setSteps] = useState<WorkflowStep[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formLoaded, setFormLoaded] = useState(false);
  const [showAddStep, setShowAddStep] = useState(false);

  // Load workflow data if editing
  useEffect(() => {
    fetchWorkflows();
  }, [fetchWorkflows]);

  useEffect(() => {
    if (id && workflows.length > 0 && !formLoaded) {
      const workflow = getWorkflow(id);
      if (workflow) {
        setName(workflow.name);
        setDescription(workflow.description);
        setPlatforms(workflow.platforms);
        setSteps(workflow.steps);
        setFormLoaded(true);
      }
    }
  }, [id, workflows, getWorkflow, formLoaded]);

  const handlePlatformChange = (platform: string) => {
    if (platforms.includes(platform)) {
      setPlatforms(platforms.filter(p => p !== platform));
    } else {
      setPlatforms([...platforms, platform]);
    }
  };

  const handleAddStep = async (step: Omit<WorkflowStep, "id" | "position">) => {
    if (id) {
      const { data, error } = await addStep(id, step);
      if (error) {
        showToast("Error adding step", error, "error");
      } else if (data) {
        setSteps([...steps, data]);
        showToast("Step added", "The workflow step has been added successfully.", "success");
        setShowAddStep(false);
      }
    } else {
      // For new workflows, just add to local state
      const newStep: WorkflowStep = {
        ...step,
        id: `temp-${Math.random().toString(36).substring(2, 9)}`,
        position: steps.length,
      };
      setSteps([...steps, newStep]);
      setShowAddStep(false);
    }
  };

  const handleDeleteStep = async (stepId: string) => {
    if (confirm("Are you sure you want to delete this step? This action cannot be undone.")) {
      if (id) {
        const { error } = await deleteStep(id, stepId);
        if (error) {
          showToast("Error deleting step", error, "error");
        } else {
          setSteps(steps.filter(step => step.id !== stepId));
          showToast("Step deleted", "The workflow step has been deleted successfully.", "success");
        }
      } else {
        // For new workflows, just remove from local state
        setSteps(steps.filter(step => step.id !== stepId));
      }
    }
  };

  const handleDragEnd = async (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(steps);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update positions
    const updatedItems = items.map((item, index) => ({
      ...item,
      position: index,
    }));

    setSteps(updatedItems);

    if (id) {
      const stepIds = updatedItems.map(item => item.id);
      const { error } = await reorderSteps(id, stepIds);
      if (error) {
        showToast("Error reordering steps", error, "error");
        // Revert to original order
        fetchWorkflows();
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const workflowData = {
        name,
        description,
        platforms,
        steps,
      };

      if (id) {
        // Update existing workflow
        const { data, error } = await updateWorkflow(id, workflowData);

        if (error) {
          showToast("Error updating workflow", error, "error");
        } else {
          showToast(
            "Workflow updated successfully",
            "Your workflow has been updated.",
            "success"
          );

          router.push(`/workflows/${id}`);
        }
      } else {
        // Create new workflow
        const { data, error } = await createWorkflow(workflowData);

        if (error) {
          showToast("Error creating workflow", error, "error");
        } else {
          showToast(
            "Workflow created successfully",
            "Your workflow has been created.",
            "success"
          );

          router.push(`/workflows/${data?.id}`);
        }
      }
    } catch (err: any) {
      showToast("Error saving workflow", err.message || "An unexpected error occurred", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading && id && !formLoaded) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Edit Workflow</h1>
        </div>
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Loading Workflow...</CardTitle>
            <CardDescription>Please wait while we load your workflow.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center">
              <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error && id) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Edit Workflow</h1>
        </div>
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Error Loading Workflow</CardTitle>
            <CardDescription>There was an error loading the workflow.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="p-6 border border-error/50 bg-error/10 rounded-lg text-error">
              <p>{error}</p>
              <Button
                onClick={() => fetchWorkflows()}
                variant="outline"
                className="mt-4"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="sm"
            className="mr-4"
            onClick={() => router.back()}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Back
          </Button>
          <h1 className="text-3xl font-bold">{id ? "Edit Workflow" : "Create Workflow"}</h1>
        </div>
      </div>

      <Card className="max-w-4xl mx-auto">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>{id ? "Edit Workflow" : "Create Workflow"}</CardTitle>
            <CardDescription>
              {id ? "Update your workflow information" : "Create a new workflow to automate your content process"}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Basic Information</h3>
              <Input
                id="name"
                label="Workflow Name"
                placeholder="Enter a name for your workflow"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
              <div className="space-y-2">
                <label htmlFor="description" className="block text-sm font-medium">
                  Description
                </label>
                <textarea
                  id="description"
                  className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  rows={3}
                  placeholder="Enter a brief description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>
            </div>

            {/* Platforms */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Target Platforms</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {["instagram", "twitter", "facebook", "linkedin", "youtube", "blog"].map((platform) => (
                  <button
                    key={platform}
                    type="button"
                    className={`px-3 py-2 rounded-md text-sm ${
                      platforms.includes(platform)
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted hover:bg-muted/80"
                    }`}
                    onClick={() => handlePlatformChange(platform)}
                  >
                    {platform.charAt(0).toUpperCase() + platform.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Workflow Steps */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Workflow Steps</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAddStep(true)}
                >
                  Add Step
                </Button>
              </div>

              {showAddStep && (
                <Card className="border-dashed">
                  <CardHeader>
                    <CardTitle className="text-lg">Add New Step</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <WorkflowStepEditor
                      onSave={handleAddStep}
                      onCancel={() => setShowAddStep(false)}
                    />
                  </CardContent>
                </Card>
              )}

              {steps.length === 0 ? (
                <div className="p-8 border border-dashed rounded-lg text-center">
                  <p className="text-muted-foreground">No steps added yet. Click "Add Step" to start building your workflow.</p>
                </div>
              ) : (
                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="workflow-steps">
                    {(provided) => (
                      <div
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        className="space-y-4"
                      >
                        {steps.map((step, index) => (
                          <Draggable key={step.id} draggableId={step.id} index={index}>
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                className="border rounded-lg p-4 bg-card"
                              >
                                <div className="flex justify-between items-start">
                                  <div className="flex items-start space-x-3">
                                    <div
                                      {...provided.dragHandleProps}
                                      className="p-2 rounded-md hover:bg-muted cursor-move"
                                    >
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-5 w-5 text-muted-foreground"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M4 6h16M4 12h16M4 18h16"
                                        />
                                      </svg>
                                    </div>
                                    <div>
                                      <h4 className="font-medium">{step.name}</h4>
                                      <p className="text-sm text-muted-foreground">{step.description}</p>
                                      <div className="mt-2">
                                        <span className="px-2 py-1 bg-muted rounded-full text-xs">
                                          {step.type}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeleteStep(step.id)}
                                    className="text-muted-foreground hover:text-error"
                                  >
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="h-5 w-5"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                      stroke="currentColor"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                      />
                                    </svg>
                                  </Button>
                                </div>
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || name === "" || steps.length === 0}
            >
              {isSubmitting ? "Saving..." : id ? "Save Changes" : "Create Workflow"}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
