"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } from "../ui/Card";
import { Button } from "../ui/Button";
import { Skeleton } from "../ui/Skeleton";
import { useToast } from "../../context/ToastContext";

interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  source: 'database' | 'file' | 'n8n';
  tags?: string[];
  nodes?: any[];
  connections?: any;
  filename?: string;
  loadedAt?: string;
  exportedAt?: string;
}

interface TemplateStats {
  total: number;
  bySource: Record<string, number>;
  byType: Record<string, number>;
  recentlyAdded: WorkflowTemplate[];
}

interface WorkflowTemplateBrowserProps {
  onTemplateSelect?: (template: WorkflowTemplate) => void;
  onTemplateClone?: (template: WorkflowTemplate) => void;
  showActions?: boolean;
}

export default function WorkflowTemplateBrowser({
  onTemplateSelect,
  onTemplateClone,
  showActions = true
}: WorkflowTemplateBrowserProps) {
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [stats, setStats] = useState<TemplateStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [isCloning, setIsCloning] = useState<string | null>(null);
  const [isImporting, setIsImporting] = useState<string | null>(null);
  const { showToast } = useToast();

  const fetchTemplates = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/workflows/templates', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch templates');
      }

      const data = await response.json();
      setTemplates(data.templates || []);
      setStats(data.stats || null);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  const cloneTemplate = async (template: WorkflowTemplate) => {
    setIsCloning(template.id);
    
    try {
      const response = await fetch(`/api/workflows/templates/${template.id}/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          name: `${template.name} (Copy)`,
          description: `Cloned from ${template.source} template`,
          source: template.source
        })
      });

      if (!response.ok) {
        throw new Error('Failed to clone template');
      }

      const data = await response.json();
      showToast("Template cloned", "The template has been successfully cloned to your workflows.", "success");
      
      if (onTemplateClone) {
        onTemplateClone(template);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      showToast("Error cloning template", errorMessage, "error");
    } finally {
      setIsCloning(null);
    }
  };

  const importToN8n = async (template: WorkflowTemplate) => {
    setIsImporting(template.id);
    
    try {
      const response = await fetch(`/api/workflows/templates/${template.id}/import`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          name: `[ContentForge] ${template.name}`,
          active: false,
          tags: ['contentforge', 'imported', template.id]
        })
      });

      if (!response.ok) {
        throw new Error('Failed to import template to n8n');
      }

      showToast("Template imported", "The template has been successfully imported to n8n.", "success");
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      showToast("Error importing template", errorMessage, "error");
    } finally {
      setIsImporting(null);
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  const getSourceBadgeColor = (source: string) => {
    switch (source) {
      case 'database':
        return 'bg-blue-100 text-blue-800';
      case 'file':
        return 'bg-green-100 text-green-800';
      case 'n8n':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTemplateTypeFromName = (name: string) => {
    const lowerName = name.toLowerCase();
    if (lowerName.includes('blog')) return 'blog';
    if (lowerName.includes('social')) return 'social';
    if (lowerName.includes('video')) return 'video';
    if (lowerName.includes('image')) return 'image';
    return 'other';
  };

  // Filter templates
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = searchTerm === '' || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.description && template.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    if (filter === 'all') return matchesSearch;
    
    if (filter === 'source') {
      return matchesSearch; // Will be further filtered by source buttons
    }
    
    const templateType = getTemplateTypeFromName(template.name);
    return matchesSearch && templateType === filter;
  });

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p className="font-medium">Error loading templates</p>
            <p className="text-sm mt-1">{error}</p>
            <Button 
              onClick={fetchTemplates} 
              variant="outline" 
              size="sm"
              className="mt-4"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{stats.total}</div>
              <div className="text-sm text-muted-foreground">Total Templates</div>
            </CardContent>
          </Card>
          
          {Object.entries(stats.bySource).map(([source, count]) => (
            <Card key={source}>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">{count}</div>
                <div className="text-sm text-muted-foreground capitalize">{source} Templates</div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Button
                variant={filter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter('all')}
              >
                All
              </Button>
              <Button
                variant={filter === 'blog' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter('blog')}
              >
                Blog
              </Button>
              <Button
                variant={filter === 'social' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter('social')}
              >
                Social
              </Button>
              <Button
                variant={filter === 'video' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter('video')}
              >
                Video
              </Button>
              <Button
                variant={filter === 'image' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter('image')}
              >
                Image
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <Skeleton className="h-6 w-2/3" />
                  <Skeleton className="h-5 w-16 rounded-full" />
                </div>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4 mb-4" />
                <div className="flex gap-2">
                  <Skeleton className="h-5 w-12 rounded-full" />
                  <Skeleton className="h-5 w-16 rounded-full" />
                </div>
              </CardContent>
              <CardFooter>
                <Skeleton className="h-8 w-full" />
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : filteredTemplates.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">No templates match your search criteria.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTemplates.map((template) => (
            <Card key={template.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSourceBadgeColor(template.source)}`}>
                    {template.source}
                  </span>
                </div>
              </CardHeader>
              
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                  {template.description || 'No description available'}
                </p>
                
                {template.tags && template.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-4">
                    {template.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-muted rounded-full text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                    {template.tags.length > 3 && (
                      <span className="px-2 py-1 bg-muted rounded-full text-xs">
                        +{template.tags.length - 3} more
                      </span>
                    )}
                  </div>
                )}
                
                {template.nodes && (
                  <div className="text-xs text-muted-foreground">
                    {template.nodes.length} workflow steps
                  </div>
                )}
              </CardContent>
              
              {showActions && (
                <CardFooter className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onTemplateSelect?.(template)}
                    className="flex-1"
                  >
                    View
                  </Button>
                  
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => cloneTemplate(template)}
                    disabled={isCloning === template.id}
                    className="flex-1"
                  >
                    {isCloning === template.id ? 'Cloning...' : 'Clone'}
                  </Button>
                  
                  {template.source === 'file' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => importToN8n(template)}
                      disabled={isImporting === template.id}
                      title="Import to n8n"
                    >
                      {isImporting === template.id ? '...' : '↗'}
                    </Button>
                  )}
                </CardFooter>
              )}
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
