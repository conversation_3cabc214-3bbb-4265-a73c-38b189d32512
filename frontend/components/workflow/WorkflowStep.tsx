"use client";

import { useState } from "react";
import { Draggable } from "@hello-pangea/dnd";
import { cn } from "../../lib/utils";

export type WorkflowStepType =
  | "input"
  | "transform"
  | "review"
  | "publish"
  | "notify";

export interface WorkflowStepProps {
  id: string;
  type: WorkflowStepType;
  title: string;
  description: string;
  config: any;
  index: number;
  onUpdate: (id: string, config: any) => void;
  onDelete: (id: string) => void;
}

export default function WorkflowStep({
  id,
  type,
  title,
  description,
  config,
  index,
  onUpdate,
  onDelete,
}: WorkflowStepProps) {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [stepConfig, setStepConfig] = useState(config);

  const handleConfigChange = (newConfig: any) => {
    setStepConfig(newConfig);
    onUpdate(id, newConfig);
  };

  const getStepIcon = () => {
    switch (type) {
      case "input":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        );
      case "transform":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
            />
          </svg>
        );
      case "review":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
      case "publish":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            />
          </svg>
        );
      case "notify":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
            />
          </svg>
        );
      default:
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
        );
    }
  };

  const renderConfigPanel = () => {
    switch (type) {
      case "input":
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium mb-1">
                Input Type
              </label>
              <select
                className="w-full p-2 border border-border rounded-md"
                value={stepConfig.inputType}
                onChange={(e) =>
                  handleConfigChange({
                    ...stepConfig,
                    inputType: e.target.value,
                  })
                }
              >
                <option value="text">Text</option>
                <option value="document">Document</option>
                <option value="image">Image</option>
                <option value="video">Video</option>
                <option value="audio">Audio</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                Required
              </label>
              <input
                type="checkbox"
                checked={stepConfig.required}
                onChange={(e) =>
                  handleConfigChange({
                    ...stepConfig,
                    required: e.target.checked,
                  })
                }
              />
            </div>
          </div>
        );
      case "transform":
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium mb-1">
                Transformation Type
              </label>
              <select
                className="w-full p-2 border border-border rounded-md"
                value={stepConfig.transformType}
                onChange={(e) =>
                  handleConfigChange({
                    ...stepConfig,
                    transformType: e.target.value,
                  })
                }
              >
                <option value="resize">Resize</option>
                <option value="format">Format Conversion</option>
                <option value="summarize">Summarize</option>
                <option value="translate">Translate</option>
                <option value="transcribe">Transcribe</option>
              </select>
            </div>
            {stepConfig.transformType === "translate" && (
              <div>
                <label className="block text-sm font-medium mb-1">
                  Target Language
                </label>
                <select
                  className="w-full p-2 border border-border rounded-md"
                  value={stepConfig.targetLanguage}
                  onChange={(e) =>
                    handleConfigChange({
                      ...stepConfig,
                      targetLanguage: e.target.value,
                    })
                  }
                >
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="ja">Japanese</option>
                </select>
              </div>
            )}
          </div>
        );
      case "review":
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium mb-1">
                Reviewer
              </label>
              <select
                className="w-full p-2 border border-border rounded-md"
                value={stepConfig.reviewer}
                onChange={(e) =>
                  handleConfigChange({
                    ...stepConfig,
                    reviewer: e.target.value,
                  })
                }
              >
                <option value="self">Self</option>
                <option value="team">Team</option>
                <option value="manager">Manager</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                Timeout (hours)
              </label>
              <input
                type="number"
                className="w-full p-2 border border-border rounded-md"
                value={stepConfig.timeout}
                onChange={(e) =>
                  handleConfigChange({
                    ...stepConfig,
                    timeout: parseInt(e.target.value),
                  })
                }
                min="1"
                max="72"
              />
            </div>
          </div>
        );
      case "publish":
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium mb-1">
                Platform
              </label>
              <select
                className="w-full p-2 border border-border rounded-md"
                value={stepConfig.platform}
                onChange={(e) =>
                  handleConfigChange({
                    ...stepConfig,
                    platform: e.target.value,
                  })
                }
              >
                <option value="youtube">YouTube</option>
                <option value="instagram">Instagram</option>
                <option value="linkedin">LinkedIn</option>
                <option value="twitter">Twitter</option>
                <option value="blog">Blog</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                Schedule
              </label>
              <select
                className="w-full p-2 border border-border rounded-md"
                value={stepConfig.schedule}
                onChange={(e) =>
                  handleConfigChange({
                    ...stepConfig,
                    schedule: e.target.value,
                  })
                }
              >
                <option value="immediate">Immediate</option>
                <option value="scheduled">Scheduled</option>
              </select>
            </div>
            {stepConfig.schedule === "scheduled" && (
              <div>
                <label className="block text-sm font-medium mb-1">
                  Date/Time
                </label>
                <input
                  type="datetime-local"
                  className="w-full p-2 border border-border rounded-md"
                  value={stepConfig.scheduledTime}
                  onChange={(e) =>
                    handleConfigChange({
                      ...stepConfig,
                      scheduledTime: e.target.value,
                    })
                  }
                />
              </div>
            )}
          </div>
        );
      case "notify":
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium mb-1">
                Notification Type
              </label>
              <select
                className="w-full p-2 border border-border rounded-md"
                value={stepConfig.notificationType}
                onChange={(e) =>
                  handleConfigChange({
                    ...stepConfig,
                    notificationType: e.target.value,
                  })
                }
              >
                <option value="email">Email</option>
                <option value="slack">Slack</option>
                <option value="app">In-App</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                Recipients
              </label>
              <input
                type="text"
                className="w-full p-2 border border-border rounded-md"
                value={stepConfig.recipients}
                onChange={(e) =>
                  handleConfigChange({
                    ...stepConfig,
                    recipients: e.target.value,
                  })
                }
                placeholder="Comma-separated list"
              />
            </div>
          </div>
        );
      default:
        return <div>No configuration options available</div>;
    }
  };

  return (
    <Draggable draggableId={id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={cn(
            "mb-4 border rounded-md bg-card transition-shadow",
            snapshot.isDragging ? "shadow-lg" : "shadow-sm"
          )}
        >
          <div className="p-4">
            <div className="flex items-center">
              <div
                {...provided.dragHandleProps}
                className="mr-3 text-muted-foreground hover:text-foreground cursor-grab"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 8h16M4 16h16"
                  />
                </svg>
              </div>
              <div className="mr-3 p-2 rounded-full bg-primary/10 text-primary">
                {getStepIcon()}
              </div>
              <div className="flex-1">
                <h3 className="font-medium">{title}</h3>
                <p className="text-sm text-muted-foreground">{description}</p>
              </div>
              <div className="flex space-x-1">
                <button
                  onClick={() => setIsConfigOpen(!isConfigOpen)}
                  className="p-1 rounded-md hover:bg-muted transition-colors"
                  aria-label="Configure"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </button>
                <button
                  onClick={() => onDelete(id)}
                  className="p-1 rounded-md hover:bg-muted transition-colors text-muted-foreground hover:text-error"
                  aria-label="Delete"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                </button>
              </div>
            </div>

            {isConfigOpen && (
              <div className="mt-4 pt-4 border-t border-border">
                {renderConfigPanel()}
              </div>
            )}
          </div>
        </div>
      )}
    </Draggable>
  );
}
