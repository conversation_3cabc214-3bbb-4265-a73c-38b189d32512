"use client";

import { But<PERSON> } from "../ui/Button";

interface AnalyticsControlsProps {
  onRefresh: () => void;
  onExport: () => void;
  isLoading: boolean;
  isExporting: boolean;
}

export default function AnalyticsControls({ 
  onRefresh, 
  onExport, 
  isLoading, 
  isExporting 
}: AnalyticsControlsProps) {
  return (
    <div className="flex items-center space-x-2">
      {/* Refresh Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={onRefresh}
        disabled={isLoading}
        className="flex items-center space-x-1"
      >
        <svg 
          className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
          />
        </svg>
        <span>Refresh</span>
      </Button>

      {/* Export Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={onExport}
        disabled={isExporting}
        className="flex items-center space-x-1"
      >
        <svg 
          className={`w-4 h-4 ${isExporting ? 'animate-pulse' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
          />
        </svg>
        <span>{isExporting ? 'Exporting...' : 'Export'}</span>
      </Button>
    </div>
  );
}
