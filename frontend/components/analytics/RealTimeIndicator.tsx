"use client";

import { Button } from "../ui/Button";

interface RealTimeIndicatorProps {
  isConnected: boolean;
  lastUpdate: Date | null;
  onToggle: () => void;
}

export default function RealTimeIndicator({ 
  isConnected, 
  lastUpdate, 
  onToggle 
}: RealTimeIndicatorProps) {
  const formatLastUpdate = (date: Date | null) => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    
    if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffMinutes < 60) {
      return `${diffMinutes}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="flex items-center space-x-3">
      {/* Connection Status Indicator */}
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${
          isConnected ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
        }`} />
        <span className={`text-sm font-medium ${
          isConnected ? 'text-green-600' : 'text-gray-500'
        }`}>
          {isConnected ? 'Live' : 'Offline'}
        </span>
      </div>

      {/* Last Update Time */}
      {lastUpdate && (
        <div className="text-xs text-gray-500">
          Updated {formatLastUpdate(lastUpdate)}
        </div>
      )}

      {/* Toggle Button */}
      <Button
        variant={isConnected ? "outline" : "default"}
        size="sm"
        onClick={onToggle}
        className={`text-xs ${
          isConnected 
            ? 'border-green-500 text-green-600 hover:bg-green-50' 
            : 'bg-green-600 hover:bg-green-700'
        }`}
      >
        {isConnected ? (
          <>
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            Disconnect
          </>
        ) : (
          <>
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            Go Live
          </>
        )}
      </Button>
    </div>
  );
}
