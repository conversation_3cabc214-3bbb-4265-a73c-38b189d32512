"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/Button";
import { <PERSON>, <PERSON>Header, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "../ui/Card";
import { Skeleton } from "../ui/Skeleton";
import { useAnalytics } from "../../context/AnalyticsContext";
import { useRealTimeAnalytics } from "../../hooks/useRealTimeAnalytics";
import OverviewStats from "./OverviewStats";
import EngagementChart from "./EngagementChart";
import ContentPerformance from "./ContentPerformance";
import PlatformDistribution from "./PlatformDistribution";
import ContentTypeDistribution from "./ContentTypeDistribution";
import RealTimeIndicator from "./RealTimeIndicator";
import AnalyticsControls from "./AnalyticsControls";

export default function AnalyticsDashboard() {
  const {
    analyticsData,
    isLoading,
    error,
    fetchAnalytics,
    timeframe,
    refreshAnalytics,
    enableRealTimeUpdates,
    disableRealTimeUpdates,
    isRealTimeEnabled,
    exportAnalytics
  } = useAnalytics();

  const {
    isConnected: isRealTimeConnected,
    summary: realTimeSummary,
    lastUpdate,
    connect: connectRealTime,
    disconnect: disconnectRealTime
  } = useRealTimeAnalytics({
    onUpdate: (data) => {
      // Refresh analytics when real-time update is received
      refreshAnalytics();
    }
  });

  const [showExportModal, setShowExportModal] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  const handleTimeframeChange = (newTimeframe: string) => {
    fetchAnalytics(newTimeframe);
  };

  const handleRealTimeToggle = () => {
    if (isRealTimeEnabled) {
      disableRealTimeUpdates();
      disconnectRealTime();
    } else {
      enableRealTimeUpdates();
      connectRealTime();
    }
  };

  const handleExport = async (format: 'csv' | 'json' | 'pdf') => {
    setIsExporting(true);
    try {
      await exportAnalytics(format);
      setShowExportModal(false);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleRefresh = () => {
    refreshAnalytics();
  };

  if (error) {
    return (
      <div className="p-6 border border-error/50 bg-error/10 rounded-lg text-error">
        <h3 className="font-medium mb-2">Error loading analytics</h3>
        <p>{error}</p>
        <Button
          onClick={() => fetchAnalytics()}
          variant="outline"
          className="mt-4"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
          <RealTimeIndicator
            isConnected={isRealTimeConnected}
            lastUpdate={lastUpdate}
            onToggle={handleRealTimeToggle}
          />
        </div>

        <div className="flex items-center space-x-4">
          {/* Controls */}
          <AnalyticsControls
            onRefresh={handleRefresh}
            onExport={() => setShowExportModal(true)}
            isLoading={isLoading}
            isExporting={isExporting}
          />

          {/* Timeframe Selector */}
          <div className="inline-flex rounded-md shadow-sm">
          <Button
            variant={timeframe === "7d" ? "default" : "outline"}
            size="sm"
            onClick={() => handleTimeframeChange("7d")}
            className="rounded-l-md rounded-r-none"
          >
            7 Days
          </Button>
          <Button
            variant={timeframe === "30d" ? "default" : "outline"}
            size="sm"
            onClick={() => handleTimeframeChange("30d")}
            className="rounded-none border-l-0 border-r-0"
          >
            30 Days
          </Button>
          <Button
            variant={timeframe === "90d" ? "default" : "outline"}
            size="sm"
            onClick={() => handleTimeframeChange("90d")}
            className="rounded-none border-r-0"
          >
            90 Days
          </Button>
          <Button
            variant={timeframe === "1y" ? "default" : "outline"}
            size="sm"
            onClick={() => handleTimeframeChange("1y")}
            className="rounded-l-none rounded-r-md"
          >
            1 Year
          </Button>
          </div>
        </div>
      </div>

      {/* Overview Stats */}
      <OverviewStats isLoading={isLoading} analyticsData={analyticsData} />

      {/* Engagement Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Engagement Trend</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="h-80">
              <Skeleton className="h-full w-full" />
            </div>
          ) : analyticsData ? (
            <EngagementChart data={analyticsData.engagementTrend} />
          ) : (
            <div className="h-80 flex items-center justify-center">
              <p className="text-muted-foreground">No data available</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Content Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Content</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                  <Skeleton className="h-8 w-20" />
                </div>
              ))}
            </div>
          ) : analyticsData ? (
            <ContentPerformance data={analyticsData.contentPerformance} />
          ) : (
            <div className="h-80 flex items-center justify-center">
              <p className="text-muted-foreground">No data available</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Distribution Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Platform Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-64">
                <Skeleton className="h-full w-full rounded-full" />
              </div>
            ) : analyticsData ? (
              <PlatformDistribution data={analyticsData.platformDistribution} />
            ) : (
              <div className="h-64 flex items-center justify-center">
                <p className="text-muted-foreground">No data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Content Type Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-64">
                <Skeleton className="h-full w-full rounded-full" />
              </div>
            ) : analyticsData ? (
              <ContentTypeDistribution data={analyticsData.contentTypeDistribution} />
            ) : (
              <div className="h-64 flex items-center justify-center">
                <p className="text-muted-foreground">No data available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
