"use client";

import { useState } from "react";
import { DragDropContext, Droppable, DropResult } from "@hello-pangea/dnd";
import ContentBlock, { ContentBlockType } from "./ContentBlock";
import { Button } from "../ui/Button";
import { generateId } from "../../lib/utils";

interface ContentEditorProps {
  initialContent?: ContentItem[];
  onSave?: (content: ContentItem[]) => void;
}

export interface ContentItem {
  id: string;
  type: ContentBlockType;
  content: any;
}

const defaultBlocks: Record<ContentBlockType, any> = {
  text: { text: "" },
  heading: { text: "", level: "h2" },
  image: { url: "", alt: "" },
  video: { url: "", caption: "" },
  quote: { text: "", source: "" },
  list: { items: [""], style: "bullet" },
  divider: {},
};

export default function ContentEditor({
  initialContent = [],
  onSave,
}: ContentEditorProps) {
  const [content, setContent] = useState<ContentItem[]>(initialContent);
  const [showBlockMenu, setShowBlockMenu] = useState(false);

  const handleDragEnd = (result: DropResult) => {
    const { destination, source } = result;

    // Dropped outside the list
    if (!destination) {
      return;
    }

    // Dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    const newContent = Array.from(content);
    const [removed] = newContent.splice(source.index, 1);
    newContent.splice(destination.index, 0, removed);

    setContent(newContent);
  };

  const addBlock = (type: ContentBlockType) => {
    const newBlock: ContentItem = {
      id: generateId(),
      type,
      content: defaultBlocks[type],
    };

    setContent([...content, newBlock]);
    setShowBlockMenu(false);
  };

  const updateBlock = (id: string, newContent: any) => {
    setContent(
      content.map((item) =>
        item.id === id ? { ...item, content: newContent } : item
      )
    );
  };

  const deleteBlock = (id: string) => {
    setContent(content.filter((item) => item.id !== id));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(content);
    }
  };

  return (
    <div className="space-y-4">
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="content-blocks">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="space-y-4"
            >
              {content.map((item, index) => (
                <ContentBlock
                  key={item.id}
                  id={item.id}
                  type={item.type}
                  content={item.content}
                  index={index}
                  onUpdate={updateBlock}
                  onDelete={deleteBlock}
                />
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      <div className="relative">
        <Button
          onClick={() => setShowBlockMenu(!showBlockMenu)}
          variant="outline"
          className="w-full border-dashed"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          Add Content Block
        </Button>

        {showBlockMenu && (
          <div className="absolute top-full left-0 right-0 mt-2 p-2 bg-card border border-border rounded-md shadow-md z-10">
            <div className="grid grid-cols-2 gap-2">
              <BlockButton
                onClick={() => addBlock("text")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16m-7 6h7"
                    />
                  </svg>
                }
                label="Text"
              />
              <BlockButton
                onClick={() => addBlock("heading")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h8m-8 6h16"
                    />
                  </svg>
                }
                label="Heading"
              />
              <BlockButton
                onClick={() => addBlock("image")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                }
                label="Image"
              />
              <BlockButton
                onClick={() => addBlock("video")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                }
                label="Video"
              />
              <BlockButton
                onClick={() => addBlock("quote")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
                    />
                  </svg>
                }
                label="Quote"
              />
              <BlockButton
                onClick={() => addBlock("list")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 10h16M4 14h16M4 18h16"
                    />
                  </svg>
                }
                label="List"
              />
              <BlockButton
                onClick={() => addBlock("divider")}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 12h14"
                    />
                  </svg>
                }
                label="Divider"
              />
            </div>
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline">Cancel</Button>
        <Button onClick={handleSave}>Save Content</Button>
      </div>
    </div>
  );
}

interface BlockButtonProps {
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
}

function BlockButton({ onClick, icon, label }: BlockButtonProps) {
  return (
    <button
      onClick={onClick}
      className="flex items-center space-x-2 p-2 rounded-md hover:bg-muted/50 transition-colors w-full text-left"
    >
      <span className="text-muted-foreground">{icon}</span>
      <span>{label}</span>
    </button>
  );
}
