"use client";

import { useState } from "react";
import { Draggable } from "@hello-pangea/dnd";
import { cn } from "../../lib/utils";

export type ContentBlockType =
  | "text"
  | "heading"
  | "image"
  | "video"
  | "quote"
  | "list"
  | "divider";

export interface ContentBlockProps {
  id: string;
  type: ContentBlockType;
  content: any;
  index: number;
  onUpdate: (id: string, content: any) => void;
  onDelete: (id: string) => void;
}

export default function ContentBlock({
  id,
  type,
  content,
  index,
  onUpdate,
  onDelete,
}: ContentBlockProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [blockContent, setBlockContent] = useState(content);

  const handleContentChange = (value: any) => {
    setBlockContent(value);
    onUpdate(id, value);
  };

  const renderBlockContent = () => {
    switch (type) {
      case "text":
        return (
          <div className="w-full">
            {isEditing ? (
              <textarea
                className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={blockContent.text}
                onChange={(e) =>
                  handleContentChange({ ...blockContent, text: e.target.value })
                }
                rows={4}
                autoFocus
              />
            ) : (
              <p className="p-2">{blockContent.text}</p>
            )}
          </div>
        );
      case "heading":
        return (
          <div className="w-full">
            {isEditing ? (
              <div className="space-y-2">
                <input
                  type="text"
                  className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  value={blockContent.text}
                  onChange={(e) =>
                    handleContentChange({
                      ...blockContent,
                      text: e.target.value,
                    })
                  }
                  autoFocus
                />
                <select
                  className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  value={blockContent.level}
                  onChange={(e) =>
                    handleContentChange({
                      ...blockContent,
                      level: e.target.value,
                    })
                  }
                >
                  <option value="h1">Heading 1</option>
                  <option value="h2">Heading 2</option>
                  <option value="h3">Heading 3</option>
                  <option value="h4">Heading 4</option>
                </select>
              </div>
            ) : (
              <div className="p-2">
                {blockContent.level === "h1" && (
                  <h1 className="text-3xl font-bold">{blockContent.text}</h1>
                )}
                {blockContent.level === "h2" && (
                  <h2 className="text-2xl font-bold">{blockContent.text}</h2>
                )}
                {blockContent.level === "h3" && (
                  <h3 className="text-xl font-bold">{blockContent.text}</h3>
                )}
                {blockContent.level === "h4" && (
                  <h4 className="text-lg font-bold">{blockContent.text}</h4>
                )}
              </div>
            )}
          </div>
        );
      case "image":
        return (
          <div className="w-full">
            {isEditing ? (
              <div className="space-y-2">
                <input
                  type="text"
                  className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  value={blockContent.url}
                  onChange={(e) =>
                    handleContentChange({
                      ...blockContent,
                      url: e.target.value,
                    })
                  }
                  placeholder="Image URL"
                  autoFocus
                />
                <input
                  type="text"
                  className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  value={blockContent.alt}
                  onChange={(e) =>
                    handleContentChange({
                      ...blockContent,
                      alt: e.target.value,
                    })
                  }
                  placeholder="Alt text"
                />
              </div>
            ) : (
              <div className="p-2">
                {blockContent.url ? (
                  <div className="relative aspect-video bg-muted/50 rounded-md overflow-hidden">
                    <img
                      src={blockContent.url}
                      alt={blockContent.alt || "Content image"}
                      className="object-cover w-full h-full"
                    />
                  </div>
                ) : (
                  <div className="aspect-video bg-muted/50 rounded-md flex items-center justify-center">
                    <span className="text-muted-foreground">No image URL</span>
                  </div>
                )}
                {blockContent.alt && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {blockContent.alt}
                  </p>
                )}
              </div>
            )}
          </div>
        );
      case "video":
        return (
          <div className="w-full">
            {isEditing ? (
              <div className="space-y-2">
                <input
                  type="text"
                  className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  value={blockContent.url}
                  onChange={(e) =>
                    handleContentChange({
                      ...blockContent,
                      url: e.target.value,
                    })
                  }
                  placeholder="Video URL (YouTube, Vimeo, etc.)"
                  autoFocus
                />
                <input
                  type="text"
                  className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  value={blockContent.caption}
                  onChange={(e) =>
                    handleContentChange({
                      ...blockContent,
                      caption: e.target.value,
                    })
                  }
                  placeholder="Caption"
                />
              </div>
            ) : (
              <div className="p-2">
                {blockContent.url ? (
                  <div className="aspect-video bg-muted/50 rounded-md overflow-hidden">
                    <div className="w-full h-full flex items-center justify-center">
                      <span className="text-muted-foreground">Video: {blockContent.url}</span>
                    </div>
                  </div>
                ) : (
                  <div className="aspect-video bg-muted/50 rounded-md flex items-center justify-center">
                    <span className="text-muted-foreground">No video URL</span>
                  </div>
                )}
                {blockContent.caption && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {blockContent.caption}
                  </p>
                )}
              </div>
            )}
          </div>
        );
      case "quote":
        return (
          <div className="w-full">
            {isEditing ? (
              <div className="space-y-2">
                <textarea
                  className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  value={blockContent.text}
                  onChange={(e) =>
                    handleContentChange({
                      ...blockContent,
                      text: e.target.value,
                    })
                  }
                  rows={3}
                  autoFocus
                />
                <input
                  type="text"
                  className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  value={blockContent.source}
                  onChange={(e) =>
                    handleContentChange({
                      ...blockContent,
                      source: e.target.value,
                    })
                  }
                  placeholder="Source"
                />
              </div>
            ) : (
              <div className="p-2">
                <blockquote className="border-l-4 border-primary pl-4 italic">
                  {blockContent.text}
                </blockquote>
                {blockContent.source && (
                  <p className="text-sm text-muted-foreground mt-1">
                    — {blockContent.source}
                  </p>
                )}
              </div>
            )}
          </div>
        );
      case "list":
        return (
          <div className="w-full">
            {isEditing ? (
              <div className="space-y-2">
                <select
                  className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  value={blockContent.style}
                  onChange={(e) =>
                    handleContentChange({
                      ...blockContent,
                      style: e.target.value,
                    })
                  }
                >
                  <option value="bullet">Bullet List</option>
                  <option value="numbered">Numbered List</option>
                </select>
                <textarea
                  className="w-full p-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  value={blockContent.items.join("\n")}
                  onChange={(e) =>
                    handleContentChange({
                      ...blockContent,
                      items: e.target.value.split("\n"),
                    })
                  }
                  rows={4}
                  placeholder="One item per line"
                  autoFocus
                />
              </div>
            ) : (
              <div className="p-2">
                {blockContent.style === "bullet" ? (
                  <ul className="list-disc pl-5">
                    {blockContent.items.map((item: string, i: number) => (
                      <li key={i}>{item}</li>
                    ))}
                  </ul>
                ) : (
                  <ol className="list-decimal pl-5">
                    {blockContent.items.map((item: string, i: number) => (
                      <li key={i}>{item}</li>
                    ))}
                  </ol>
                )}
              </div>
            )}
          </div>
        );
      case "divider":
        return (
          <div className="w-full p-2">
            <hr className="border-t border-border my-2" />
          </div>
        );
      default:
        return <div>Unknown block type</div>;
    }
  };

  return (
    <Draggable draggableId={id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={cn(
            "mb-4 border rounded-md bg-card transition-shadow",
            snapshot.isDragging ? "shadow-lg" : "shadow-sm",
            isEditing ? "ring-2 ring-primary" : ""
          )}
        >
          <div className="flex items-center">
            <div
              {...provided.dragHandleProps}
              className="p-2 text-muted-foreground hover:text-foreground cursor-grab"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 8h16M4 16h16"
                />
              </svg>
            </div>
            <div className="flex-1">{renderBlockContent()}</div>
            <div className="flex p-2 space-x-1">
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="p-1 rounded-md hover:bg-muted transition-colors"
                aria-label={isEditing ? "Save" : "Edit"}
              >
                {isEditing ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                )}
              </button>
              <button
                onClick={() => onDelete(id)}
                className="p-1 rounded-md hover:bg-muted transition-colors text-muted-foreground hover:text-error"
                aria-label="Delete"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}
    </Draggable>
  );
}
