"use client";

import { formatDate } from "../../lib/utils";

export interface ContentCardProps {
  id: string;
  title: string;
  excerpt: string;
  thumbnail?: string;
  status: "draft" | "published" | "scheduled";
  date: string;
  platform?: string;
  onClick?: () => void;
}

export default function ContentCard({
  id,
  title,
  excerpt,
  thumbnail,
  status,
  date,
  platform,
  onClick,
}: ContentCardProps) {
  const statusColors = {
    draft: "bg-muted text-muted-foreground",
    published: "bg-secondary/10 text-secondary",
    scheduled: "bg-primary/10 text-primary",
  };

  return (
    <div
      className="border border-border rounded-lg overflow-hidden bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer"
      onClick={onClick}
    >
      {thumbnail ? (
        <div className="aspect-video bg-muted/50 relative">
          <img
            src={thumbnail}
            alt={title}
            className="w-full h-full object-cover"
          />
        </div>
      ) : (
        <div className="aspect-video bg-muted/50 flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-muted-foreground/50"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        </div>
      )}

      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <span
            className={`text-xs px-2 py-1 rounded-full ${statusColors[status]}`}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span>
          {platform && (
            <span className="text-xs text-muted-foreground">{platform}</span>
          )}
        </div>

        <h3 className="font-semibold mb-1 line-clamp-1">{title}</h3>
        <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
          {excerpt}
        </p>

        <div className="text-xs text-muted-foreground">
          {formatDate(date)}
        </div>
      </div>
    </div>
  );
}
