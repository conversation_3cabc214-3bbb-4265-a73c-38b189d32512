"use client";

import { useState } from "react";
import { Input } from "../ui/Input";
import { Button } from "../ui/Button";

export interface ContentFiltersProps {
  onFilterChange: (filters: ContentFilters) => void;
}

export interface ContentFilters {
  search: string;
  status: string[];
  platform: string[];
  dateRange: {
    from: string;
    to: string;
  };
}

export default function ContentFilters({ onFilterChange }: ContentFiltersProps) {
  const [filters, setFilters] = useState<ContentFilters>({
    search: "",
    status: [],
    platform: [],
    dateRange: {
      from: "",
      to: "",
    },
  });

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFilters = {
      ...filters,
      search: e.target.value,
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const toggleStatus = (status: string) => {
    const newStatus = filters.status.includes(status)
      ? filters.status.filter((s) => s !== status)
      : [...filters.status, status];

    const newFilters = {
      ...filters,
      status: newStatus,
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const togglePlatform = (platform: string) => {
    const newPlatform = filters.platform.includes(platform)
      ? filters.platform.filter((p) => p !== platform)
      : [...filters.platform, platform];

    const newFilters = {
      ...filters,
      platform: newPlatform,
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleDateChange = (field: "from" | "to", value: string) => {
    const newFilters = {
      ...filters,
      dateRange: {
        ...filters.dateRange,
        [field]: value,
      },
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    const newFilters = {
      search: "",
      status: [],
      platform: [],
      dateRange: {
        from: "",
        to: "",
      },
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  return (
    <div className="space-y-4">
      <div className="relative">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
        <Input
          type="search"
          placeholder="Search content..."
          className="pl-10"
          value={filters.search}
          onChange={handleSearchChange}
        />
      </div>

      <div>
        <h3 className="text-sm font-medium mb-2">Status</h3>
        <div className="flex flex-wrap gap-2">
          <FilterChip
            label="Draft"
            active={filters.status.includes("draft")}
            onClick={() => toggleStatus("draft")}
          />
          <FilterChip
            label="Published"
            active={filters.status.includes("published")}
            onClick={() => toggleStatus("published")}
          />
          <FilterChip
            label="Scheduled"
            active={filters.status.includes("scheduled")}
            onClick={() => toggleStatus("scheduled")}
          />
        </div>
      </div>

      <div>
        <h3 className="text-sm font-medium mb-2">Platform</h3>
        <div className="flex flex-wrap gap-2">
          <FilterChip
            label="YouTube"
            active={filters.platform.includes("youtube")}
            onClick={() => togglePlatform("youtube")}
          />
          <FilterChip
            label="Instagram"
            active={filters.platform.includes("instagram")}
            onClick={() => togglePlatform("instagram")}
          />
          <FilterChip
            label="LinkedIn"
            active={filters.platform.includes("linkedin")}
            onClick={() => togglePlatform("linkedin")}
          />
          <FilterChip
            label="Twitter"
            active={filters.platform.includes("twitter")}
            onClick={() => togglePlatform("twitter")}
          />
          <FilterChip
            label="Blog"
            active={filters.platform.includes("blog")}
            onClick={() => togglePlatform("blog")}
          />
        </div>
      </div>

      <div>
        <h3 className="text-sm font-medium mb-2">Date Range</h3>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="text-xs text-muted-foreground">From</label>
            <Input
              type="date"
              value={filters.dateRange.from}
              onChange={(e) => handleDateChange("from", e.target.value)}
            />
          </div>
          <div>
            <label className="text-xs text-muted-foreground">To</label>
            <Input
              type="date"
              value={filters.dateRange.to}
              onChange={(e) => handleDateChange("to", e.target.value)}
            />
          </div>
        </div>
      </div>

      <Button
        variant="outline"
        className="w-full"
        onClick={clearFilters}
      >
        Clear Filters
      </Button>
    </div>
  );
}

interface FilterChipProps {
  label: string;
  active: boolean;
  onClick: () => void;
}

function FilterChip({ label, active, onClick }: FilterChipProps) {
  return (
    <button
      onClick={onClick}
      className={`px-3 py-1 text-sm rounded-full transition-colors ${
        active
          ? "bg-primary text-primary-foreground"
          : "bg-muted hover:bg-muted/80 text-foreground"
      }`}
    >
      {label}
    </button>
  );
}
