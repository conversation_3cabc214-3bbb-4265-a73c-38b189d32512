"use client";

import { useState } from "react";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Legend,
} from "recharts";
import { <PERSON>, CardH<PERSON>er, CardTitle, CardContent } from "../ui/Card";

// Sample data for the charts
const areaChartData = [
  { name: "Jan", views: 4000, engagement: 2400 },
  { name: "Feb", views: 3000, engagement: 1398 },
  { name: "Mar", views: 2000, engagement: 9800 },
  { name: "Apr", views: 2780, engagement: 3908 },
  { name: "May", views: 1890, engagement: 4800 },
  { name: "<PERSON>", views: 2390, engagement: 3800 },
  { name: "Jul", views: 3490, engagement: 4300 },
];

const barChartData = [
  { name: "YouTube", value: 4000 },
  { name: "Instagram", value: 3000 },
  { name: "LinkedIn", value: 2000 },
  { name: "Twitter", value: 2780 },
  { name: "Blog", value: 1890 },
];

type TimeRange = "7d" | "30d" | "90d" | "1y";

export default function PerformanceMetrics() {
  const [timeRange, setTimeRange] = useState<TimeRange>("30d");

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Performance Metrics</h2>
        <div className="flex items-center space-x-2">
          <TimeRangeButton
            range="7d"
            active={timeRange === "7d"}
            onClick={() => setTimeRange("7d")}
          />
          <TimeRangeButton
            range="30d"
            active={timeRange === "30d"}
            onClick={() => setTimeRange("30d")}
          />
          <TimeRangeButton
            range="90d"
            active={timeRange === "90d"}
            onClick={() => setTimeRange("90d")}
          />
          <TimeRangeButton
            range="1y"
            active={timeRange === "1y"}
            onClick={() => setTimeRange("1y")}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Content Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={areaChartData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <defs>
                    <linearGradient id="colorViews" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="var(--primary)" stopOpacity={0.8} />
                      <stop offset="95%" stopColor="var(--primary)" stopOpacity={0} />
                    </linearGradient>
                    <linearGradient id="colorEngagement" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="var(--secondary)" stopOpacity={0.8} />
                      <stop offset="95%" stopColor="var(--secondary)" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <XAxis dataKey="name" />
                  <YAxis />
                  <CartesianGrid strokeDasharray="3 3" />
                  <Tooltip />
                  <Area
                    type="monotone"
                    dataKey="views"
                    stroke="var(--primary)"
                    fillOpacity={1}
                    fill="url(#colorViews)"
                  />
                  <Area
                    type="monotone"
                    dataKey="engagement"
                    stroke="var(--secondary)"
                    fillOpacity={1}
                    fill="url(#colorEngagement)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Platform Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={barChartData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="value" name="Views" fill="var(--primary)" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

interface TimeRangeButtonProps {
  range: TimeRange;
  active: boolean;
  onClick: () => void;
}

function TimeRangeButton({ range, active, onClick }: TimeRangeButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`px-3 py-1 text-sm rounded-md transition-colors ${
        active
          ? "bg-primary text-primary-foreground"
          : "bg-muted hover:bg-muted/80 text-foreground"
      }`}
    >
      {range}
    </button>
  );
}
