"use client";

import { useAuth } from './useAuth';
import { Permission, ROLE_PERMISSIONS, UserRole } from '../types/auth.types';
import { supabase } from '../lib/supabaseClient';
import { useEffect, useState } from 'react';

export function useRBAC() {
  const { user } = useAuth();
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchUserRole() {
      if (!user) {
        setUserRole(null);
        setLoading(false);
        return;
      }

      try {
        // Fetch the user's role from the database
        const { data, error } = await supabase
          .from('users')
          .select('role')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching user role:', error);
          setUserRole(null);
        } else {
          setUserRole(data.role as UserRole);
        }
      } catch (error) {
        console.error('Error in useRBAC:', error);
        setUserRole(null);
      } finally {
        setLoading(false);
      }
    }

    fetchUserRole();
  }, [user]);

  /**
   * Check if the current user has a specific permission
   */
  const hasPermission = (permission: Permission): boolean => {
    if (!user || !userRole) return false;
    return ROLE_PERMISSIONS[userRole].includes(permission);
  };

  /**
   * Check if the current user has any of the specified permissions
   */
  const hasAnyPermission = (permissions: Permission[]): boolean => {
    if (!user || !userRole) return false;
    return permissions.some(permission => ROLE_PERMISSIONS[userRole].includes(permission));
  };

  /**
   * Check if the current user has all of the specified permissions
   */
  const hasAllPermissions = (permissions: Permission[]): boolean => {
    if (!user || !userRole) return false;
    return permissions.every(permission => ROLE_PERMISSIONS[userRole].includes(permission));
  };

  /**
   * Check if the current user has a specific role
   */
  const hasRole = (role: UserRole): boolean => {
    if (!user || !userRole) return false;
    return userRole === role || userRole === 'admin'; // Admin has all roles
  };

  /**
   * Get all permissions for the current user
   */
  const getUserPermissions = (): Permission[] => {
    if (!user || !userRole) return [];
    return ROLE_PERMISSIONS[userRole];
  };

  return {
    userRole,
    loading,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    getUserPermissions
  };
}
