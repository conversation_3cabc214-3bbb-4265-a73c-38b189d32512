"use client";

import { useState, useCallback } from 'react';
import { usePublishing } from '../context/PublishingContext';
import { 
  PublicationFilterOptions, 
  PublicationItem, 
  PublicationCreateInput, 
  PublicationUpdateInput,
  Platform
} from '../types/publishing.types';

export function usePublishingManagement() {
  const {
    publications,
    selectedPublication,
    productions,
    platformConnections,
    isLoading,
    error,
    searchResult,
    fetchAllPublications,
    searchPublications,
    getPublicationById,
    getPublicationsByProduction,
    createPublication,
    updatePublication,
    deletePublication,
    publishNow,
    getProductionById,
    getProductionsByWorkflow,
    fetchPlatformConnections,
    connectToPlatform,
    disconnectFromPlatform,
    setSelectedPublication,
    clearPublishingState
  } = usePublishing();

  const [filterOptions, setFilterOptions] = useState<PublicationFilterOptions>({
    sortBy: 'scheduled_at',
    sortOrder: 'desc',
    limit: 10,
    offset: 0
  });

  // Load publications with current filter options
  const loadPublications = useCallback(async () => {
    await fetchAllPublications(filterOptions);
  }, [fetchAllPublications, filterOptions]);

  // Load publications with pagination
  const loadPublicationsPage = useCallback(async (page: number) => {
    const newOffset = (page - 1) * (filterOptions.limit || 10);
    const newOptions = { ...filterOptions, offset: newOffset };
    setFilterOptions(newOptions);
    await searchPublications(newOptions);
  }, [searchPublications, filterOptions]);

  // Update filter options and reload publications
  const updateFilters = useCallback(async (newOptions: Partial<PublicationFilterOptions>) => {
    const updatedOptions = { ...filterOptions, ...newOptions, offset: 0 }; // Reset to first page
    setFilterOptions(updatedOptions);
    await searchPublications(updatedOptions);
  }, [searchPublications, filterOptions]);

  // Select publication by ID
  const selectPublicationById = useCallback(async (publicationId: string) => {
    const publication = await getPublicationById(publicationId);
    if (publication) {
      setSelectedPublication(publication);
    }
    return publication;
  }, [getPublicationById, setSelectedPublication]);

  // Create new publication with validation
  const createNewPublication = useCallback(async (input: PublicationCreateInput) => {
    // Basic validation
    if (!input.production_id) {
      throw new Error('Production ID is required');
    }
    
    if (!input.platform) {
      throw new Error('Platform is required');
    }
    
    // Check if the platform is connected
    const isPlatformConnected = platformConnections.some(
      conn => conn.platform === input.platform && conn.connected
    );
    
    if (!isPlatformConnected) {
      throw new Error(`You need to connect to ${input.platform} before publishing`);
    }
    
    return await createPublication(input);
  }, [createPublication, platformConnections]);

  // Update publication with validation
  const updateExistingPublication = useCallback(async (publicationId: string, updates: PublicationUpdateInput) => {
    return await updatePublication(publicationId, updates);
  }, [updatePublication]);

  // Confirm and delete publication
  const confirmAndDeletePublication = useCallback(async (publicationId: string, confirmText?: string) => {
    // This is a client-side confirmation - in a real app, you might want to show a modal
    const isConfirmed = window.confirm(confirmText || 'Are you sure you want to delete this publication? This action cannot be undone.');
    
    if (isConfirmed) {
      return await deletePublication(publicationId);
    }
    
    return false;
  }, [deletePublication]);

  // Publish now with confirmation
  const publishNowWithConfirmation = useCallback(async (publicationId: string, confirmText?: string) => {
    const publication = await getPublicationById(publicationId);
    
    if (!publication) {
      throw new Error('Publication not found');
    }
    
    // If publication is already publishing or published, ask for confirmation
    if (publication.status === 'publishing' || publication.status === 'published') {
      const isConfirmed = window.confirm(confirmText || 'This content is already being published or has been published. Do you want to publish it again?');
      
      if (!isConfirmed) {
        return null;
      }
    }
    
    return await publishNow(publicationId);
  }, [getPublicationById, publishNow]);

  // Check if a platform is connected
  const isPlatformConnected = useCallback((platform: Platform): boolean => {
    return platformConnections.some(conn => conn.platform === platform && conn.connected);
  }, [platformConnections]);

  // Connect to platform with redirect
  const connectToPlatformWithRedirect = useCallback(async (platform: Platform) => {
    const redirectUrl = await connectToPlatform(platform);
    
    if (redirectUrl) {
      // In a real app, you would redirect to this URL for OAuth flow
      window.location.href = redirectUrl;
      return true;
    }
    
    return false;
  }, [connectToPlatform]);

  // Load platform connections if not already loaded
  const loadPlatformConnectionsIfNeeded = useCallback(async () => {
    if (platformConnections.length === 0) {
      await fetchPlatformConnections();
    }
  }, [platformConnections.length, fetchPlatformConnections]);

  return {
    // State
    publications,
    selectedPublication,
    productions,
    platformConnections,
    isLoading,
    error,
    searchResult,
    filterOptions,
    
    // Basic operations
    fetchAllPublications,
    searchPublications,
    getPublicationById,
    getPublicationsByProduction,
    createPublication,
    updatePublication,
    deletePublication,
    publishNow,
    getProductionById,
    getProductionsByWorkflow,
    fetchPlatformConnections,
    connectToPlatform,
    disconnectFromPlatform,
    setSelectedPublication,
    clearPublishingState,
    
    // Enhanced operations
    loadPublications,
    loadPublicationsPage,
    updateFilters,
    selectPublicationById,
    createNewPublication,
    updateExistingPublication,
    confirmAndDeletePublication,
    publishNowWithConfirmation,
    isPlatformConnected,
    connectToPlatformWithRedirect,
    loadPlatformConnectionsIfNeeded
  };
}
