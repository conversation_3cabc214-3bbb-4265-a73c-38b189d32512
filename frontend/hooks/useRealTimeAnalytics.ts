"use client";

import { useEffect, useRef, useState, useCallback } from 'react';
import { useAuth } from '../context/AuthContext';
import { useToast } from '../context/ToastContext';

interface AnalyticsSummary {
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  totalShares: number;
  totalImpressions: number;
  platforms: Record<string, {
    views: number;
    likes: number;
    comments: number;
    shares: number;
    impressions: number;
  }>;
}

interface WebSocketMessage {
  type: string;
  data?: any;
  message?: string;
  timestamp: string;
  platform?: string;
}

interface UseRealTimeAnalyticsOptions {
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  onUpdate?: (data: any) => void;
  onError?: (error: string) => void;
}

export function useRealTimeAnalytics(options: UseRealTimeAnalyticsOptions = {}) {
  const {
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectDelay = 3000,
    onUpdate,
    onError
  } = options;

  const { user } = useAuth();
  const { showToast } = useToast();
  
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [summary, setSummary] = useState<AnalyticsSummary | null>(null);
  const [recentUpdates, setRecentUpdates] = useState<any[]>([]);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);
  const subscribedPlatformsRef = useRef<Set<string>>(new Set());

  const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
  const WS_URL = API_BASE.replace('http', 'ws');

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (!user || isConnecting || isConnected) {
      return;
    }

    setIsConnecting(true);
    setConnectionError(null);

    try {
      const wsUrl = `${WS_URL}/ws/analytics?token=${user.access_token}`;
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('Real-time analytics WebSocket connected');
        setIsConnected(true);
        setIsConnecting(false);
        setConnectionError(null);
        reconnectCountRef.current = 0;
        
        showToast('Real-time analytics connected', 'success');
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleMessage(message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('Real-time analytics WebSocket disconnected', event.code, event.reason);
        setIsConnected(false);
        setIsConnecting(false);
        wsRef.current = null;

        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectCountRef.current < reconnectAttempts) {
          scheduleReconnect();
        } else if (reconnectCountRef.current >= reconnectAttempts) {
          setConnectionError('Failed to reconnect after multiple attempts');
          showToast('Real-time analytics connection lost', 'error');
        }
      };

      ws.onerror = (error) => {
        console.error('Real-time analytics WebSocket error:', error);
        setConnectionError('WebSocket connection error');
        setIsConnecting(false);
        
        if (onError) {
          onError('WebSocket connection error');
        }
      };

      wsRef.current = ws;

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setIsConnecting(false);
      setConnectionError('Failed to create connection');
      
      if (onError) {
        onError('Failed to create connection');
      }
    }
  }, [user, isConnecting, isConnected, reconnectAttempts, WS_URL, showToast, onError]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'User disconnected');
      wsRef.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
    setConnectionError(null);
    reconnectCountRef.current = 0;
  }, []);

  // Schedule reconnection attempt
  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    reconnectCountRef.current += 1;
    console.log(`Scheduling reconnect attempt ${reconnectCountRef.current}/${reconnectAttempts} in ${reconnectDelay}ms`);

    reconnectTimeoutRef.current = setTimeout(() => {
      if (!isConnected && user) {
        connect();
      }
    }, reconnectDelay);
  }, [connect, isConnected, user, reconnectAttempts, reconnectDelay]);

  // Handle incoming WebSocket messages
  const handleMessage = useCallback((message: WebSocketMessage) => {
    console.log('Received WebSocket message:', message.type);

    switch (message.type) {
      case 'connection_established':
        console.log('WebSocket connection established');
        break;

      case 'initial_data':
        if (message.data) {
          setSummary(message.data.summary);
          setRecentUpdates(message.data.recentUpdates || []);
          setLastUpdate(new Date(message.timestamp));
        }
        break;

      case 'analytics_update':
        if (message.data) {
          setSummary(message.data.summary);
          setRecentUpdates(prev => [
            ...(message.data.recent || []),
            ...prev.slice(0, 4) // Keep only 5 most recent
          ]);
          setLastUpdate(new Date(message.timestamp));
          
          if (onUpdate) {
            onUpdate(message.data);
          }
          
          showToast('Analytics updated', 'info');
        }
        break;

      case 'global_analytics_update':
        // Handle global updates if needed
        console.log('Global analytics update received');
        break;

      case 'subscription_confirmed':
        console.log(`Subscribed to ${message.platform} updates`);
        if (message.platform) {
          subscribedPlatformsRef.current.add(message.platform);
        }
        break;

      case 'unsubscription_confirmed':
        console.log(`Unsubscribed from ${message.platform} updates`);
        if (message.platform) {
          subscribedPlatformsRef.current.delete(message.platform);
        }
        break;

      case 'pong':
        // Handle ping/pong for connection health
        break;

      case 'error':
        console.error('WebSocket error message:', message.message);
        setConnectionError(message.message || 'Unknown error');
        if (onError) {
          onError(message.message || 'Unknown error');
        }
        break;

      default:
        console.log('Unknown message type:', message.type);
    }
  }, [onUpdate, onError, showToast]);

  // Send message to WebSocket
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      try {
        wsRef.current.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        return false;
      }
    }
    return false;
  }, []);

  // Subscribe to platform updates
  const subscribeToPlatform = useCallback((platform: string) => {
    return sendMessage({
      type: 'subscribe_platform',
      platform
    });
  }, [sendMessage]);

  // Unsubscribe from platform updates
  const unsubscribeFromPlatform = useCallback((platform: string) => {
    return sendMessage({
      type: 'unsubscribe_platform',
      platform
    });
  }, [sendMessage]);

  // Request immediate update
  const requestUpdate = useCallback(() => {
    return sendMessage({
      type: 'request_update'
    });
  }, [sendMessage]);

  // Send ping to check connection
  const ping = useCallback(() => {
    return sendMessage({
      type: 'ping'
    });
  }, [sendMessage]);

  // Auto-connect when user is available
  useEffect(() => {
    if (user && autoConnect && !isConnected && !isConnecting) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [user, autoConnect, connect, disconnect, isConnected, isConnecting]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  // Periodic ping to keep connection alive
  useEffect(() => {
    if (!isConnected) return;

    const pingInterval = setInterval(() => {
      ping();
    }, 30000); // Ping every 30 seconds

    return () => clearInterval(pingInterval);
  }, [isConnected, ping]);

  return {
    // Connection state
    isConnected,
    isConnecting,
    connectionError,
    lastUpdate,
    
    // Data
    summary,
    recentUpdates,
    subscribedPlatforms: Array.from(subscribedPlatformsRef.current),
    
    // Actions
    connect,
    disconnect,
    subscribeToPlatform,
    unsubscribeFromPlatform,
    requestUpdate,
    ping,
    
    // Utils
    sendMessage
  };
}
