"use client";

import { useState, useCallback } from 'react';
import { useContent } from '../context/ContentContext';
import { ContentFilterOptions, ContentItem, ContentCreateInput, ContentUpdateInput } from '../types/content.types';

export function useContentManagement() {
  const {
    contentItems,
    selectedContent,
    isLoading,
    error,
    searchResult,
    fetchAllContent,
    searchContent,
    getContentById,
    createContent,
    updateContent,
    deleteContent,
    archiveContent,
    setSelectedContent,
    clearContentState
  } = useContent();

  const [filterOptions, setFilterOptions] = useState<ContentFilterOptions>({
    sortBy: 'created_at',
    sortOrder: 'desc',
    limit: 10,
    offset: 0
  });

  // Load content with current filter options
  const loadContent = useCallback(async () => {
    await fetchAllContent(filterOptions);
  }, [fetchAllContent, filterOptions]);

  // Load content with pagination
  const loadContentPage = useCallback(async (page: number) => {
    const newOffset = (page - 1) * (filterOptions.limit || 10);
    const newOptions = { ...filterOptions, offset: newOffset };
    setFilterOptions(newOptions);
    await searchContent(newOptions);
  }, [searchContent, filterOptions]);

  // Update filter options and reload content
  const updateFilters = useCallback(async (newOptions: Partial<ContentFilterOptions>) => {
    const updatedOptions = { ...filterOptions, ...newOptions, offset: 0 }; // Reset to first page
    setFilterOptions(updatedOptions);
    await searchContent(updatedOptions);
  }, [searchContent, filterOptions]);

  // Select content by ID
  const selectContentById = useCallback(async (contentId: string) => {
    const content = await getContentById(contentId);
    if (content) {
      setSelectedContent(content);
    }
    return content;
  }, [getContentById, setSelectedContent]);

  // Create new content with validation
  const createNewContent = useCallback(async (input: ContentCreateInput) => {
    // Basic validation
    if (!input.title.trim()) {
      throw new Error('Title is required');
    }

    return await createContent(input);
  }, [createContent]);

  // Update content with validation
  const updateExistingContent = useCallback(async (contentId: string, updates: ContentUpdateInput) => {
    // Basic validation for title if it's being updated
    if (updates.title !== undefined && !updates.title.trim()) {
      throw new Error('Title cannot be empty');
    }

    return await updateContent(contentId, updates);
  }, [updateContent]);

  // Confirm and delete content
  const confirmAndDeleteContent = useCallback(async (contentId: string, confirmText: string) => {
    // This is a client-side confirmation - in a real app, you might want to show a modal
    const isConfirmed = window.confirm(confirmText || 'Are you sure you want to delete this content? This action cannot be undone.');
    
    if (isConfirmed) {
      return await deleteContent(contentId);
    }
    
    return false;
  }, [deleteContent]);

  return {
    // State
    contentItems,
    selectedContent,
    isLoading,
    error,
    searchResult,
    filterOptions,
    
    // Basic operations
    fetchAllContent,
    searchContent,
    getContentById,
    createContent,
    updateContent,
    deleteContent,
    archiveContent,
    setSelectedContent,
    clearContentState,
    
    // Enhanced operations
    loadContent,
    loadContentPage,
    updateFilters,
    selectContentById,
    createNewContent,
    updateExistingContent,
    confirmAndDeleteContent
  };
}
