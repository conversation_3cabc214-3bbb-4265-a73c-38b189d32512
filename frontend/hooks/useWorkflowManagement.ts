"use client";

import { useState, useCallback } from 'react';
import { useWorkflow } from '../context/WorkflowContext';
import { 
  WorkflowFilterOptions, 
  WorkflowItem, 
  WorkflowCreateInput, 
  WorkflowUpdateInput,
  WorkflowTemplate
} from '../types/workflow.types';

export function useWorkflowManagement() {
  const {
    workflows,
    selectedWorkflow,
    workflowTemplates,
    isLoading,
    error,
    searchResult,
    fetchAllWorkflows,
    searchWorkflows,
    getWorkflowById,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    startWorkflow,
    cancelWorkflow,
    fetchWorkflowTemplates,
    getWorkflowTemplateById,
    setSelectedWorkflow,
    clearWorkflowState
  } = useWorkflow();

  const [filterOptions, setFilterOptions] = useState<WorkflowFilterOptions>({
    sortBy: 'created_at',
    sortOrder: 'desc',
    limit: 10,
    offset: 0
  });

  // Load workflows with current filter options
  const loadWorkflows = useCallback(async () => {
    await fetchAllWorkflows(filterOptions);
  }, [fetchAllWorkflows, filterOptions]);

  // Load workflows with pagination
  const loadWorkflowsPage = useCallback(async (page: number) => {
    const newOffset = (page - 1) * (filterOptions.limit || 10);
    const newOptions = { ...filterOptions, offset: newOffset };
    setFilterOptions(newOptions);
    await searchWorkflows(newOptions);
  }, [searchWorkflows, filterOptions]);

  // Update filter options and reload workflows
  const updateFilters = useCallback(async (newOptions: Partial<WorkflowFilterOptions>) => {
    const updatedOptions = { ...filterOptions, ...newOptions, offset: 0 }; // Reset to first page
    setFilterOptions(updatedOptions);
    await searchWorkflows(updatedOptions);
  }, [searchWorkflows, filterOptions]);

  // Select workflow by ID
  const selectWorkflowById = useCallback(async (workflowId: string) => {
    const workflow = await getWorkflowById(workflowId);
    if (workflow) {
      setSelectedWorkflow(workflow);
    }
    return workflow;
  }, [getWorkflowById, setSelectedWorkflow]);

  // Create new workflow from template
  const createWorkflowFromTemplate = useCallback(async (templateId: string, name: string, description?: string) => {
    const template = await getWorkflowTemplateById(templateId);
    
    if (!template) {
      throw new Error('Workflow template not found');
    }
    
    const workflowInput: WorkflowCreateInput = {
      workflow_type: template.workflow_type,
      name: name || template.name,
      description: description || template.description,
      steps: template.steps,
      metadata: template.metadata
    };
    
    return await createWorkflow(workflowInput);
  }, [getWorkflowTemplateById, createWorkflow]);

  // Create new workflow with validation
  const createNewWorkflow = useCallback(async (input: WorkflowCreateInput) => {
    // Basic validation
    if (!input.name.trim()) {
      throw new Error('Workflow name is required');
    }
    
    if (!input.steps || input.steps.length === 0) {
      throw new Error('Workflow must have at least one step');
    }
    
    return await createWorkflow(input);
  }, [createWorkflow]);

  // Update workflow with validation
  const updateExistingWorkflow = useCallback(async (workflowId: string, updates: WorkflowUpdateInput) => {
    // Basic validation for name if it's being updated
    if (updates.name !== undefined && !updates.name.trim()) {
      throw new Error('Workflow name cannot be empty');
    }
    
    return await updateWorkflow(workflowId, updates);
  }, [updateWorkflow]);

  // Confirm and delete workflow
  const confirmAndDeleteWorkflow = useCallback(async (workflowId: string, confirmText?: string) => {
    // This is a client-side confirmation - in a real app, you might want to show a modal
    const isConfirmed = window.confirm(confirmText || 'Are you sure you want to delete this workflow? This action cannot be undone.');
    
    if (isConfirmed) {
      return await deleteWorkflow(workflowId);
    }
    
    return false;
  }, [deleteWorkflow]);

  // Start workflow with confirmation if needed
  const startWorkflowWithConfirmation = useCallback(async (workflowId: string, confirmText?: string) => {
    const workflow = await getWorkflowById(workflowId);
    
    if (!workflow) {
      throw new Error('Workflow not found');
    }
    
    // If workflow is already running, ask for confirmation
    if (workflow.status === 'running') {
      const isConfirmed = window.confirm(confirmText || 'This workflow is already running. Do you want to restart it?');
      
      if (!isConfirmed) {
        return null;
      }
    }
    
    return await startWorkflow(workflowId);
  }, [getWorkflowById, startWorkflow]);

  // Load templates if not already loaded
  const loadTemplatesIfNeeded = useCallback(async () => {
    if (workflowTemplates.length === 0) {
      await fetchWorkflowTemplates();
    }
  }, [workflowTemplates.length, fetchWorkflowTemplates]);

  return {
    // State
    workflows,
    selectedWorkflow,
    workflowTemplates,
    isLoading,
    error,
    searchResult,
    filterOptions,
    
    // Basic operations
    fetchAllWorkflows,
    searchWorkflows,
    getWorkflowById,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    startWorkflow,
    cancelWorkflow,
    fetchWorkflowTemplates,
    getWorkflowTemplateById,
    setSelectedWorkflow,
    clearWorkflowState,
    
    // Enhanced operations
    loadWorkflows,
    loadWorkflowsPage,
    updateFilters,
    selectWorkflowById,
    createWorkflowFromTemplate,
    createNewWorkflow,
    updateExistingWorkflow,
    confirmAndDeleteWorkflow,
    startWorkflowWithConfirmation,
    loadTemplatesIfNeeded
  };
}
