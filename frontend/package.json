{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@radix-ui/react-slot": "^1.0.2", "@supabase/ssr": "^0.0.10", "@supabase/supabase-js": "^2.39.0", "@types/react-beautiful-dnd": "^13.1.4", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "next": "15.3.2", "picocolors": "^1.0.0", "react": "^18.2.0", "@hello-pangea/dnd": "^16.3.0", "react-dom": "^18.2.0", "recharts": "^2.10.3", "tailwind-merge": "^2.1.0"}, "devDependencies": {"@eslint/eslintrc": "^2.1.4", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.16", "cssnano": "^6.0.1", "eslint": "^8.55.0", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lru-cache": "^10.0.1", "postcss": "^8.4.31", "postcss-nested": "^6.0.1", "tailwindcss": "^3.3.5", "typescript": "^5"}}