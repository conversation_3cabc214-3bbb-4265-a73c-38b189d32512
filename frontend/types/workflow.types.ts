import { <PERSON><PERSON> } from './database.types';

// Workflow status types
export type WorkflowStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

// Workflow type
export type WorkflowType = 'text_to_blog' | 'image_processing' | 'video_editing' | 'social_media_blitz' | 'custom';

// Workflow step type
export interface WorkflowStep {
  id: string;
  name: string;
  type: string;
  config: J<PERSON>;
  status: 'pending' | 'running' | 'completed' | 'failed';
  order: number;
  started_at?: string;
  completed_at?: string;
  error?: string;
}

// Workflow item interface
export interface WorkflowItem {
  workflow_id: string;
  user_id: string;
  workflow_type: WorkflowType;
  name: string;
  description?: string;
  steps: WorkflowStep[];
  status: WorkflowStatus;
  metadata?: Json;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error?: string;
}

// Workflow creation input
export interface WorkflowCreateInput {
  workflow_type: WorkflowType;
  name: string;
  description?: string;
  steps: Omit<WorkflowStep, 'status' | 'started_at' | 'completed_at' | 'error'>[];
  metadata?: Json;
}

// Workflow update input
export interface WorkflowUpdateInput {
  name?: string;
  description?: string;
  steps?: WorkflowStep[];
  status?: WorkflowStatus;
  metadata?: Json;
  error?: string;
}

// Workflow filter options
export interface WorkflowFilterOptions {
  workflow_type?: WorkflowType;
  status?: WorkflowStatus;
  search?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: 'created_at' | 'completed_at' | 'name';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// Workflow search result
export interface WorkflowSearchResult {
  items: WorkflowItem[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Workflow template
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  workflow_type: WorkflowType;
  steps: Omit<WorkflowStep, 'status' | 'started_at' | 'completed_at' | 'error'>[];
  metadata?: Json;
}
