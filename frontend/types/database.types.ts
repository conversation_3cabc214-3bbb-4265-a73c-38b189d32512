export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string | null
          role: 'admin' | 'editor' | 'viewer'
          preferences: <PERSON><PERSON> | null
          created_at: string
          last_login: string | null
        }
        Insert: {
          id?: string
          email: string
          name?: string | null
          role?: 'admin' | 'editor' | 'viewer'
          preferences?: Json | null
          created_at?: string
          last_login?: string | null
        }
        Update: {
          id?: string
          email?: string
          name?: string | null
          role?: 'admin' | 'editor' | 'viewer'
          preferences?: Json | null
          created_at?: string
          last_login?: string | null
        }
      }
      inputs: {
        Row: {
          input_id: string
          user_id: string
          input_type: string
          content: string | Json
          metadata: Json | null
          created_at: string
        }
        Insert: {
          input_id?: string
          user_id: string
          input_type: string
          content: string | Json
          metadata?: Json | null
          created_at?: string
        }
        Update: {
          input_id?: string
          user_id?: string
          input_type?: string
          content?: string | Json
          metadata?: Json | null
          created_at?: string
        }
      }
      workflows: {
        Row: {
          workflow_id: string
          user_id: string
          workflow_type: string
          status: string
          created_at: string
          completed_at: string | null
        }
        Insert: {
          workflow_id?: string
          user_id: string
          workflow_type: string
          status?: string
          created_at?: string
          completed_at?: string | null
        }
        Update: {
          workflow_id?: string
          user_id?: string
          workflow_type?: string
          status?: string
          created_at?: string
          completed_at?: string | null
        }
      }
      productions: {
        Row: {
          production_id: string
          workflow_id: string
          content_type: string
          content: string | Json
          metadata: Json | null
          created_at: string
        }
        Insert: {
          production_id?: string
          workflow_id: string
          content_type: string
          content: string | Json
          metadata?: Json | null
          created_at?: string
        }
        Update: {
          production_id?: string
          workflow_id?: string
          content_type?: string
          content?: string | Json
          metadata?: Json | null
          created_at?: string
        }
      }
      publications: {
        Row: {
          publication_id: string
          production_id: string
          platform: string
          status: string
          url: string | null
          published_at: string | null
        }
        Insert: {
          publication_id?: string
          production_id: string
          platform: string
          status?: string
          url?: string | null
          published_at?: string | null
        }
        Update: {
          publication_id?: string
          production_id?: string
          platform?: string
          status?: string
          url?: string | null
          published_at?: string | null
        }
      }
      published_content: {
        Row: {
          id: string
          content_id: string
          user_id: string
          platform: string
          platform_post_id: string | null
          publication_date: string
          status: string
          url: string | null
          metadata: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          content_id: string
          user_id: string
          platform: string
          platform_post_id?: string | null
          publication_date: string
          status?: string
          url?: string | null
          metadata?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          content_id?: string
          user_id?: string
          platform?: string
          platform_post_id?: string | null
          publication_date?: string
          status?: string
          url?: string | null
          metadata?: Json | null
          created_at?: string
        }
      }
      analytics: {
        Row: {
          id: string
          published_content_id: string
          platform: string
          metrics: Json
          date: string
          collected_at: string
          created_at: string
        }
        Insert: {
          id?: string
          published_content_id: string
          platform: string
          metrics: Json
          date: string
          collected_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          published_content_id?: string
          platform?: string
          metrics?: Json
          date?: string
          collected_at?: string
          created_at?: string
        }
      }
      platform_credentials: {
        Row: {
          id: string
          user_id: string
          platform: string
          access_token: string
          refresh_token: string | null
          expires_at: string | null
          status: string
          account_info: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          platform: string
          access_token: string
          refresh_token?: string | null
          expires_at?: string | null
          status?: string
          account_info?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          platform?: string
          access_token?: string
          refresh_token?: string | null
          expires_at?: string | null
          status?: string
          account_info?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
