// User role types
export type UserRole = 'admin' | 'editor' | 'viewer';

// Permission types
export type Permission = 
  // Content permissions
  | 'content:create'
  | 'content:read'
  | 'content:update'
  | 'content:delete'
  // Workflow permissions
  | 'workflow:create'
  | 'workflow:read'
  | 'workflow:update'
  | 'workflow:delete'
  // Publishing permissions
  | 'publishing:create'
  | 'publishing:read'
  | 'publishing:update'
  | 'publishing:delete'
  // Analytics permissions
  | 'analytics:read'
  // User management permissions
  | 'users:create'
  | 'users:read'
  | 'users:update'
  | 'users:delete';

// Role-based permissions mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  admin: [
    // Admin has all permissions
    'content:create', 'content:read', 'content:update', 'content:delete',
    'workflow:create', 'workflow:read', 'workflow:update', 'workflow:delete',
    'publishing:create', 'publishing:read', 'publishing:update', 'publishing:delete',
    'analytics:read',
    'users:create', 'users:read', 'users:update', 'users:delete'
  ],
  editor: [
    // Editor can manage content, workflows, and publishing
    'content:create', 'content:read', 'content:update', 'content:delete',
    'workflow:create', 'workflow:read', 'workflow:update', 'workflow:delete',
    'publishing:create', 'publishing:read', 'publishing:update', 'publishing:delete',
    'analytics:read'
  ],
  viewer: [
    // Viewer can only read content, workflows, publishing, and analytics
    'content:read',
    'workflow:read',
    'publishing:read',
    'analytics:read'
  ]
};

// User profile type
export interface UserProfile {
  id: string;
  email: string;
  name: string | null;
  role: UserRole;
  preferences: any | null;
  created_at: string;
  last_login: string | null;
}
