import { Json } from './database.types';

// Platform types
export type Platform = 'youtube' | 'instagram' | 'linkedin' | 'twitter' | 'facebook' | 'blog' | 'medium';

// Publication status types
export type PublicationStatus = 'draft' | 'scheduled' | 'publishing' | 'published' | 'failed';

// Publication item interface
export interface PublicationItem {
  publication_id: string;
  production_id: string;
  platform: Platform;
  status: PublicationStatus;
  url?: string;
  scheduled_at?: string;
  published_at?: string;
  metadata?: Json;
  error?: string;
}

// Production item interface (transformed content ready for publishing)
export interface ProductionItem {
  production_id: string;
  workflow_id: string;
  content_type: string;
  content: string | Json;
  metadata?: Json;
  created_at: string;
}

// Publication creation input
export interface PublicationCreateInput {
  production_id: string;
  platform: Platform;
  scheduled_at?: string;
  metadata?: Json;
}

// Publication update input
export interface PublicationUpdateInput {
  status?: PublicationStatus;
  url?: string;
  scheduled_at?: string;
  published_at?: string;
  metadata?: Json;
  error?: string;
}

// Publication filter options
export interface PublicationFilterOptions {
  platform?: Platform;
  status?: PublicationStatus;
  startDate?: string;
  endDate?: string;
  sortBy?: 'scheduled_at' | 'published_at' | 'platform';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// Publication search result
export interface PublicationSearchResult {
  items: PublicationItem[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Platform connection status
export interface PlatformConnection {
  platform: Platform;
  connected: boolean;
  username?: string;
  lastConnected?: string;
  error?: string;
}
