import { Json } from './database.types';

// Content input types
export type ContentInputType = 'text' | 'document' | 'image' | 'video' | 'audio' | 'spreadsheet';

// Content status types
export type ContentStatus = 'draft' | 'processing' | 'published' | 'archived' | 'error';

// Content item interface
export interface ContentItem {
  content_id: string;
  user_id: string;
  title: string;
  input_type: ContentInputType;
  content: string | Json;
  description?: string;
  tags?: string[];
  metadata?: Json;
  status: ContentStatus;
  created_at: string;
  updated_at: string;
}

// Content creation input
export interface ContentCreateInput {
  title: string;
  input_type: ContentInputType;
  content: string | Json;
  description?: string;
  tags?: string[];
  metadata?: Json;
}

// Content update input
export interface ContentUpdateInput {
  title?: string;
  description?: string;
  tags?: string[];
  metadata?: Json;
  status?: ContentStatus;
}

// Content filter options
export interface ContentFilterOptions {
  input_type?: ContentInputType;
  status?: ContentStatus;
  tags?: string[];
  search?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: 'created_at' | 'updated_at' | 'title';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// Content search result
export interface ContentSearchResult {
  items: ContentItem[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
