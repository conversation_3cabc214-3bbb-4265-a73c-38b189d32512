"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { useToast } from './ToastContext';

// Types for analytics data
export interface AnalyticsMetrics {
  views?: number;
  likes?: number;
  comments?: number;
  shares?: number;
  impressions?: number;
  reach?: number;
  clicks?: number;
  saves?: number;
  engagementRate?: number;
  clickThroughRate?: number;
  [key: string]: any;
}

export interface AnalyticsData {
  id: string;
  published_content_id: string;
  platform: string;
  metrics: AnalyticsMetrics;
  date: string;
  collected_at: string;
}

export interface PlatformAnalytics {
  platform: string;
  total_publications: number;
  summary: AnalyticsMetrics;
}

export interface AnalyticsOverview {
  platforms: PlatformAnalytics[];
  totalMetrics: AnalyticsMetrics;
  dateRange: {
    start_date: string;
    end_date: string;
  };
}

export interface EngagementTrendData {
  date: string;
  engagement: number;
  views: number;
  likes: number;
  comments: number;
  shares: number;
}

export interface ContentPerformanceData {
  content_id: string;
  title: string;
  platform: string;
  publication_date: string;
  metrics: AnalyticsMetrics;
  url?: string;
}

interface AnalyticsContextType {
  // Data
  analyticsData: AnalyticsOverview | null;
  engagementTrend: EngagementTrendData[];
  contentPerformance: ContentPerformanceData[];
  platformDistribution: PlatformAnalytics[];
  
  // State
  isLoading: boolean;
  error: string | null;
  timeframe: string;
  selectedPlatforms: string[];
  
  // Actions
  fetchAnalytics: (timeframe?: string, platforms?: string[]) => Promise<void>;
  fetchEngagementTrend: (timeframe?: string) => Promise<void>;
  fetchContentPerformance: (limit?: number) => Promise<void>;
  refreshAnalytics: () => Promise<void>;
  setTimeframe: (timeframe: string) => void;
  setSelectedPlatforms: (platforms: string[]) => void;
  
  // Real-time updates
  enableRealTimeUpdates: () => void;
  disableRealTimeUpdates: () => void;
  isRealTimeEnabled: boolean;
  
  // Export functionality
  exportAnalytics: (format: 'csv' | 'json' | 'pdf') => Promise<void>;
}

const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined);

export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const { showToast } = useToast();
  
  // State
  const [analyticsData, setAnalyticsData] = useState<AnalyticsOverview | null>(null);
  const [engagementTrend, setEngagementTrend] = useState<EngagementTrendData[]>([]);
  const [contentPerformance, setContentPerformance] = useState<ContentPerformanceData[]>([]);
  const [platformDistribution, setPlatformDistribution] = useState<PlatformAnalytics[]>([]);
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState('7d');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(['youtube', 'instagram', 'linkedin']);
  
  // Real-time updates
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(false);
  const [websocket, setWebsocket] = useState<WebSocket | null>(null);

  // API base URL
  const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

  // Helper function to make authenticated API calls
  const makeAPICall = useCallback(async (endpoint: string, options: RequestInit = {}) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${API_BASE}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${user.access_token}`,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(errorData.message || `HTTP ${response.status}`);
    }

    return response.json();
  }, [user, API_BASE]);

  // Fetch analytics overview
  const fetchAnalytics = useCallback(async (newTimeframe?: string, platforms?: string[]) => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const currentTimeframe = newTimeframe || timeframe;
      const currentPlatforms = platforms || selectedPlatforms;
      
      // Calculate date range based on timeframe
      const endDate = new Date();
      const startDate = new Date();
      
      switch (currentTimeframe) {
        case '24h':
          startDate.setDate(endDate.getDate() - 1);
          break;
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setDate(endDate.getDate() - 7);
      }

      const params = new URLSearchParams({
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0],
      });

      if (currentPlatforms.length > 0) {
        params.append('platforms', currentPlatforms.join(','));
      }

      const data = await makeAPICall(`/api/analytics/overview?${params}`);
      
      setAnalyticsData({
        platforms: data.platforms || [],
        totalMetrics: data.summary || {},
        dateRange: {
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate.toISOString().split('T')[0],
        },
      });

      setPlatformDistribution(data.platforms || []);

      if (newTimeframe) {
        setTimeframe(newTimeframe);
      }
      if (platforms) {
        setSelectedPlatforms(platforms);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch analytics';
      setError(errorMessage);
      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [user, timeframe, selectedPlatforms, makeAPICall, showToast]);

  // Fetch engagement trend data
  const fetchEngagementTrend = useCallback(async (newTimeframe?: string) => {
    if (!user) return;

    try {
      const currentTimeframe = newTimeframe || timeframe;
      
      const endDate = new Date();
      const startDate = new Date();
      
      switch (currentTimeframe) {
        case '24h':
          startDate.setDate(endDate.getDate() - 1);
          break;
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setDate(endDate.getDate() - 7);
      }

      const params = new URLSearchParams({
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0],
        interval: currentTimeframe === '24h' ? 'hour' : 'day',
      });

      const data = await makeAPICall(`/api/analytics/performance?${params}`);
      
      // Transform the data for the chart
      const trendData = data.timeline?.map((item: any) => ({
        date: item.date,
        engagement: item.metrics?.engagement || (item.metrics?.likes + item.metrics?.comments + item.metrics?.shares) || 0,
        views: item.metrics?.views || item.metrics?.impressions || 0,
        likes: item.metrics?.likes || 0,
        comments: item.metrics?.comments || 0,
        shares: item.metrics?.shares || 0,
      })) || [];

      setEngagementTrend(trendData);

    } catch (err) {
      console.error('Failed to fetch engagement trend:', err);
    }
  }, [user, timeframe, makeAPICall]);

  // Fetch content performance data
  const fetchContentPerformance = useCallback(async (limit = 10) => {
    if (!user) return;

    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        sort: 'engagement',
        order: 'desc',
      });

      const data = await makeAPICall(`/api/analytics/content/top?${params}`);
      
      const performanceData = data.content?.map((item: any) => ({
        content_id: item.content_id,
        title: item.title || 'Untitled',
        platform: item.platform,
        publication_date: item.publication_date,
        metrics: item.metrics || {},
        url: item.url,
      })) || [];

      setContentPerformance(performanceData);

    } catch (err) {
      console.error('Failed to fetch content performance:', err);
    }
  }, [user, makeAPICall]);

  // Refresh all analytics data
  const refreshAnalytics = useCallback(async () => {
    await Promise.all([
      fetchAnalytics(),
      fetchEngagementTrend(),
      fetchContentPerformance(),
    ]);
  }, [fetchAnalytics, fetchEngagementTrend, fetchContentPerformance]);

  // Enable real-time updates via WebSocket
  const enableRealTimeUpdates = useCallback(() => {
    if (!user || isRealTimeEnabled) return;

    try {
      const wsUrl = `${API_BASE.replace('http', 'ws')}/ws/analytics?token=${user.access_token}`;
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('Analytics WebSocket connected');
        setIsRealTimeEnabled(true);
        showToast('Real-time analytics updates enabled', 'success');
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'analytics_update') {
            // Refresh analytics data when updates are received
            refreshAnalytics();
          }
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };

      ws.onclose = () => {
        console.log('Analytics WebSocket disconnected');
        setIsRealTimeEnabled(false);
        setWebsocket(null);
      };

      ws.onerror = (error) => {
        console.error('Analytics WebSocket error:', error);
        setIsRealTimeEnabled(false);
        showToast('Real-time updates connection failed', 'error');
      };

      setWebsocket(ws);

    } catch (err) {
      console.error('Failed to enable real-time updates:', err);
      showToast('Failed to enable real-time updates', 'error');
    }
  }, [user, isRealTimeEnabled, API_BASE, showToast, refreshAnalytics]);

  // Disable real-time updates
  const disableRealTimeUpdates = useCallback(() => {
    if (websocket) {
      websocket.close();
      setWebsocket(null);
    }
    setIsRealTimeEnabled(false);
    showToast('Real-time analytics updates disabled', 'info');
  }, [websocket, showToast]);

  // Export analytics data
  const exportAnalytics = useCallback(async (format: 'csv' | 'json' | 'pdf') => {
    if (!user) return;

    try {
      const params = new URLSearchParams({
        format,
        timeframe,
        platforms: selectedPlatforms.join(','),
      });

      const response = await fetch(`${API_BASE}/api/analytics/export?${params}`, {
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics-${timeframe}-${Date.now()}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      showToast(`Analytics exported as ${format.toUpperCase()}`, 'success');

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Export failed';
      showToast(errorMessage, 'error');
    }
  }, [user, timeframe, selectedPlatforms, API_BASE, showToast]);

  // Load initial data when user changes
  useEffect(() => {
    if (user) {
      refreshAnalytics();
    }
  }, [user, refreshAnalytics]);

  // Cleanup WebSocket on unmount
  useEffect(() => {
    return () => {
      if (websocket) {
        websocket.close();
      }
    };
  }, [websocket]);

  const value: AnalyticsContextType = {
    // Data
    analyticsData,
    engagementTrend,
    contentPerformance,
    platformDistribution,
    
    // State
    isLoading,
    error,
    timeframe,
    selectedPlatforms,
    
    // Actions
    fetchAnalytics,
    fetchEngagementTrend,
    fetchContentPerformance,
    refreshAnalytics,
    setTimeframe,
    setSelectedPlatforms,
    
    // Real-time updates
    enableRealTimeUpdates,
    disableRealTimeUpdates,
    isRealTimeEnabled,
    
    // Export functionality
    exportAnalytics,
  };

  return (
    <AnalyticsContext.Provider value={value}>
      {children}
    </AnalyticsContext.Provider>
  );
}

export function useAnalytics() {
  const context = useContext(AnalyticsContext);
  if (context === undefined) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
}
