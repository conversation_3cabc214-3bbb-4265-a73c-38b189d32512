"use client";

import { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useToast } from './ToastContext';
import { workflowApi } from '../lib/workflowApi';
import { 
  WorkflowItem, 
  WorkflowCreateInput, 
  WorkflowUpdateInput, 
  WorkflowFilterOptions,
  WorkflowSearchResult,
  WorkflowTemplate
} from '../types/workflow.types';

interface WorkflowContextType {
  // Workflow state
  workflows: WorkflowItem[];
  selectedWorkflow: WorkflowItem | null;
  workflowTemplates: WorkflowTemplate[];
  isLoading: boolean;
  error: Error | null;
  searchResult: WorkflowSearchResult | null;
  
  // Workflow operations
  fetchAllWorkflows: (options?: WorkflowFilterOptions) => Promise<void>;
  searchWorkflows: (options: WorkflowFilterOptions) => Promise<void>;
  getWorkflowById: (workflowId: string) => Promise<WorkflowItem | null>;
  createWorkflow: (input: WorkflowCreateInput) => Promise<WorkflowItem | null>;
  updateWorkflow: (workflowId: string, updates: WorkflowUpdateInput) => Promise<WorkflowItem | null>;
  deleteWorkflow: (workflowId: string) => Promise<boolean>;
  startWorkflow: (workflowId: string) => Promise<WorkflowItem | null>;
  cancelWorkflow: (workflowId: string) => Promise<WorkflowItem | null>;
  fetchWorkflowTemplates: () => Promise<void>;
  getWorkflowTemplateById: (templateId: string) => Promise<WorkflowTemplate | null>;
  setSelectedWorkflow: (workflow: WorkflowItem | null) => void;
  clearWorkflowState: () => void;
}

const WorkflowContext = createContext<WorkflowContextType | undefined>(undefined);

export function WorkflowProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const { showToast } = useToast();
  
  // Workflow state
  const [workflows, setWorkflows] = useState<WorkflowItem[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<WorkflowItem | null>(null);
  const [workflowTemplates, setWorkflowTemplates] = useState<WorkflowTemplate[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [searchResult, setSearchResult] = useState<WorkflowSearchResult | null>(null);
  
  // Fetch all workflows for the current user
  const fetchAllWorkflows = useCallback(async (options?: WorkflowFilterOptions) => {
    if (!user) {
      setError(new Error('User not authenticated'));
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await workflowApi.getAllWorkflows(user.id, options);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch workflows: ${error.message}`, 'error');
      } else {
        setWorkflows(data || []);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch workflows: ${errorMessage}`, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [user, showToast]);
  
  // Search workflows with pagination
  const searchWorkflows = useCallback(async (options: WorkflowFilterOptions) => {
    if (!user) {
      setError(new Error('User not authenticated'));
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await workflowApi.searchWorkflows(user.id, options);
      
      if (error) {
        setError(error);
        showToast('Error', `Search failed: ${error.message}`, 'error');
      } else if (data) {
        setSearchResult(data);
        setWorkflows(data.items);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Search failed: ${errorMessage}`, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [user, showToast]);
  
  // Get workflow by ID
  const getWorkflowById = useCallback(async (workflowId: string): Promise<WorkflowItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await workflowApi.getWorkflowById(workflowId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch workflow: ${error.message}`, 'error');
        return null;
      }
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch workflow: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [showToast]);
  
  // Create new workflow
  const createWorkflow = useCallback(async (input: WorkflowCreateInput): Promise<WorkflowItem | null> => {
    if (!user) {
      setError(new Error('User not authenticated'));
      return null;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await workflowApi.createWorkflow(user.id, input);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to create workflow: ${error.message}`, 'error');
        return null;
      }
      
      // Update the workflow list with the new item
      setWorkflows(prev => [data!, ...prev]);
      showToast('Success', 'Workflow created successfully', 'success');
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to create workflow: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [user, showToast]);
  
  // Update workflow
  const updateWorkflow = useCallback(async (workflowId: string, updates: WorkflowUpdateInput): Promise<WorkflowItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await workflowApi.updateWorkflow(workflowId, updates);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to update workflow: ${error.message}`, 'error');
        return null;
      }
      
      // Update the workflow in the list
      setWorkflows(prev => 
        prev.map(item => item.workflow_id === workflowId ? data! : item)
      );
      
      // Update selected workflow if it's the one being updated
      if (selectedWorkflow && selectedWorkflow.workflow_id === workflowId) {
        setSelectedWorkflow(data);
      }
      
      showToast('Success', 'Workflow updated successfully', 'success');
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to update workflow: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [selectedWorkflow, showToast]);
  
  // Delete workflow
  const deleteWorkflow = useCallback(async (workflowId: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { error } = await workflowApi.deleteWorkflow(workflowId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to delete workflow: ${error.message}`, 'error');
        return false;
      }
      
      // Remove the workflow from the list
      setWorkflows(prev => prev.filter(item => item.workflow_id !== workflowId));
      
      // Clear selected workflow if it's the one being deleted
      if (selectedWorkflow && selectedWorkflow.workflow_id === workflowId) {
        setSelectedWorkflow(null);
      }
      
      showToast('Success', 'Workflow deleted successfully', 'success');
      
      return true;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to delete workflow: ${errorMessage}`, 'error');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [selectedWorkflow, showToast]);
  
  // Start workflow execution
  const startWorkflow = useCallback(async (workflowId: string): Promise<WorkflowItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await workflowApi.startWorkflow(workflowId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to start workflow: ${error.message}`, 'error');
        return null;
      }
      
      // Update the workflow in the list
      setWorkflows(prev => 
        prev.map(item => item.workflow_id === workflowId ? data! : item)
      );
      
      // Update selected workflow if it's the one being started
      if (selectedWorkflow && selectedWorkflow.workflow_id === workflowId) {
        setSelectedWorkflow(data);
      }
      
      showToast('Success', 'Workflow started successfully', 'success');
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to start workflow: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [selectedWorkflow, showToast]);
  
  // Cancel workflow execution
  const cancelWorkflow = useCallback(async (workflowId: string): Promise<WorkflowItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await workflowApi.cancelWorkflow(workflowId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to cancel workflow: ${error.message}`, 'error');
        return null;
      }
      
      // Update the workflow in the list
      setWorkflows(prev => 
        prev.map(item => item.workflow_id === workflowId ? data! : item)
      );
      
      // Update selected workflow if it's the one being cancelled
      if (selectedWorkflow && selectedWorkflow.workflow_id === workflowId) {
        setSelectedWorkflow(data);
      }
      
      showToast('Success', 'Workflow cancelled successfully', 'success');
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to cancel workflow: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [selectedWorkflow, showToast]);
  
  // Fetch workflow templates
  const fetchWorkflowTemplates = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await workflowApi.getWorkflowTemplates();
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch workflow templates: ${error.message}`, 'error');
      } else {
        setWorkflowTemplates(data || []);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch workflow templates: ${errorMessage}`, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showToast]);
  
  // Get workflow template by ID
  const getWorkflowTemplateById = useCallback(async (templateId: string): Promise<WorkflowTemplate | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await workflowApi.getWorkflowTemplateById(templateId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch workflow template: ${error.message}`, 'error');
        return null;
      }
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch workflow template: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [showToast]);
  
  // Clear workflow state
  const clearWorkflowState = useCallback(() => {
    setWorkflows([]);
    setSelectedWorkflow(null);
    setError(null);
    setSearchResult(null);
  }, []);
  
  const value = {
    workflows,
    selectedWorkflow,
    workflowTemplates,
    isLoading,
    error,
    searchResult,
    fetchAllWorkflows,
    searchWorkflows,
    getWorkflowById,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    startWorkflow,
    cancelWorkflow,
    fetchWorkflowTemplates,
    getWorkflowTemplateById,
    setSelectedWorkflow,
    clearWorkflowState
  };
  
  return (
    <WorkflowContext.Provider value={value}>
      {children}
    </WorkflowContext.Provider>
  );
}

export function useWorkflow() {
  const context = useContext(WorkflowContext);
  
  if (context === undefined) {
    throw new Error('useWorkflow must be used within a WorkflowProvider');
  }
  
  return context;
}
