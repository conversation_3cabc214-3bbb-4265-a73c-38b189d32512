"use client";

import { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useToast } from './ToastContext';
import { contentApi } from '../lib/contentApi';
import { 
  ContentItem, 
  ContentCreateInput, 
  ContentUpdateInput, 
  ContentFilterOptions,
  ContentSearchResult
} from '../types/content.types';

interface ContentContextType {
  // Content state
  contentItems: ContentItem[];
  selectedContent: ContentItem | null;
  isLoading: boolean;
  error: Error | null;
  searchResult: ContentSearchResult | null;
  
  // Content operations
  fetchAllContent: (options?: ContentFilterOptions) => Promise<void>;
  searchContent: (options: ContentFilterOptions) => Promise<void>;
  getContentById: (contentId: string) => Promise<ContentItem | null>;
  createContent: (input: ContentCreateInput) => Promise<ContentItem | null>;
  updateContent: (contentId: string, updates: ContentUpdateInput) => Promise<ContentItem | null>;
  deleteContent: (contentId: string) => Promise<boolean>;
  archiveContent: (contentId: string) => Promise<ContentItem | null>;
  setSelectedContent: (content: ContentItem | null) => void;
  clearContentState: () => void;
}

const ContentContext = createContext<ContentContextType | undefined>(undefined);

export function ContentProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const { showToast } = useToast();
  
  // Content state
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [selectedContent, setSelectedContent] = useState<ContentItem | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [searchResult, setSearchResult] = useState<ContentSearchResult | null>(null);
  
  // Fetch all content for the current user
  const fetchAllContent = useCallback(async (options?: ContentFilterOptions) => {
    if (!user) {
      setError(new Error('User not authenticated'));
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await contentApi.getAllContent(user.id, options);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch content: ${error.message}`, 'error');
      } else {
        setContentItems(data || []);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch content: ${errorMessage}`, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [user, showToast]);
  
  // Search content with pagination
  const searchContent = useCallback(async (options: ContentFilterOptions) => {
    if (!user) {
      setError(new Error('User not authenticated'));
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await contentApi.searchContent(user.id, options);
      
      if (error) {
        setError(error);
        showToast('Error', `Search failed: ${error.message}`, 'error');
      } else if (data) {
        setSearchResult(data);
        setContentItems(data.items);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Search failed: ${errorMessage}`, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [user, showToast]);
  
  // Get content by ID
  const getContentById = useCallback(async (contentId: string): Promise<ContentItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await contentApi.getContentById(contentId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch content: ${error.message}`, 'error');
        return null;
      }
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch content: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [showToast]);
  
  // Create new content
  const createContent = useCallback(async (input: ContentCreateInput): Promise<ContentItem | null> => {
    if (!user) {
      setError(new Error('User not authenticated'));
      return null;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await contentApi.createContent(user.id, input);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to create content: ${error.message}`, 'error');
        return null;
      }
      
      // Update the content list with the new item
      setContentItems(prev => [data!, ...prev]);
      showToast('Success', 'Content created successfully', 'success');
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to create content: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [user, showToast]);
  
  // Update content
  const updateContent = useCallback(async (contentId: string, updates: ContentUpdateInput): Promise<ContentItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await contentApi.updateContent(contentId, updates);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to update content: ${error.message}`, 'error');
        return null;
      }
      
      // Update the content in the list
      setContentItems(prev => 
        prev.map(item => item.content_id === contentId ? data! : item)
      );
      
      // Update selected content if it's the one being updated
      if (selectedContent && selectedContent.content_id === contentId) {
        setSelectedContent(data);
      }
      
      showToast('Success', 'Content updated successfully', 'success');
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to update content: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [selectedContent, showToast]);
  
  // Delete content
  const deleteContent = useCallback(async (contentId: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { error } = await contentApi.deleteContent(contentId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to delete content: ${error.message}`, 'error');
        return false;
      }
      
      // Remove the content from the list
      setContentItems(prev => prev.filter(item => item.content_id !== contentId));
      
      // Clear selected content if it's the one being deleted
      if (selectedContent && selectedContent.content_id === contentId) {
        setSelectedContent(null);
      }
      
      showToast('Success', 'Content deleted successfully', 'success');
      
      return true;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to delete content: ${errorMessage}`, 'error');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [selectedContent, showToast]);
  
  // Archive content (soft delete)
  const archiveContent = useCallback(async (contentId: string): Promise<ContentItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await contentApi.archiveContent(contentId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to archive content: ${error.message}`, 'error');
        return null;
      }
      
      // Update the content in the list
      setContentItems(prev => 
        prev.map(item => item.content_id === contentId ? data! : item)
      );
      
      // Update selected content if it's the one being archived
      if (selectedContent && selectedContent.content_id === contentId) {
        setSelectedContent(data);
      }
      
      showToast('Success', 'Content archived successfully', 'success');
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to archive content: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [selectedContent, showToast]);
  
  // Clear content state
  const clearContentState = useCallback(() => {
    setContentItems([]);
    setSelectedContent(null);
    setError(null);
    setSearchResult(null);
  }, []);
  
  const value = {
    contentItems,
    selectedContent,
    isLoading,
    error,
    searchResult,
    fetchAllContent,
    searchContent,
    getContentById,
    createContent,
    updateContent,
    deleteContent,
    archiveContent,
    setSelectedContent,
    clearContentState
  };
  
  return (
    <ContentContext.Provider value={value}>
      {children}
    </ContentContext.Provider>
  );
}

export function useContent() {
  const context = useContext(ContentContext);
  
  if (context === undefined) {
    throw new Error('useContent must be used within a ContentProvider');
  }
  
  return context;
}
