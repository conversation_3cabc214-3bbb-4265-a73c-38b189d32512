"use client";

import { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useToast } from './ToastContext';
import { publishingApi } from '../lib/publishingApi';
import { 
  PublicationItem, 
  ProductionItem,
  PublicationCreateInput, 
  PublicationUpdateInput, 
  PublicationFilterOptions,
  PublicationSearchResult,
  PlatformConnection,
  Platform
} from '../types/publishing.types';

interface PublishingContextType {
  // Publishing state
  publications: PublicationItem[];
  selectedPublication: PublicationItem | null;
  productions: ProductionItem[];
  platformConnections: PlatformConnection[];
  isLoading: boolean;
  error: Error | null;
  searchResult: PublicationSearchResult | null;
  
  // Publishing operations
  fetchAllPublications: (options?: PublicationFilterOptions) => Promise<void>;
  searchPublications: (options: PublicationFilterOptions) => Promise<void>;
  getPublicationById: (publicationId: string) => Promise<PublicationItem | null>;
  getPublicationsByProduction: (productionId: string) => Promise<PublicationItem[]>;
  createPublication: (input: PublicationCreateInput) => Promise<PublicationItem | null>;
  updatePublication: (publicationId: string, updates: PublicationUpdateInput) => Promise<PublicationItem | null>;
  deletePublication: (publicationId: string) => Promise<boolean>;
  publishNow: (publicationId: string) => Promise<PublicationItem | null>;
  getProductionById: (productionId: string) => Promise<ProductionItem | null>;
  getProductionsByWorkflow: (workflowId: string) => Promise<ProductionItem[]>;
  fetchPlatformConnections: () => Promise<void>;
  connectToPlatform: (platform: Platform) => Promise<string | null>;
  disconnectFromPlatform: (platform: Platform) => Promise<boolean>;
  setSelectedPublication: (publication: PublicationItem | null) => void;
  clearPublishingState: () => void;
}

const PublishingContext = createContext<PublishingContextType | undefined>(undefined);

export function PublishingProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const { showToast } = useToast();
  
  // Publishing state
  const [publications, setPublications] = useState<PublicationItem[]>([]);
  const [selectedPublication, setSelectedPublication] = useState<PublicationItem | null>(null);
  const [productions, setProductions] = useState<ProductionItem[]>([]);
  const [platformConnections, setPlatformConnections] = useState<PlatformConnection[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [searchResult, setSearchResult] = useState<PublicationSearchResult | null>(null);
  
  // Fetch all publications
  const fetchAllPublications = useCallback(async (options?: PublicationFilterOptions) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await publishingApi.getAllPublications(options);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch publications: ${error.message}`, 'error');
      } else {
        setPublications(data || []);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch publications: ${errorMessage}`, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showToast]);
  
  // Search publications with pagination
  const searchPublications = useCallback(async (options: PublicationFilterOptions) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await publishingApi.searchPublications(options);
      
      if (error) {
        setError(error);
        showToast('Error', `Search failed: ${error.message}`, 'error');
      } else if (data) {
        setSearchResult(data);
        setPublications(data.items);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Search failed: ${errorMessage}`, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showToast]);
  
  // Get publication by ID
  const getPublicationById = useCallback(async (publicationId: string): Promise<PublicationItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await publishingApi.getPublicationById(publicationId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch publication: ${error.message}`, 'error');
        return null;
      }
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch publication: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [showToast]);
  
  // Get publications by production ID
  const getPublicationsByProduction = useCallback(async (productionId: string): Promise<PublicationItem[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await publishingApi.getPublicationsByProduction(productionId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch publications: ${error.message}`, 'error');
        return [];
      }
      
      return data || [];
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch publications: ${errorMessage}`, 'error');
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [showToast]);
  
  // Create new publication
  const createPublication = useCallback(async (input: PublicationCreateInput): Promise<PublicationItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await publishingApi.createPublication(input);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to create publication: ${error.message}`, 'error');
        return null;
      }
      
      // Update the publications list with the new item
      setPublications(prev => [data!, ...prev]);
      showToast('Success', 'Publication created successfully', 'success');
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to create publication: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [showToast]);
  
  // Update publication
  const updatePublication = useCallback(async (publicationId: string, updates: PublicationUpdateInput): Promise<PublicationItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await publishingApi.updatePublication(publicationId, updates);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to update publication: ${error.message}`, 'error');
        return null;
      }
      
      // Update the publication in the list
      setPublications(prev => 
        prev.map(item => item.publication_id === publicationId ? data! : item)
      );
      
      // Update selected publication if it's the one being updated
      if (selectedPublication && selectedPublication.publication_id === publicationId) {
        setSelectedPublication(data);
      }
      
      showToast('Success', 'Publication updated successfully', 'success');
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to update publication: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [selectedPublication, showToast]);
  
  // Delete publication
  const deletePublication = useCallback(async (publicationId: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { error } = await publishingApi.deletePublication(publicationId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to delete publication: ${error.message}`, 'error');
        return false;
      }
      
      // Remove the publication from the list
      setPublications(prev => prev.filter(item => item.publication_id !== publicationId));
      
      // Clear selected publication if it's the one being deleted
      if (selectedPublication && selectedPublication.publication_id === publicationId) {
        setSelectedPublication(null);
      }
      
      showToast('Success', 'Publication deleted successfully', 'success');
      
      return true;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to delete publication: ${errorMessage}`, 'error');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [selectedPublication, showToast]);
  
  // Publish now
  const publishNow = useCallback(async (publicationId: string): Promise<PublicationItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await publishingApi.publishNow(publicationId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to publish: ${error.message}`, 'error');
        return null;
      }
      
      // Update the publication in the list
      setPublications(prev => 
        prev.map(item => item.publication_id === publicationId ? data! : item)
      );
      
      // Update selected publication if it's the one being published
      if (selectedPublication && selectedPublication.publication_id === publicationId) {
        setSelectedPublication(data);
      }
      
      showToast('Success', 'Content is being published', 'success');
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to publish: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [selectedPublication, showToast]);
  
  // Get production by ID
  const getProductionById = useCallback(async (productionId: string): Promise<ProductionItem | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await publishingApi.getProductionById(productionId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch production: ${error.message}`, 'error');
        return null;
      }
      
      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch production: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [showToast]);
  
  // Get productions by workflow ID
  const getProductionsByWorkflow = useCallback(async (workflowId: string): Promise<ProductionItem[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await publishingApi.getProductionsByWorkflow(workflowId);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch productions: ${error.message}`, 'error');
        return [];
      }
      
      setProductions(data || []);
      return data || [];
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch productions: ${errorMessage}`, 'error');
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [showToast]);
  
  // Fetch platform connections
  const fetchPlatformConnections = useCallback(async () => {
    if (!user) {
      setError(new Error('User not authenticated'));
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await publishingApi.getPlatformConnections(user.id);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to fetch platform connections: ${error.message}`, 'error');
      } else {
        setPlatformConnections(data || []);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to fetch platform connections: ${errorMessage}`, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [user, showToast]);
  
  // Connect to a platform
  const connectToPlatform = useCallback(async (platform: Platform): Promise<string | null> => {
    if (!user) {
      setError(new Error('User not authenticated'));
      return null;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await publishingApi.connectToPlatform(user.id, platform);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to connect to ${platform}: ${error.message}`, 'error');
        return null;
      }
      
      return data?.redirectUrl || null;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to connect to ${platform}: ${errorMessage}`, 'error');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [user, showToast]);
  
  // Disconnect from a platform
  const disconnectFromPlatform = useCallback(async (platform: Platform): Promise<boolean> => {
    if (!user) {
      setError(new Error('User not authenticated'));
      return false;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { error } = await publishingApi.disconnectFromPlatform(user.id, platform);
      
      if (error) {
        setError(error);
        showToast('Error', `Failed to disconnect from ${platform}: ${error.message}`, 'error');
        return false;
      }
      
      // Update platform connections list
      setPlatformConnections(prev => prev.filter(conn => conn.platform !== platform));
      showToast('Success', `Disconnected from ${platform}`, 'success');
      
      return true;
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(new Error(errorMessage));
      showToast('Error', `Failed to disconnect from ${platform}: ${errorMessage}`, 'error');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user, showToast]);
  
  // Clear publishing state
  const clearPublishingState = useCallback(() => {
    setPublications([]);
    setSelectedPublication(null);
    setProductions([]);
    setError(null);
    setSearchResult(null);
  }, []);
  
  const value = {
    publications,
    selectedPublication,
    productions,
    platformConnections,
    isLoading,
    error,
    searchResult,
    fetchAllPublications,
    searchPublications,
    getPublicationById,
    getPublicationsByProduction,
    createPublication,
    updatePublication,
    deletePublication,
    publishNow,
    getProductionById,
    getProductionsByWorkflow,
    fetchPlatformConnections,
    connectToPlatform,
    disconnectFromPlatform,
    setSelectedPublication,
    clearPublishingState
  };
  
  return (
    <PublishingContext.Provider value={value}>
      {children}
    </PublishingContext.Provider>
  );
}

export function usePublishing() {
  const context = useContext(PublishingContext);
  
  if (context === undefined) {
    throw new Error('usePublishing must be used within a PublishingProvider');
  }
  
  return context;
}
