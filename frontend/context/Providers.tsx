"use client";
import React, { ReactNode } from 'react';
import { ToastProvider } from './ToastContext';
import { AuthProvider } from './AuthContext';
import { AnalyticsProvider } from './AnalyticsContext';

export function Providers({ children }: { children: ReactNode }) {
  return (
    <AuthProvider>
      <ToastProvider>
        <AnalyticsProvider>
          {children}
        </AnalyticsProvider>
      </ToastProvider>
    </AuthProvider>
  );
}
