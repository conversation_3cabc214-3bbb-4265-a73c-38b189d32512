#!/bin/bash

# ContentForge n8n Quick Start Script
# This script sets up the n8n workflow automation system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
port_available() {
    ! nc -z localhost $1 >/dev/null 2>&1
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Main setup function
main() {
    echo "🚀 ContentForge n8n Quick Start"
    echo "================================"
    echo ""

    # Check prerequisites
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 18.x or higher."
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    print_success "All prerequisites are installed"

    # Check if ports are available
    print_status "Checking port availability..."
    
    if ! port_available 5678; then
        print_warning "Port 5678 is already in use. n8n might already be running."
    fi
    
    if ! port_available 4000; then
        print_warning "Port 4000 is already in use. Backend might already be running."
    fi

    # Check if .env file exists
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from .env.example..."
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success ".env file created from .env.example"
            print_warning "Please update the .env file with your configuration before continuing."
            echo ""
            echo "Required variables to configure:"
            echo "- NEXT_PUBLIC_SUPABASE_URL"
            echo "- NEXT_PUBLIC_SUPABASE_ANON_KEY"
            echo "- SUPABASE_SERVICE_KEY"
            echo "- N8N_BASIC_AUTH_USER (optional, defaults to 'admin')"
            echo "- N8N_BASIC_AUTH_PASSWORD (optional, defaults to 'admin')"
            echo ""
            read -p "Press Enter to continue after updating .env file..."
        else
            print_error ".env.example file not found. Please create .env file manually."
            exit 1
        fi
    fi

    # Start Docker services
    print_status "Starting Docker services..."
    
    if docker-compose up -d; then
        print_success "Docker services started successfully"
    else
        print_error "Failed to start Docker services"
        exit 1
    fi

    # Wait for services to be ready
    wait_for_service "http://localhost:4000/api/health" "Backend API"
    wait_for_service "http://localhost:5678/healthz" "n8n"

    # Install backend dependencies if needed
    if [ ! -d "backend/node_modules" ]; then
        print_status "Installing backend dependencies..."
        cd backend
        npm install
        cd ..
        print_success "Backend dependencies installed"
    fi

    # Run n8n setup script
    print_status "Setting up n8n workflow templates..."
    
    cd backend
    if npm run setup-n8n; then
        print_success "n8n setup completed successfully"
    else
        print_warning "n8n setup encountered some issues, but continuing..."
    fi
    cd ..

    # Install frontend dependencies if needed
    if [ ! -d "frontend/node_modules" ]; then
        print_status "Installing frontend dependencies..."
        cd frontend
        npm install
        cd ..
        print_success "Frontend dependencies installed"
    fi

    # Final status check
    print_status "Performing final health checks..."
    
    # Check backend health
    if curl -s http://localhost:4000/api/health >/dev/null; then
        print_success "Backend API is healthy"
    else
        print_warning "Backend API health check failed"
    fi
    
    # Check n8n health
    if curl -s http://localhost:5678/healthz >/dev/null; then
        print_success "n8n is healthy"
    else
        print_warning "n8n health check failed"
    fi

    # Display summary
    echo ""
    echo "🎉 Setup Complete!"
    echo "=================="
    echo ""
    echo "Services are now running:"
    echo "• Backend API: http://localhost:4000"
    echo "• n8n Interface: http://localhost:5678"
    echo "• Frontend: http://localhost:3000 (if started separately)"
    echo ""
    echo "n8n Login Credentials:"
    echo "• Username: admin (or your configured N8N_BASIC_AUTH_USER)"
    echo "• Password: admin (or your configured N8N_BASIC_AUTH_PASSWORD)"
    echo ""
    echo "Next Steps:"
    echo "1. Access n8n at http://localhost:5678 to review imported workflows"
    echo "2. Start the frontend with: cd frontend && npm run dev"
    echo "3. Access ContentForge at http://localhost:3000"
    echo "4. Test workflow execution by creating content with a workflow"
    echo ""
    echo "For troubleshooting, check:"
    echo "• Docker logs: docker-compose logs"
    echo "• Backend logs: docker-compose logs backend"
    echo "• n8n logs: docker-compose logs n8n"
    echo ""
    echo "Documentation: docs/n8n-workflow-setup.md"
}

# Function to stop services
stop_services() {
    print_status "Stopping ContentForge services..."
    docker-compose down
    print_success "Services stopped"
}

# Function to restart services
restart_services() {
    print_status "Restarting ContentForge services..."
    docker-compose restart
    print_success "Services restarted"
}

# Function to show status
show_status() {
    echo "ContentForge Service Status"
    echo "=========================="
    echo ""
    
    # Docker services status
    echo "Docker Services:"
    docker-compose ps
    echo ""
    
    # Health checks
    echo "Health Checks:"
    
    if curl -s http://localhost:4000/api/health >/dev/null; then
        print_success "Backend API: Healthy"
    else
        print_error "Backend API: Unhealthy"
    fi
    
    if curl -s http://localhost:5678/healthz >/dev/null; then
        print_success "n8n: Healthy"
    else
        print_error "n8n: Unhealthy"
    fi
    
    if curl -s http://localhost:3000 >/dev/null; then
        print_success "Frontend: Running"
    else
        print_warning "Frontend: Not running (start with: cd frontend && npm run dev)"
    fi
}

# Function to show logs
show_logs() {
    local service=${1:-""}
    
    if [ -z "$service" ]; then
        echo "Available services: backend, n8n, frontend"
        echo "Usage: $0 logs [service]"
        echo "Example: $0 logs backend"
        return 1
    fi
    
    docker-compose logs -f "$service"
}

# Parse command line arguments
case "${1:-start}" in
    "start")
        main
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs "$2"
        ;;
    "help"|"-h"|"--help")
        echo "ContentForge n8n Quick Start Script"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  start     Start all services and set up n8n (default)"
        echo "  stop      Stop all services"
        echo "  restart   Restart all services"
        echo "  status    Show service status"
        echo "  logs      Show logs for a specific service"
        echo "  help      Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0                    # Start services"
        echo "  $0 start              # Start services"
        echo "  $0 stop               # Stop services"
        echo "  $0 status             # Check status"
        echo "  $0 logs backend       # Show backend logs"
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
