# n8n Workflow Automation Setup Guide

This guide provides comprehensive instructions for setting up and configuring the n8n workflow automation system in ContentForge.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Environment Configuration](#environment-configuration)
4. [Docker Setup](#docker-setup)
5. [n8n Configuration](#n8n-configuration)
6. [Workflow Templates](#workflow-templates)
7. [API Integration](#api-integration)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)

## Overview

ContentForge integrates with n8n to provide powerful workflow automation capabilities. The system includes:

- **n8n Instance**: Containerized n8n deployment for workflow execution
- **Workflow Templates**: Pre-built templates for common content automation tasks
- **API Integration**: Seamless communication between ContentForge and n8n
- **Execution Monitoring**: Real-time status tracking and error handling
- **Template Management**: Import/export and customization of workflow templates

## Prerequisites

Before setting up the n8n workflow system, ensure you have:

- <PERSON><PERSON> and <PERSON>er Compose installed
- Node.js 18.x or higher
- Access to ContentForge backend and frontend
- Supabase database configured
- Basic understanding of workflow automation concepts

## Environment Configuration

### 1. Update Environment Variables

Add the following variables to your `.env` file:

```bash
# n8n Configuration
N8N_API_URL=http://localhost:5678/api/v1
N8N_API_KEY=your_n8n_api_key
N8N_WEBHOOK_BASE_URL=http://localhost:4000/api/webhooks
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin

# n8n Instance Configuration
N8N_PORT=5678
N8N_PROTOCOL=http
N8N_HOST=localhost
GENERIC_TIMEZONE=Europe/London
N8N_EDITOR_BASE_URL=http://localhost:5678
N8N_USER_MANAGEMENT_DISABLED=false
N8N_DIAGNOSTICS_ENABLED=false
```

### 2. Docker Environment Variables

The Docker Compose configuration automatically maps these variables to the n8n container.

## Docker Setup

### 1. Start the Services

```bash
# Start all services including n8n
docker-compose up -d

# Or start only n8n
docker-compose up -d n8n
```

### 2. Verify n8n is Running

```bash
# Check container status
docker-compose ps

# Check n8n logs
docker-compose logs n8n

# Test n8n accessibility
curl http://localhost:5678/healthz
```

## n8n Configuration

### 1. Access n8n Interface

1. Open your browser and navigate to `http://localhost:5678`
2. Login with the credentials configured in your environment variables
3. Complete the initial setup wizard if prompted

### 2. Configure API Access

1. In n8n, go to Settings → API
2. Generate an API key if not already configured
3. Update your `.env` file with the generated API key

### 3. Set Up Webhooks

n8n will automatically configure webhooks to communicate with ContentForge. The webhook URLs follow this pattern:
```
http://backend:4000/api/webhooks/content/{contentId}/{workflow-type}-result
```

## Workflow Templates

### 1. Available Templates

ContentForge includes the following pre-built workflow templates:

#### Text to Blog Post Workflow (`text-to-blog.json`)
- **Purpose**: Convert text content into SEO-optimized blog posts
- **Features**: Content analysis, blog structure generation, social media snippets
- **Platforms**: Blog platforms, social media

#### Image to Social Media Workflow (`image-to-social.json`)
- **Purpose**: Optimize images for multiple social media platforms
- **Features**: Image analysis, platform-specific optimization, caption generation
- **Platforms**: Instagram, Twitter, Facebook, LinkedIn

#### Video to Multi-Platform Workflow (`video-to-multiplatform.json`)
- **Purpose**: Distribute video content across multiple platforms
- **Features**: Video analysis, platform-specific formatting, scheduling
- **Platforms**: YouTube, Instagram, TikTok, LinkedIn, Twitter

### 2. Import Templates

#### Automatic Import (Recommended)

```bash
# Navigate to backend directory
cd backend

# Run the setup script
npm run setup-n8n
```

#### Manual Import

1. Access n8n interface at `http://localhost:5678`
2. Go to Workflows → Import
3. Upload the JSON files from the `workflows/` directory
4. Configure any required credentials or settings

### 3. Template Structure

Each template includes:
- **Nodes**: Individual workflow steps
- **Connections**: Data flow between nodes
- **Parameters**: Configurable settings
- **Webhooks**: Integration points with ContentForge

## API Integration

### 1. Workflow Execution Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant n8n
    participant Database

    User->>Frontend: Create content with workflow
    Frontend->>Backend: POST /api/workflows/{id}/execute
    Backend->>Database: Create execution record
    Backend->>n8n: Execute workflow
    n8n->>Backend: Webhook callback with results
    Backend->>Database: Update execution status
    Backend->>Frontend: Return execution status
    Frontend->>User: Display results
```

### 2. API Endpoints

#### Workflow Management
- `GET /api/workflows/templates` - Get all workflow templates
- `POST /api/workflows/templates/{id}/clone` - Clone a template
- `POST /api/workflows/templates/{id}/import` - Import template to n8n
- `POST /api/workflows/{id}/execute` - Execute a workflow
- `GET /api/workflows/{id}/status` - Get execution status

#### Webhook Endpoints
- `POST /api/webhooks/content/{id}/blog-result` - Blog workflow results
- `POST /api/webhooks/content/{id}/social-result` - Social media workflow results
- `POST /api/webhooks/content/{id}/multiplatform-result` - Multi-platform workflow results

### 3. Authentication

All API calls require authentication using JWT tokens:

```javascript
const response = await fetch('/api/workflows/templates', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

## Testing

### 1. Test n8n Connectivity

```bash
# Test basic connectivity
curl -X GET http://localhost:5678/api/v1/workflows \
  -H "Authorization: Bearer YOUR_API_KEY"

# Test webhook endpoint
curl -X POST http://localhost:4000/api/webhooks/content/test/blog-result \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"test": "data"}'
```

### 2. Test Workflow Execution

1. Create test content in ContentForge
2. Select a workflow template
3. Execute the workflow
4. Monitor execution status in real-time
5. Verify results are properly stored

### 3. Run Automated Tests

```bash
# Backend tests
cd backend
npm test

# Specific n8n tests
npm test -- --testNamePattern="n8n"
```

## Troubleshooting

### Common Issues

#### 1. n8n Container Won't Start

**Symptoms**: Container exits immediately or fails to start

**Solutions**:
- Check Docker logs: `docker-compose logs n8n`
- Verify environment variables are set correctly
- Ensure port 5678 is not in use by another service
- Check Docker daemon is running

#### 2. API Authentication Errors

**Symptoms**: 401 Unauthorized errors when calling n8n API

**Solutions**:
- Verify N8N_API_KEY is set correctly
- Check n8n API key configuration in the interface
- Ensure basic auth credentials are correct

#### 3. Webhook Callbacks Not Working

**Symptoms**: Workflows execute but results don't appear in ContentForge

**Solutions**:
- Check webhook URLs in workflow templates
- Verify backend webhook routes are accessible
- Check network connectivity between containers
- Review backend logs for webhook errors

#### 4. Template Import Failures

**Symptoms**: Templates fail to import or execute incorrectly

**Solutions**:
- Validate template JSON structure
- Check for missing node types in n8n
- Verify template compatibility with n8n version
- Review template validation errors

### Debug Mode

Enable debug logging by setting environment variables:

```bash
# Backend debug logging
DEBUG=contentforge:*

# n8n debug logging
N8N_LOG_LEVEL=debug
```

### Health Checks

Monitor system health using these endpoints:

```bash
# Backend health
curl http://localhost:4000/api/health

# n8n health
curl http://localhost:5678/healthz

# Database connectivity
curl http://localhost:4000/api/health/database
```

## Advanced Configuration

### 1. Custom Workflow Templates

Create custom templates by:

1. Building workflows in n8n interface
2. Exporting workflow JSON
3. Adding to `workflows/` directory
4. Running setup script to import

### 2. Scaling n8n

For production deployments:

- Use external database for n8n data persistence
- Configure load balancing for multiple n8n instances
- Set up monitoring and alerting
- Implement backup strategies

### 3. Security Considerations

- Use strong API keys and passwords
- Configure HTTPS for production
- Implement rate limiting
- Regular security updates

## Support

For additional support:

1. Check the [n8n documentation](https://docs.n8n.io/)
2. Review ContentForge backend logs
3. Consult the troubleshooting section above
4. Contact the development team

## Next Steps

After successful setup:

1. Explore workflow templates in the ContentForge UI
2. Create custom workflows for your specific needs
3. Monitor workflow execution and performance
4. Optimize templates based on usage patterns
5. Consider advanced features like conditional logic and error handling
