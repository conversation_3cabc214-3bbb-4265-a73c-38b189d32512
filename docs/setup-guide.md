# Developer Setup Guide

This guide will help you set up your development environment for ContentForge.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js**: Version 18.x or higher
- **npm**: Usually comes with Node.js
- **Git**: For version control
- **Docker** and **Docker Compose**: For containerized development
- **Supabase CLI**: For local database development (optional)
- **n8n**: For workflow automation (installed via Docker Compose)

## Initial Setup

### 1. Clone the Repository

```bash
git clone https://github.com/GEMDevEng/ContentForge.git
cd ContentForge
```

### 2. Install Dependencies

Install dependencies for the entire project:

```bash
npm install
```

This will install dependencies for both the frontend and backend thanks to the workspace configuration.

### 3. Environment Variables

Create environment files for both frontend and backend:

Create a `.env` file in the root directory with the following variables:

```
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# n8n Configuration
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin
```

These environment variables will be used by Docker Compose to configure all services.

### 4. Start Development Servers

### Using Docker Compose (Recommended)

Start all services with Docker Compose:
```bash
docker-compose up
```

This will start the frontend, backend, n8n, and mailhog services in development mode.

You can access the services at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:4000
- n8n Workflow Editor: http://localhost:5678
- MailHog (for email testing): http://localhost:8025

### Using npm scripts (Alternative)

If you prefer to run services individually, you can use npm scripts:

Start the frontend development server:
```bash
cd frontend
npm install
npm run dev
```

In a separate terminal, start the backend:
```bash
cd backend
npm install
npm run dev
```

Note: When using npm scripts, you'll need to set up n8n and other services separately.

## Development Workflow

### Code Style and Linting

We use ESLint and Prettier for code quality and formatting. Run linting with:

```bash
npm run lint
```

### Testing

Run tests with:

```bash
npm test
```

### Building for Production

#### Option 1: Using Docker Compose

Build and start the production services:

```bash
docker-compose -f docker-compose.prod.yml up -d
```

This will build optimized containers for production deployment.

#### Option 2: Building Individually

Build the frontend:

```bash
cd frontend
npm run build
```

The backend doesn't require a separate build step, but you can start it in production mode:

```bash
cd backend
NODE_ENV=production npm start
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: If you see "port already in use" errors, check if you have other services running on ports 3000, 4000, 5678, 1025, or 8025.

2. **Node.js version issues**: Make sure you're using Node.js 18.x or higher. You can use nvm to manage multiple Node.js versions.

3. **Docker issues**: If Docker containers fail to start, try:
   ```bash
   docker-compose down -v
   docker-compose up --build
   ```

4. **Module not found errors**: Try deleting node_modules and reinstalling:
   ```bash
   rm -rf node_modules
   npm install
   ```

5. **Environment variable issues**: Make sure all required environment variables are set in your .env file.

6. **Supabase connection issues**: Verify your Supabase URL and keys are correct and that your IP is allowed in Supabase.

7. **n8n workflow issues**: If workflows aren't executing, check the n8n logs and ensure the webhook URLs are correctly configured.

## Getting Help

If you encounter any issues not covered in this guide, please:

1. Check the existing GitHub issues
2. Ask for help in the project's communication channels
3. Create a new issue with detailed information about your problem
