# ContentForge API Documentation

This document provides comprehensive documentation for the ContentForge API.

## Base URL

- **Development**: `http://localhost:4000`
- **Staging**: `https://api-staging.contentforge.example.com`
- **Production**: `https://api.contentforge.example.com`

## Authentication

All API requests require authentication using a JWT token. To authenticate, include the token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

You can obtain a JWT token by authenticating with Supabase using the frontend authentication flow.

## API Endpoints

### Health Check

#### GET /api/health

Check if the API is running.

**Response**:
```json
{
  "status": "healthy"
}
```

### Content Management

#### GET /api/content

Get all content items for the authenticated user.

**Query Parameters**:
- `limit` (optional): Number of items to return (default: 10)
- `offset` (optional): Offset for pagination (default: 0)
- `status` (optional): Filter by status (draft, published, archived)

**Response**:
```json
{
  "data": [
    {
      "id": "uuid",
      "title": "Content Title",
      "description": "Content Description",
      "content_type": "blog",
      "status": "draft",
      "created_at": "2023-05-23T12:34:56Z",
      "updated_at": "2023-05-23T12:34:56Z"
    }
  ],
  "total": 1,
  "limit": 10,
  "offset": 0
}
```

#### GET /api/content/:id

Get a specific content item by ID.

**Response**:
```json
{
  "id": "uuid",
  "title": "Content Title",
  "description": "Content Description",
  "content_type": "blog",
  "status": "draft",
  "metadata": {},
  "created_at": "2023-05-23T12:34:56Z",
  "updated_at": "2023-05-23T12:34:56Z"
}
```

#### POST /api/content

Create a new content item.

**Request Body**:
```json
{
  "title": "Content Title",
  "description": "Content Description",
  "content_type": "blog",
  "metadata": {}
}
```

**Response**:
```json
{
  "id": "uuid",
  "title": "Content Title",
  "description": "Content Description",
  "content_type": "blog",
  "status": "draft",
  "metadata": {},
  "created_at": "2023-05-23T12:34:56Z",
  "updated_at": "2023-05-23T12:34:56Z"
}
```

#### PUT /api/content/:id

Update an existing content item.

**Request Body**:
```json
{
  "title": "Updated Title",
  "description": "Updated Description",
  "metadata": {}
}
```

**Response**:
```json
{
  "id": "uuid",
  "title": "Updated Title",
  "description": "Updated Description",
  "content_type": "blog",
  "status": "draft",
  "metadata": {},
  "created_at": "2023-05-23T12:34:56Z",
  "updated_at": "2023-05-23T12:34:56Z"
}
```

#### DELETE /api/content/:id

Delete a content item.

**Response**:
```json
{
  "message": "Content deleted successfully"
}
```

### Workflows

#### GET /api/workflows

Get all available workflow templates.

**Response**:
```json
{
  "data": [
    {
      "id": "uuid",
      "name": "Text to Video",
      "description": "Convert text content to video format",
      "is_active": true,
      "created_at": "2023-05-23T12:34:56Z",
      "updated_at": "2023-05-23T12:34:56Z"
    }
  ]
}
```

#### POST /api/content/:id/workflow

Apply a workflow to a content item.

**Request Body**:
```json
{
  "workflow_id": "uuid"
}
```

**Response**:
```json
{
  "id": "uuid",
  "content_id": "uuid",
  "workflow_id": "uuid",
  "status": "pending",
  "started_at": "2023-05-23T12:34:56Z",
  "created_at": "2023-05-23T12:34:56Z",
  "updated_at": "2023-05-23T12:34:56Z"
}
```

### Platforms

#### GET /api/platforms

Get all available publishing platforms.

**Response**:
```json
{
  "data": [
    {
      "id": "uuid",
      "name": "YouTube",
      "description": "Video sharing platform",
      "icon": "youtube",
      "created_at": "2023-05-23T12:34:56Z",
      "updated_at": "2023-05-23T12:34:56Z"
    }
  ]
}
```

#### POST /api/content/:id/publish

Publish content to a platform.

**Request Body**:
```json
{
  "platform_id": "uuid",
  "platform_data": {}
}
```

**Response**:
```json
{
  "id": "uuid",
  "content_id": "uuid",
  "platform_id": "uuid",
  "status": "pending",
  "platform_data": {},
  "created_at": "2023-05-23T12:34:56Z",
  "updated_at": "2023-05-23T12:34:56Z"
}
```

### Analytics

#### GET /api/content/:id/analytics

Get analytics for a specific content item.

**Response**:
```json
{
  "data": [
    {
      "platform_id": "uuid",
      "platform_name": "YouTube",
      "views": 1000,
      "likes": 50,
      "shares": 10,
      "comments": 5,
      "last_updated": "2023-05-23T12:34:56Z"
    }
  ]
}
```

## Error Handling

The API returns standard HTTP status codes to indicate the success or failure of a request.

### Error Response Format

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Error message",
    "details": {}
  }
}
```

### Common Error Codes

- `400`: Bad Request - The request was malformed or missing required parameters
- `401`: Unauthorized - Authentication is required or failed
- `403`: Forbidden - The authenticated user doesn't have permission to access the resource
- `404`: Not Found - The requested resource doesn't exist
- `409`: Conflict - The request conflicts with the current state of the resource
- `422`: Unprocessable Entity - The request was well-formed but contains semantic errors
- `500`: Internal Server Error - Something went wrong on the server

## Rate Limiting

The API implements rate limiting to prevent abuse. The current limits are:

- 100 requests per minute per IP address
- 1000 requests per hour per user

When a rate limit is exceeded, the API returns a `429 Too Many Requests` response with a `Retry-After` header indicating how many seconds to wait before making another request.
