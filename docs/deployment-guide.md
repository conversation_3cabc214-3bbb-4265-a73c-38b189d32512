# ContentForge Deployment Guide

This guide provides instructions for deploying ContentForge to staging and production environments.

## Prerequisites

Before deploying ContentForge, ensure you have the following:

- A server with <PERSON><PERSON> and <PERSON>er Compose installed
- Domain names configured for your environments
- SSL certificates for your domains
- Supabase project set up with proper RLS policies
- n8n account or self-hosted instance
- Access to Docker Hub or another container registry
- GitHub repository access

## Environment Setup

### 1. Server Preparation

Prepare your server with the following steps:

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.18.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create application directory
sudo mkdir -p /opt/contentforge
sudo chown $(whoami):$(whoami) /opt/contentforge
```

### 2. Environment Configuration

1. Clone the repository:
   ```bash
   cd /opt/contentforge
   git clone https://github.com/GEMDevEng/ContentForge.git .
   ```

2. Create environment files:
   ```bash
   # Create a production environment file
   touch .env
   ```

3. Edit the environment file with your specific configuration:
   ```bash
   nano .env
   ```

   Add the following environment variables:
   ```
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_KEY=your_supabase_service_key

   # n8n Configuration
   N8N_BASIC_AUTH_USER=your_n8n_username
   N8N_BASIC_AUTH_PASSWORD=your_n8n_password
   N8N_WEBHOOK_URL=https://your-domain.com/n8n/
   N8N_URL=https://your-domain.com/n8n

   # Email Configuration
   SMTP_HOST=your_smtp_host
   SMTP_PORT=your_smtp_port
   SMTP_USER=your_smtp_username
   SMTP_PASSWORD=your_smtp_password
   SMTP_FROM=<EMAIL>
   ```

### 3. SSL Certificate Setup

1. Create SSL certificate directory:
   ```bash
   mkdir -p nginx/ssl
   ```

2. Copy your SSL certificates to the directory:
   ```bash
   cp /path/to/your/certificate.crt nginx/ssl/contentforge.crt
   cp /path/to/your/private.key nginx/ssl/contentforge.key
   ```

3. Update Nginx configuration if needed:
   ```bash
   nano nginx/conf.d/default.conf
   ```

## Deployment

### Production Deployment

To deploy to the production environment:

1. Build and start the services:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d --build
   ```

2. Verify the deployment:
   ```bash
   docker-compose -f docker-compose.prod.yml ps
   ```

3. Check the logs to ensure everything is running correctly:
   ```bash
   docker-compose -f docker-compose.prod.yml logs
   ```

## Database Migration

To run database migrations:

1. Connect to the Supabase project:
   ```bash
   # Install Supabase CLI if not already installed
   npm install -g supabase

   # Login to Supabase
   supabase login

   # Link to your project
   supabase link --project-ref your-project-ref
   ```

2. Apply migrations:
   ```bash
   # Apply migrations to your production project
   cat database/migrations/*.sql | supabase db execute --project-ref your-project-ref
   ```

3. Verify the database schema:
   ```bash
   # List tables to verify the schema
   supabase db tables list --project-ref your-project-ref
   ```

## CI/CD Setup

ContentForge uses GitHub Actions for CI/CD. To set up the CI/CD pipeline:

1. Add the following secrets to your GitHub repository:
   - `DOCKER_HUB_USERNAME`: Your Docker Hub username
   - `DOCKER_HUB_TOKEN`: Your Docker Hub access token

   For production:
   - `PRODUCTION_HOST`: Your production server hostname
   - `PRODUCTION_USERNAME`: SSH username for production server
   - `PRODUCTION_SSH_KEY`: SSH private key for production server
   - `PRODUCTION_SUPABASE_URL`: Production Supabase URL
   - `PRODUCTION_SUPABASE_ANON_KEY`: Production Supabase anonymous key
   - `PRODUCTION_SUPABASE_SERVICE_KEY`: Production Supabase service key
   - `PRODUCTION_API_URL`: Production API URL
   - `PRODUCTION_N8N_WEBHOOK_URL`: Production n8n webhook URL
   - `PRODUCTION_N8N_URL`: Production n8n URL
   - `PRODUCTION_SMTP_HOST`: Production SMTP host
   - `PRODUCTION_SMTP_PORT`: Production SMTP port
   - `PRODUCTION_SMTP_USER`: Production SMTP username
   - `PRODUCTION_SMTP_PASSWORD`: Production SMTP password
   - `PRODUCTION_SMTP_FROM`: Production email sender address

2. Push to the main branch to trigger production deployment:
   - Push to `main` branch for production deployment

## Monitoring and Maintenance

### Logs

View logs for troubleshooting:

```bash
# View logs for all services
docker-compose -f docker-compose.prod.yml logs

# View logs for a specific service
docker-compose -f docker-compose.prod.yml logs frontend
docker-compose -f docker-compose.prod.yml logs backend
docker-compose -f docker-compose.prod.yml logs n8n
```

### Updates

To update the deployment:

```bash
# Pull the latest code
git pull

# Pull the latest Docker images
docker-compose -f docker-compose.prod.yml pull

# Restart the services
docker-compose -f docker-compose.prod.yml up -d
```

### Backup

Backup your data regularly:

```bash
# Backup n8n data
docker run --rm -v workspace_n8n_data:/data -v $(pwd)/backups:/backups alpine tar -czf /backups/n8n_data_$(date +%Y%m%d).tar.gz -C /data .

# Backup workflow files
tar -czf backups/workflows_$(date +%Y%m%d).tar.gz workflows/
```

## Rollback

If you need to rollback to a previous version:

```bash
# Check out a specific git tag or commit
git checkout v1.0.0

# Rebuild and restart the services
docker-compose -f docker-compose.prod.yml up -d --build
```

## Troubleshooting

### Common Issues

1. **Services not starting**: Check Docker logs for errors
   ```bash
   docker-compose -f docker-compose.prod.yml logs
   ```

2. **Database connection issues**: Verify environment variables and network connectivity
   ```bash
   docker-compose -f docker-compose.prod.yml exec backend env | grep SUPABASE
   ```

3. **Nginx configuration errors**: Check Nginx logs
   ```bash
   docker-compose -f docker-compose.prod.yml logs nginx
   ```

4. **SSL certificate issues**: Verify certificate paths and permissions
   ```bash
   ls -la nginx/ssl/
   ```

### Support

If you encounter issues that you cannot resolve, contact our support team:

- GitHub Issues: https://github.com/GEMDevEng/ContentForge/issues
