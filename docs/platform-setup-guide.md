# Platform API Setup Guide

This guide walks you through setting up the Platform API Integrations for ContentForge, including OAuth credentials for YouTube, Instagram, and LinkedIn.

## Prerequisites

- Node.js 18+ installed
- Supabase project set up
- Access to Google Cloud Console, Facebook Developer Console, and LinkedIn Developer Portal

## 1. Database Setup

### Run Database Migrations

1. Apply the initial schema:
```sql
-- Run the contents of database/migrations/001_initial_schema.sql
```

2. Apply the OAuth tokens migration:
```sql
-- Run the contents of database/migrations/002_oauth_tokens.sql
```

### Verify Tables

Ensure these tables exist in your Supabase database:
- `oauth_tokens`
- `scheduled_publications`
- `published_content`
- `platforms`
- `content`
- `profiles`

## 2. YouTube API Setup

### Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the YouTube Data API v3:
   - Go to "APIs & Services" > "Library"
   - Search for "YouTube Data API v3"
   - Click "Enable"

### Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Configure the consent screen if prompted
4. Choose "Web application" as application type
5. Add authorized redirect URIs:
   - `http://localhost:3000/oauth/callback/youtube` (development)
   - `https://yourdomain.com/oauth/callback/youtube` (production)
6. Save and note the Client ID and Client Secret

### Environment Variables

Add to your `.env` file:
```env
YOUTUBE_CLIENT_ID=your_youtube_client_id
YOUTUBE_CLIENT_SECRET=your_youtube_client_secret
YOUTUBE_REDIRECT_URI=http://localhost:3000/oauth/callback/youtube
```

## 3. Instagram API Setup

### Create Facebook App

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click "Create App" > "Business" type
3. Fill in app details and create

### Configure Instagram Basic Display

1. In your app dashboard, add "Instagram Basic Display" product
2. Go to Instagram Basic Display > Basic Display
3. Create a new Instagram App ID
4. Add OAuth Redirect URIs:
   - `http://localhost:3000/oauth/callback/instagram` (development)
   - `https://yourdomain.com/oauth/callback/instagram` (production)

### Add Test Users (Development)

1. Go to Instagram Basic Display > Roles > Roles
2. Add Instagram Testers
3. Test users must accept the invitation in their Instagram app

### Environment Variables

Add to your `.env` file:
```env
INSTAGRAM_CLIENT_ID=your_instagram_client_id
INSTAGRAM_CLIENT_SECRET=your_instagram_client_secret
INSTAGRAM_REDIRECT_URI=http://localhost:3000/oauth/callback/instagram
```

## 4. LinkedIn API Setup

### Create LinkedIn App

1. Go to [LinkedIn Developers](https://www.linkedin.com/developers/)
2. Click "Create App"
3. Fill in required information:
   - App name
   - LinkedIn Page (create a company page if needed)
   - App logo
   - Legal agreement acceptance

### Configure OAuth Settings

1. In your app settings, go to "Auth" tab
2. Add OAuth 2.0 redirect URLs:
   - `http://localhost:3000/oauth/callback/linkedin` (development)
   - `https://yourdomain.com/oauth/callback/linkedin` (production)

### Request API Access

1. Go to "Products" tab
2. Request access to:
   - "Share on LinkedIn" (for posting)
   - "Sign In with LinkedIn" (for authentication)
3. Wait for approval (usually instant for basic features)

### Environment Variables

Add to your `.env` file:
```env
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
LINKEDIN_REDIRECT_URI=http://localhost:3000/oauth/callback/linkedin
```

## 5. Complete Environment Configuration

Your final `.env` file should include:

```env
# Server Configuration
PORT=4000
NODE_ENV=development

# Database Configuration
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# YouTube API Configuration
YOUTUBE_CLIENT_ID=your_youtube_client_id
YOUTUBE_CLIENT_SECRET=your_youtube_client_secret
YOUTUBE_REDIRECT_URI=http://localhost:3000/oauth/callback/youtube

# Instagram API Configuration
INSTAGRAM_CLIENT_ID=your_instagram_client_id
INSTAGRAM_CLIENT_SECRET=your_instagram_client_secret
INSTAGRAM_REDIRECT_URI=http://localhost:3000/oauth/callback/instagram

# LinkedIn API Configuration
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
LINKEDIN_REDIRECT_URI=http://localhost:3000/oauth/callback/linkedin

# N8N Configuration (for workflows)
N8N_URL=http://localhost:5678
N8N_API_KEY=your_n8n_api_key

# JWT Configuration (if using custom JWT)
JWT_SECRET=your_jwt_secret

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# File Upload Configuration
MAX_FILE_SIZE=100MB
UPLOAD_PATH=./uploads
```

## 6. Install Dependencies

```bash
cd backend
npm install
```

The following packages are required and should be installed:
- `googleapis` - YouTube API client
- `axios` - HTTP client for Instagram and LinkedIn APIs
- `form-data` - For file uploads

## 7. Start the Application

```bash
# Development mode
npm run dev

# Production mode
npm start
```

The server will start on port 4000 and the scheduling service will automatically begin running.

## 8. Testing the Integration

### Test OAuth Flow

1. Start your frontend application
2. Navigate to the platform connection page
3. Click "Connect" for each platform
4. Complete the OAuth flow
5. Verify the connection appears in your dashboard

### Test Publishing

1. Create or upload content
2. Select platforms to publish to
3. Click "Publish Now" or "Schedule"
4. Check the publication status

### Test API Endpoints

Use a tool like Postman or curl to test the API endpoints:

```bash
# Get available platforms
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:4000/api/publish/platforms

# Get OAuth auth URL
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:4000/api/oauth/auth/youtube

# Get scheduled publications
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:4000/api/publish/schedule
```

## 9. Troubleshooting

### Common Issues

#### OAuth Redirect URI Mismatch
- Ensure redirect URIs in platform settings match your environment variables exactly
- Check for trailing slashes and protocol (http vs https)

#### API Access Denied
- Verify your app has the necessary permissions/products enabled
- Check if you need to submit for review (Instagram/LinkedIn)
- Ensure test users are added for development

#### Token Refresh Failures
- Check that refresh tokens are being stored correctly
- Verify token refresh logic is working
- Monitor token expiration times

#### Publishing Failures
- Verify file formats are supported by the target platform
- Check file size limits
- Ensure content meets platform guidelines

### Debug Mode

Enable detailed logging by setting:
```env
NODE_ENV=development
```

This will provide verbose logs for troubleshooting.

### Database Issues

Check Supabase logs and ensure:
- RLS policies are correctly configured
- Tables have proper indexes
- Connection limits are not exceeded

## 10. Production Deployment

### Environment Variables

Update redirect URIs for production:
```env
YOUTUBE_REDIRECT_URI=https://yourdomain.com/oauth/callback/youtube
INSTAGRAM_REDIRECT_URI=https://yourdomain.com/oauth/callback/instagram
LINKEDIN_REDIRECT_URI=https://yourdomain.com/oauth/callback/linkedin
```

### Platform App Settings

Update OAuth redirect URIs in each platform's developer console to use your production domain.

### Security Considerations

1. Use HTTPS in production
2. Implement rate limiting
3. Monitor API usage and costs
4. Set up proper error tracking
5. Configure backup and recovery

### Monitoring

Set up monitoring for:
- API response times
- Error rates
- Token refresh success rates
- Publishing success rates
- Database performance

## 11. API Rate Limits

Be aware of platform rate limits:

### YouTube
- 10,000 units per day (default)
- Video uploads: 6 uploads per day for unverified channels

### Instagram
- 200 requests per hour per user
- 4800 requests per hour per app

### LinkedIn
- 500 requests per day per member
- 100,000 requests per day per app

Implement proper rate limiting and retry logic to handle these constraints.

## 12. Support and Resources

### Documentation
- [YouTube Data API](https://developers.google.com/youtube/v3)
- [Instagram Basic Display API](https://developers.facebook.com/docs/instagram-basic-display-api)
- [LinkedIn API](https://docs.microsoft.com/en-us/linkedin/)

### Community
- Stack Overflow tags: `youtube-api`, `instagram-api`, `linkedin-api`
- Platform-specific developer communities

### ContentForge Support
- Check the troubleshooting section in the main documentation
- Review the API documentation for detailed endpoint information
- Submit issues on the project repository
