# Platform API Integrations

This document describes the Platform API Integrations implementation for ContentForge, providing unified publishing capabilities across YouTube, Instagram, and LinkedIn.

## Overview

The Platform API Integrations system provides:

- **OAuth 2.0 Authentication** for YouTube, Instagram, and LinkedIn
- **Unified Publishing Interface** for cross-platform content distribution
- **Scheduling System** for automated content publishing
- **Analytics Integration** for performance tracking
- **Platform-Specific Content Formatting** for optimal presentation

## Architecture

### Core Components

1. **OAuth Controller** (`backend/controllers/oauthController.js`)
   - Handles OAuth flows for all platforms
   - Manages token storage and refresh
   - Provides platform connection status

2. **Platform Manager** (`backend/services/platformManager.js`)
   - Unified interface for all platform services
   - Handles multi-platform publishing
   - Manages platform-specific content formatting

3. **Scheduling Service** (`backend/services/schedulingService.js`)
   - Processes scheduled publications
   - Handles retry logic for failed publications
   - Provides scheduling management

4. **Platform Services** (`backend/services/platforms/`)
   - YouTube Service: Video uploads, metadata management
   - Instagram Service: Image/video posts, Stories, carousels
   - LinkedIn Service: Text, image, video, document, and article posts

### Database Schema

#### OAuth Tokens Table
```sql
CREATE TABLE oauth_tokens (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  platform TEXT NOT NULL,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  expires_at TIMESTAMP,
  platform_user_id TEXT,
  platform_username TEXT,
  is_active BOOLEAN DEFAULT true
);
```

#### Scheduled Publications Table
```sql
CREATE TABLE scheduled_publications (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  content_id UUID REFERENCES content(id),
  platform TEXT NOT NULL,
  scheduled_time TIMESTAMP NOT NULL,
  status TEXT DEFAULT 'scheduled',
  published_url TEXT,
  platform_post_id TEXT,
  retry_count INTEGER DEFAULT 0
);
```

#### Published Content Table
```sql
CREATE TABLE published_content (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  content_id UUID REFERENCES content(id),
  platform TEXT NOT NULL,
  platform_post_id TEXT NOT NULL,
  published_url TEXT,
  platform_data JSONB
);
```

## API Endpoints

### OAuth Authentication

#### Get Authorization URL
```http
GET /api/oauth/auth/:platform
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "authUrl": "https://platform.com/oauth/authorize?...",
  "state": "user_id:platform:timestamp",
  "platform": "youtube"
}
```

#### Handle OAuth Callback
```http
GET /api/oauth/callback/:platform?code=...&state=...
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully connected to YouTube",
  "platform": "youtube",
  "user_info": {
    "platform_user_id": "channel_id",
    "platform_username": "Channel Name"
  }
}
```

#### Get Connected Platforms
```http
GET /api/oauth/connected
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "connected_platforms": [
    {
      "platform": "youtube",
      "platform_user_id": "channel_id",
      "platform_username": "Channel Name",
      "connected_at": "2024-01-01T00:00:00Z",
      "is_expired": false
    }
  ]
}
```

#### Disconnect Platform
```http
DELETE /api/oauth/disconnect/:platform
Authorization: Bearer <token>
```

#### Refresh Tokens
```http
POST /api/oauth/refresh/:platform
Authorization: Bearer <token>
```

### Publishing

#### Get Available Platforms
```http
GET /api/publish/platforms
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "platforms": [
    {
      "platform": "youtube",
      "username": "Channel Name",
      "is_expired": false,
      "capabilities": {
        "supportedContentTypes": ["video/mp4", "video/webm"],
        "supportedActions": ["publish", "update", "delete", "analytics"]
      }
    }
  ],
  "count": 1
}
```

#### Publish Content
```http
POST /api/publish
Authorization: Bearer <token>
Content-Type: application/json

{
  "content_id": "uuid",
  "platforms": ["youtube", "instagram"],
  "options": {
    "youtube": {
      "privacy": "private",
      "categoryId": "22"
    },
    "instagram": {
      "caption": "Custom caption"
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "results": {
    "youtube": {
      "success": true,
      "videoId": "video_id",
      "videoUrl": "https://youtube.com/watch?v=video_id"
    }
  },
  "errors": {
    "instagram": {
      "success": false,
      "error": "Invalid file format"
    }
  },
  "published_platforms": ["youtube"],
  "failed_platforms": ["instagram"]
}
```

#### Schedule Publication
```http
POST /api/publish/schedule
Authorization: Bearer <token>
Content-Type: application/json

{
  "content_id": "uuid",
  "platforms": ["youtube", "linkedin"],
  "schedule_time": "2024-12-25T10:00:00Z",
  "options": {
    "maxRetries": 3
  }
}
```

#### Get Scheduled Publications
```http
GET /api/publish/schedule?platform=youtube&status=scheduled&limit=10
Authorization: Bearer <token>
```

#### Cancel Scheduled Publication
```http
DELETE /api/publish/schedule/:id
Authorization: Bearer <token>
```

## Platform-Specific Features

### YouTube Integration

**Supported Content Types:**
- `video/mp4`
- `video/webm`
- `video/quicktime`

**Features:**
- Video upload with metadata (title, description, tags)
- Thumbnail upload
- Privacy settings (private, public, unlisted)
- Scheduled publishing
- Video analytics
- Video management (update, delete)

**Configuration Required:**
```env
YOUTUBE_CLIENT_ID=your_client_id
YOUTUBE_CLIENT_SECRET=your_client_secret
YOUTUBE_REDIRECT_URI=http://localhost:3000/oauth/callback/youtube
```

### Instagram Integration

**Supported Content Types:**
- `image/jpeg`
- `image/png`
- `video/mp4`
- `image/carousel` (multiple images)
- `video/carousel` (mixed media)

**Features:**
- Image and video posts
- Carousel posts (multiple media items)
- Caption management
- Hashtag support
- Post analytics
- Content deletion

**Configuration Required:**
```env
INSTAGRAM_CLIENT_ID=your_client_id
INSTAGRAM_CLIENT_SECRET=your_client_secret
INSTAGRAM_REDIRECT_URI=http://localhost:3000/oauth/callback/instagram
```

### LinkedIn Integration

**Supported Content Types:**
- `text/plain`
- `image/jpeg`
- `image/png`
- `video/mp4`
- `application/pdf`
- `article` (LinkedIn articles)

**Features:**
- Text posts
- Image and video posts
- Document sharing
- Article publishing
- Professional and organization posting
- Post analytics
- Visibility settings

**Configuration Required:**
```env
LINKEDIN_CLIENT_ID=your_client_id
LINKEDIN_CLIENT_SECRET=your_client_secret
LINKEDIN_REDIRECT_URI=http://localhost:3000/oauth/callback/linkedin
```

## Content Formatting

The system automatically formats content for each platform:

### YouTube
```javascript
{
  title: content.title,
  description: content.description,
  tags: content.metadata?.tags || [],
  thumbnailPath: content.metadata?.thumbnail_path,
  privacy: content.metadata?.privacy || 'private'
}
```

### Instagram
```javascript
{
  caption: content.description || content.title,
  fileUrls: content.metadata?.file_urls || [content.file_url]
}
```

### LinkedIn
```javascript
{
  text: content.description || content.title,
  visibility: content.metadata?.visibility || 'PUBLIC'
}
```

## Scheduling System

The scheduling service runs continuously and:

1. **Checks for due publications** every minute
2. **Processes up to 10 publications** per cycle
3. **Implements retry logic** with exponential backoff
4. **Updates publication status** in real-time
5. **Handles failures gracefully** with error logging

### Scheduling States

- `scheduled`: Waiting to be published
- `publishing`: Currently being processed
- `published`: Successfully published
- `failed`: Failed after max retries
- `cancelled`: Manually cancelled

## Error Handling

The system provides comprehensive error handling:

### OAuth Errors
- Invalid credentials
- Expired tokens
- Platform API errors
- Network connectivity issues

### Publishing Errors
- Unsupported content types
- File format issues
- Platform-specific restrictions
- API rate limits

### Scheduling Errors
- Invalid schedule times
- Missing platform connections
- Content not found
- Database errors

## Security Considerations

1. **Token Encryption**: OAuth tokens are stored securely in the database
2. **State Validation**: OAuth state parameters prevent CSRF attacks
3. **User Isolation**: RLS policies ensure users can only access their own data
4. **Token Refresh**: Automatic token refresh prevents expired credentials
5. **Error Sanitization**: Sensitive information is not exposed in error messages

## Performance Optimization

1. **Batch Processing**: Multiple platforms can be published to simultaneously
2. **Async Operations**: Non-blocking operations for better performance
3. **Connection Pooling**: Efficient database connection management
4. **Caching**: Platform capabilities and user info are cached
5. **Rate Limiting**: Respects platform API rate limits

## Monitoring and Analytics

The system provides:

1. **Publication Success Rates** per platform
2. **Error Tracking** with detailed logs
3. **Performance Metrics** for publishing operations
4. **User Engagement** analytics from platforms
5. **System Health** monitoring

## Future Enhancements

Planned improvements include:

1. **Additional Platforms**: Twitter, TikTok, Facebook
2. **Advanced Scheduling**: Recurring publications, optimal timing
3. **Content Optimization**: AI-powered platform-specific optimization
4. **Bulk Operations**: Mass publishing and scheduling
5. **Advanced Analytics**: Cross-platform performance comparison

## Troubleshooting

### Common Issues

1. **OAuth Connection Failed**
   - Check API credentials in environment variables
   - Verify redirect URIs match platform settings
   - Ensure platform APIs are enabled

2. **Publishing Failed**
   - Verify content format is supported by platform
   - Check file size limits
   - Ensure user has necessary permissions

3. **Scheduling Not Working**
   - Verify scheduling service is running
   - Check database connectivity
   - Review scheduled publication status

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This provides detailed logs for troubleshooting platform integrations.
